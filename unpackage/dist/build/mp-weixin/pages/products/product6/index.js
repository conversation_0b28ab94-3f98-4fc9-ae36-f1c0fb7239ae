(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/products/product6/index"],{"5e80":function(n,t,e){"use strict";e.d(t,"b",(function(){return c})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){}));var c=function(){var n=this.$createElement;this._self._c},a=[]},"9b8b":function(n,t,e){"use strict";(function(n,t){var c=e("47a9");e("9c86");c(e("3240"));var a=c(e("b341"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},b341:function(n,t,e){"use strict";e.r(t);var c=e("5e80"),a=e("5abe");for(var u in a)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(u);e("d73a");var r=e("828b"),o=Object(r["a"])(a["default"],c["b"],c["c"],!1,null,"cd5023ba",null,!1,c["a"],void 0);t["default"]=o.exports}},[["9b8b","common/runtime","common/vendor"]]]);