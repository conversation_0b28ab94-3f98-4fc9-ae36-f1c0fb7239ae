(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/products/product4/index"],{"49fe":function(e,n,t){"use strict";(function(e,n){var a=t("47a9");t("9c86");a(t("3240"));var c=a(t("ae69"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(c.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"8b67":function(e,n,t){"use strict";t.d(n,"b",(function(){return a})),t.d(n,"c",(function(){return c})),t.d(n,"a",(function(){}));var a=function(){var e=this.$createElement;this._self._c},c=[]},ae69:function(e,n,t){"use strict";t.r(n);var a=t("8b67"),c=t("a9c4");for(var u in c)["default"].indexOf(u)<0&&function(e){t.d(n,e,(function(){return c[e]}))}(u);t("5463");var r=t("828b"),o=Object(r["a"])(c["default"],a["b"],a["c"],!1,null,"ef3af2e6",null,!1,a["a"],void 0);n["default"]=o.exports}},[["49fe","common/runtime","common/vendor"]]]);