<view class="products-list-container data-v-8af674a2"><view class="navbar data-v-8af674a2"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-back data-v-8af674a2" bindtap="__e"><text class="back-icon data-v-8af674a2">‹</text></view><view class="nav-title data-v-8af674a2">商品列表</view></view><view class="products-grid data-v-8af674a2"><block wx:for="{{$root.l0}}" wx:for-item="product" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['goToProduct',['$0'],[[['productList','id',product.$orig.id]]]]]]]}}" class="product-card data-v-8af674a2" bindtap="__e"><view class="product-image-wrapper data-v-8af674a2"><image class="product-image data-v-8af674a2" src="{{product.$orig.image}}" mode="aspectFill"></image></view><view class="product-info data-v-8af674a2"><view class="product-name data-v-8af674a2">{{product.$orig.name}}</view><view class="product-price data-v-8af674a2"><text class="price-symbol data-v-8af674a2">¥</text><text class="price-value data-v-8af674a2">{{product.g0}}</text></view></view><view class="product-button data-v-8af674a2"><button data-event-opts="{{[['tap',[['goToProduct',['$0'],[[['productList','id',product.$orig.id]]]]]]]}}" class="view-button data-v-8af674a2" catchtap="__e">查看详情</button></view></view></block></view></view>