<view><view class="box"><view class="orderInfoTip"><view class="tit">{{$root.m0+''}}<block wx:if="{{timeout&&orderDetailsData.status===1}}"><text class="smw">( 已经超时)</text></block></view><block wx:if="{{orderDetailsData.status===7}}"><view class="rejectionReason"><block wx:if="{{orderDetailsData.payStatus===1||orderDetailsData.payStatus===2}}"><text>退款成功</text></block><block wx:else><block wx:if="{{orderDetailsData.cancelReason}}"><text>{{orderDetailsData.cancelReason}}</text></block><block wx:else><block wx:if="{{orderDetailsData.rejectionReason}}"><text>{{orderDetailsData.rejectionReason}}</text></block></block></block></view></block><block wx:if="{{!timeout&&orderDetailsData.status===1}}"><view><view class="time"><view class="timeIcon"></view>等待支付：<text>{{rocallTime}}</text><text>{{paymentTime}}</text></view></view></block><view class="againBtn"><block wx:if="{{!timeout&&orderDetailsData.status===1||orderDetailsData.status===2||orderDetailsData.status===3||orderDetailsData.status===4}}"><button class="new_btn" type="default" data-event-opts="{{[['tap',[['handleCancel',['center','$0'],['orderDetailsData']]]]]}}" bindtap="__e">取消订单</button></block><block wx:if="{{!timeout&&orderDetailsData.status===1}}"><button class="new_btn btn" type="default" data-event-opts="{{[['tap',[['handlePay',['$0'],['orderDetailsData.id']]]]]}}" bindtap="__e">立即支付</button></block><block wx:if="{{orderDetailsData.status===2}}"><button class="new_btn btn" type="default" data-event-opts="{{[['tap',[['handleReminder',['center','$0'],['orderDetailsData.id']]]]]}}" bindtap="__e">催单</button></block><block wx:if="{{orderDetailsData.status==5}}"><button class="new_btn" type="default" data-event-opts="{{[['tap',[['handleRefund',['center']]]]]}}" bindtap="__e">申请退款</button></block><block wx:if="{{orderDetailsData.status!==7}}"><button class="new_btn" type="default" data-event-opts="{{[['tap',[['oneMoreOrder',['$0'],['orderDetailsData.id']]]]]}}" bindtap="__e">再来一单</button></block></view></view></view><block wx:if="{{!timeout&&orderDetailsData.status===1}}"><view class="box timeTip"><view class="icon newIcon"></view>请在15分钟内完成支付，超时将自动取消。</view></block><block wx:if="{{orderDetailsData.status===6&&orderDetailsData.payStatus===2}}"><view class="box timeTip"><view class="icon moneyIcon"></view>您的订单已<text>退款成功</text>。</view></block></view>