<view class="box order_list data-v-776bb4ea"><view class="uniInfo data-v-776bb4ea"><view data-event-opts="{{[['tap',[['goRemark',['$event']]]]]}}" bindtap="__e" class="data-v-776bb4ea"><uni-list vue-id="c6bad3e2-1" class="data-v-776bb4ea" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item class="uniListItem data-v-776bb4ea" vue-id="{{('c6bad3e2-2')+','+('c6bad3e2-1')}}" showArrow="{{true}}" title="备注" bind:__l="__l" vue-slots="{{['footer']}}"><text class="temarkText data-v-776bb4ea" slot="footer">{{remark?remark:"推荐使用无接触配送"}}</text></uni-list-item></uni-list></view><view class="invoiceBox data-v-776bb4ea"><uni-list vue-id="c6bad3e2-3" class="data-v-776bb4ea" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item class="uniListItem data-v-776bb4ea" vue-id="{{('c6bad3e2-4')+','+('c6bad3e2-3')}}" title="发票" bind:__l="__l" vue-slots="{{['footer']}}"><text class="data-v-776bb4ea" slot="footer">请联系商家提供</text></uni-list-item></uni-list></view><view class="container data-v-776bb4ea"><uni-popup class="popupBox data-v-776bb4ea vue-ref" bind:change="__e" vue-id="c6bad3e2-5" data-ref="popup" data-event-opts="{{[['^change',[['change']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-776bb4ea"><view class="popupTitle data-v-776bb4ea"><text class="data-v-776bb4ea">按政府条例要求：</text><text class="data-v-776bb4ea">商家不得主动向您提供一次性餐具，请按需选择餐具数量</text></view><view class="popupCon data-v-776bb4ea"><view class="popupBtn data-v-776bb4ea"><text data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" bindtap="__e" class="data-v-776bb4ea">取消</text><text class="data-v-776bb4ea">选择本单餐具</text><text data-event-opts="{{[['tap',[['handlePiker',['$event']]]]]}}" bindtap="__e" class="data-v-776bb4ea">确定</text></view><pikers vue-id="{{('c6bad3e2-6')+','+('c6bad3e2-5')}}" baseData="{{baseData}}" data-ref="piker" data-event-opts="{{[['^changeCont',[['changeCont']]]]}}" bind:changeCont="__e" class="data-v-776bb4ea vue-ref" bind:__l="__l"></pikers></view><view class="popupSet data-v-776bb4ea"><view class="data-v-776bb4ea">后续订单餐具设置</view><view class="data-v-776bb4ea"><radio-group data-event-opts="{{[['change',[['handleRadio',['$event']]]]]}}" bindchange="__e" class="data-v-776bb4ea"><block wx:for="{{radioGroup}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><label class="data-v-776bb4ea"><radio value="{{item}}" color="#FFC200" checked="{{item==activeRadio}}" class="data-v-776bb4ea"></radio>{{item+''}}</label></block></radio-group></view></view></view></uni-popup></view></view></view>