<view class="data-v-9f95574c"><nav-bar vue-id="8dd740cc-1" class="data-v-9f95574c" bind:__l="__l"></nav-bar><view data-event-opts="{{[['touchmove',[['disabledScroll',['$event']]]]]}}" class="home_content data-v-9f95574c" style="{{'padding-top:'+(ht+'px')+';'}}" catchtouchmove="__e"><view class="restaurant_info_box data-v-9f95574c"><view class="restaurant_info data-v-9f95574c"><view class="info_top data-v-9f95574c"><view class="info_top_left data-v-9f95574c"><image class="logo_ruiji data-v-9f95574c" src="../../static/logo_ruiji.png"></image></view>·<view class="info_top_right data-v-9f95574c"><view class="right_title3 data-v-9f95574c"><text class="data-v-9f95574c">{{currentShopInfo.shopName}}</text><block wx:if="{{shopStatus===1}}"><view class="businessStatus data-v-9f95574c">营业中</view></block><block wx:else><view class="businessStatus close data-v-9f95574c">休息中</view></block></view><view class="right_details data-v-9f95574c"><view class="details_flex data-v-9f95574c"><image class="top_icon data-v-9f95574c" src="../../static/money.png"></image><text class="icon_text data-v-9f95574c">{{"配送费"+$root.m0+"元"}}</text></view></view></view></view><view class="info_bottom data-v-9f95574c"><view class="data-v-9f95574c"><view class="word data-v-9f95574c">{{currentShopInfo.shopName}}</view><view class="address data-v-9f95574c"><icon class="data-v-9f95574c"></icon>{{''+currentShopInfo.shopAddress+''}}</view></view><view class="data-v-9f95574c"><view data-event-opts="{{[['tap',[['handlePhone',['bottom']]]]]}}" class="phone data-v-9f95574c" bindtap="__e"><icon class="phoneIcon data-v-9f95574c"></icon></view></view></view></view></view><block wx:if="{{shopStatus===1}}"><view class="restaurant_menu_list data-v-9f95574c"><view class="type_list data-v-9f95574c"><scroll-view class="u-tab-view menu-scroll-view data-v-9f95574c" scroll-y="{{true}}" scroll-with-animation="{{true}}" scroll-top="{{scrollTop+100}}" scroll-into-view="{{itemId}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['type_item','data-v-9f95574c',typeIndex==index?'active':'']}}" id="target" data-event-opts="{{[['tap',[['swichMenu',['$0',index],[[['typeListData','',index]]]]]]]}}" catchtap="__e"><view class="{{['item','data-v-9f95574c',item.g0>5?'allLine':'']}}">{{item.$orig.name}}</view></view></block><view class="seize_seat data-v-9f95574c"></view></scroll-view></view><block wx:if="{{$root.g1}}"><scroll-view class="vegetable_order_list data-v-9f95574c" scroll-y="true" scroll-top="0rpx"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="type_item data-v-9f95574c"><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_img data-v-9f95574c" bindtap="__e"><image class="dish_img_url data-v-9f95574c" mode="aspectFill" src="{{item.$orig.image}}"></image></view><view class="dish_info data-v-9f95574c"><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_name data-v-9f95574c" bindtap="__e">{{item.$orig.name}}</view><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_label data-v-9f95574c" bindtap="__e">{{item.$orig.description||item.$orig.name}}</view><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_label data-v-9f95574c" bindtap="__e">月销量0</view><view class="dish_price data-v-9f95574c"><text class="ico data-v-9f95574c">￥</text>{{''+item.g2+''}}</view><block wx:if="{{item.g3}}"><view class="dish_active data-v-9f95574c"><block wx:if="{{item.$orig.dishNumber>=1}}"><image class="dish_red data-v-9f95574c" src="../../static/btn_red.png" data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],[[['dishListItems','',index]]]]]]]}}" bindtap="__e"></image></block><block wx:if="{{item.$orig.dishNumber>0}}"><text class="dish_number data-v-9f95574c">{{item.$orig.dishNumber}}</text></block><image class="dish_add data-v-9f95574c" src="../../static/btn_add.png" data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],[[['dishListItems','',index]]]]]]]}}" bindtap="__e"></image></view></block><block wx:else><view class="dish_active_btn data-v-9f95574c"><view data-event-opts="{{[['tap',[['moreNormDataesHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="check_but data-v-9f95574c" bindtap="__e">选择规格</view></view></block></view></view></block><view class="seize_seat data-v-9f95574c"></view></scroll-view></block><block wx:else><view class="no_dish data-v-9f95574c"><block wx:if="{{$root.g4>0}}"><view class="data-v-9f95574c">该分类下暂无菜品</view></block></view></block></view></block><view class="restaurant_close data-v-9f95574c">店铺已打烊</view><view class="mask-box data-v-9f95574c"></view><block wx:if="{{$root.g5}}"><view class="footer_order_buttom data-v-9f95574c"><view class="order_number data-v-9f95574c"><image class="order_number_icon data-v-9f95574c" src="../../static/btn_waiter_nor.png" mode></image></view><view class="order_price data-v-9f95574c"><text class="ico data-v-9f95574c">￥</text>0</view><view class="ord<strong></strong>er_but data-v-9f95574c">去结算</view></view></block><block wx:else><view class="footer_order_buttom order_form data-v-9f95574c"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="orderCar data-v-9f95574c" bindtap="__e"><view class="order_number data-v-9f95574c"><image class="order_number_icon data-v-9f95574c" src="../../static/btn_waiter_sel.png" mode></image><view class="order_dish_num data-v-9f95574c">{{orderDishNumber}}</view></view><view class="order_price data-v-9f95574c"><text class="ico data-v-9f95574c">￥</text>{{''+$root.g6+''}}</view></view><view data-event-opts="{{[['tap',[['goOrder']]]]}}" class="order_but data-v-9f95574c" bindtap="__e">去结算</view></view></block><view hidden="{{!(openMoreNormPop)}}" class="pop_mask data-v-9f95574c"><pop-mask vue-id="8dd740cc-2" moreNormDishdata="{{moreNormDishdata}}" moreNormdata="{{moreNormdata}}" flavorDataes="{{flavorDataes}}" data-event-opts="{{[['^checkMoreNormPop',[['checkMoreNormPop']]],['^addShop',[['addShop']]],['^closeMoreNorm',[['closeMoreNorm']]]]}}" bind:checkMoreNormPop="__e" bind:addShop="__e" bind:closeMoreNorm="__e" class="data-v-9f95574c" bind:__l="__l"></pop-mask></view><view hidden="{{!(openDetailPop)}}" class="pop_mask data-v-9f95574c" style="z-index:9999;"><dish-detail vue-id="8dd740cc-3" dishDetailes="{{dishDetailes}}" openDetailPop="{{openDetailPop}}" dishMealData="{{dishMealData}}" data-event-opts="{{[['^redDishAction',[['redDishAction']]],['^addDishAction',[['addDishAction']]],['^moreNormDataesHandle',[['moreNormDataesHandle']]],['^dishClose',[['dishClose']]]]}}" bind:redDishAction="__e" bind:addDishAction="__e" bind:moreNormDataesHandle="__e" bind:dishClose="__e" class="data-v-9f95574c" bind:__l="__l"></dish-detail></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" hidden="{{!(openOrderCartList)}}" class="pop_mask data-v-9f95574c" bindtap="__e"><pop-cart vue-id="8dd740cc-4" openOrderCartLis="{{openOrderCartList}}" orderAndUserInfo="{{orderAndUserInfo}}" data-event-opts="{{[['^clearCardOrder',[['clearCardOrder']]],['^addDishAction',[['addDishAction']]],['^redDishAction',[['redDishAction']]]]}}" bind:clearCardOrder="__e" bind:addDishAction="__e" bind:redDishAction="__e" class="data-v-9f95574c" bind:__l="__l"></pop-cart></view><view hidden="{{!(loaddingSt)}}" class="pop_mask data-v-9f95574c"><view class="lodding data-v-9f95574c"><image class="lodding_ico data-v-9f95574c" src="../../static/lodding.gif" mode></image><view class="lodding_text data-v-9f95574c">加载中...</view></view></view><phone vue-id="8dd740cc-5" phoneData="{{phoneData}}" data-ref="phone" data-event-opts="{{[['^closePopup',[['closePopup']]]]}}" bind:closePopup="__e" class="data-v-9f95574c vue-ref" bind:__l="__l"></phone><block wx:if="{{shopStatus===0}}"><view class="colseShop data-v-9f95574c"><view class="shop data-v-9f95574c">本店已打样</view></view></block></view></view>