<block wx:if="{{dishDetailes.type==1}}"><view class="dish_detail_pop data-v-b5057c36"><image class="div_big_image data-v-b5057c36" mode="aspectFill" src="{{dishDetailes.image}}"></image><view class="title data-v-b5057c36">{{dishDetailes.name}}</view><view class="desc data-v-b5057c36">{{dishDetailes.description}}</view><view class="but_item data-v-b5057c36"><view class="price data-v-b5057c36"><text class="ico data-v-b5057c36">￥</text>{{''+$root.g0+''}}</view><block wx:if="{{$root.g1}}"><view class="active data-v-b5057c36"><image class="dish_red data-v-b5057c36" src="../../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image><text class="dish_number data-v-b5057c36">{{dishDetailes.dishNumber}}</text><image class="dish_add data-v-b5057c36" src="../../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{$root.g2>0}}"><view class="active data-v-b5057c36"><view data-event-opts="{{[['tap',[['moreNormDataesHandle',['$0'],['dishDetailes']]]]]}}" class="dish_card_add data-v-b5057c36" bindtap="__e">选择规格</view></view></block><block wx:if="{{$root.g3}}"><view class="active data-v-b5057c36"><view data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" class="dish_card_add data-v-b5057c36" bindtap="__e">加入购物车</view></view></block></view><view data-event-opts="{{[['tap',[['dishClose',['$event']]]]]}}" class="close data-v-b5057c36" bindtap="__e"><image class="close_img data-v-b5057c36" src="../../../static/but_close.png" mode></image></view></view></block><block wx:else><view class="dish_detail_pop data-v-b5057c36"><scroll-view class="dish_items data-v-b5057c36" scroll-y="true" scroll-top="0rpx"><block wx:for="{{dishMealData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="dish_item data-v-b5057c36"><image class="div_big_image data-v-b5057c36" src="{{item.image}}" mode></image><view class="title data-v-b5057c36">{{''+item.name+''}}<text class="data-v-b5057c36">{{"X"+item.copies}}</text></view><view class="desc data-v-b5057c36">{{item.description}}</view></view></block></scroll-view><view class="but_item data-v-b5057c36"><view class="price data-v-b5057c36"><text class="ico data-v-b5057c36">￥</text>{{''+dishDetailes.price+''}}</view><block wx:if="{{dishDetailes.dishNumber&&dishDetailes.dishNumber>0}}"><view class="active data-v-b5057c36"><image class="dish_red data-v-b5057c36" src="../../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image><text class="dish_number data-v-b5057c36">{{dishDetailes.dishNumber}}</text><image class="dish_add data-v-b5057c36" src="../../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{dishDetailes.dishNumber==0}}"><view class="active data-v-b5057c36"><view data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" class="dish_card_add data-v-b5057c36" bindtap="__e">加入购物车</view></view></block></block></view><view data-event-opts="{{[['tap',[['dishClose',['$event']]]]]}}" class="close data-v-b5057c36" bindtap="__e"><image class="close_img data-v-b5057c36" src="../../../static/but_close.png" mode></image></view></view></block>