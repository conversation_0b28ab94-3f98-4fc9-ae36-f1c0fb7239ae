<view class="more_norm_pop data-v-17916d8e"><view class="title data-v-17916d8e">{{moreNormDishdata.name}}</view><scroll-view class="items_cont data-v-17916d8e" scroll-y="true" scroll-top="0rpx"><block wx:for="{{$root.l1}}" wx:for-item="obj" wx:for-index="index" wx:key="index"><view class="item_row data-v-17916d8e"><view class="flavor_name data-v-17916d8e">{{obj.$orig.name}}</view><view class="flavor_item data-v-17916d8e"><block wx:for="{{obj.l0}}" wx:for-item="item" wx:for-index="ind" wx:key="ind"><view data-event-opts="{{[['tap',[['checkMoreNormPop',['$0','$1'],[[['moreNormdata','',index,'value']],[['moreNormdata','',index],['value','',ind]]]]]]]}}" class="{{['data-v-17916d8e',(true)?'item':'',(item.g0!==-1)?'act':'']}}" bindtap="__e">{{''+item.$orig+''}}</view></block></view></view></block></scroll-view><view class="but_item data-v-17916d8e"><view class="price data-v-17916d8e"><text class="ico data-v-17916d8e">￥</text>{{''+moreNormDishdata.price+''}}</view><view class="active data-v-17916d8e"><view data-event-opts="{{[['tap',[['addShop',['$0','普通'],['moreNormDishdata']]]]]}}" class="dish_card_add data-v-17916d8e" bindtap="__e">加入购物车</view></view></view><view data-event-opts="{{[['tap',[['closeMoreNorm',['$0'],['moreNormDishdata']]]]]}}" class="close data-v-17916d8e" bindtap="__e"><image class="close_img data-v-17916d8e" src="../../../static/but_close.png" mode></image></view></view>