<view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="cart_pop data-v-2e4fd690" catchtap="__e"><view class="top_title data-v-2e4fd690"><view class="tit data-v-2e4fd690">购物车</view><view data-event-opts="{{[['tap',[['clearCardOrder']]]]}}" class="clear data-v-2e4fd690" catchtap="__e"><image class="clear_icon data-v-2e4fd690" src="../../../static/clear.png" mode></image><text class="clear-des data-v-2e4fd690">清空</text></view></view><scroll-view class="card_order_list data-v-2e4fd690" scroll-y="true" scroll-top="40rpx"><block wx:for="{{orderAndUserInfo}}" wx:for-item="item" wx:for-index="ind" wx:key="ind"><view class="type_item_cont data-v-2e4fd690"><block wx:for="{{item.dishList}}" wx:for-item="obj" wx:for-index="index" wx:key="index"><view class="type_item data-v-2e4fd690"><view class="dish_img data-v-2e4fd690"><image class="dish_img_url data-v-2e4fd690" mode="aspectFill" src="{{obj.image}}"></image></view><view class="dish_info data-v-2e4fd690"><view class="dish_name data-v-2e4fd690">{{obj.name}}</view><block wx:if="{{obj.dishFlavor}}"><view class="dish_dishFlavor data-v-2e4fd690">{{obj.dishFlavor}}</view></block><view class="dish_price data-v-2e4fd690"><text class="ico data-v-2e4fd690">￥</text>{{''+obj.amount+''}}</view><view class="dish_active data-v-2e4fd690"><block wx:if="{{obj.number&&obj.number>0}}"><image class="dish_red data-v-2e4fd690" src="../../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','购物车'],[[['orderAndUserInfo','',ind],['dishList','',index]]]]]]]}}" catchtap="__e"></image></block><block wx:if="{{obj.number&&obj.number>0}}"><text class="dish_number data-v-2e4fd690">{{obj.number}}</text></block><image class="dish_add data-v-2e4fd690" src="../../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','购物车'],[[['orderAndUserInfo','',ind],['dishList','',index]]]]]]]}}" catchtap="__e"></image></view></view></view></block></view></block><view class="seize_seat data-v-2e4fd690"></view></scroll-view></view>