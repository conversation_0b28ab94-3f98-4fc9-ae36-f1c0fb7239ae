<view class="history_order data-v-2816545c"><uni-nav-bar vue-id="411e5780-1" left-icon="back" leftIcon="arrowleft" title="历史订单" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-2816545c" bind:__l="__l"></uni-nav-bar><scroll-view class="scroll-row data-v-2816545c" scroll-x="{{true}}" scroll-into-view="{{scrollinto}}" scroll-with-animation="{{true}}" enable-flex="{{true}}"><block wx:for="{{tabBars}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="scroll-row-item data-v-2816545c" id="{{'tab'+index}}" data-event-opts="{{[['tap',[['changeTab',[index]]]]]}}" bindtap="__e"><view class="{{['data-v-2816545c',tabIndex==index?'scroll-row-item-act':'']}}"><text class="line data-v-2816545c"></text>{{item}}</view></view></block></scroll-view><swiper style="{{'height:'+(scrollH+'px')+';'}}" current="{{tabIndex}}" data-event-opts="{{[['change',[['onChangeSwiperTab',['$event']]]]]}}" bindchange="__e" class="data-v-2816545c"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-2816545c"><scroll-view style="{{'height:'+(scrollH+'px')+';'}}" scroll-y="true" data-event-opts="{{[['scrolltolower',[['lower',['$event']]]]]}}" bindscrolltolower="__e" class="data-v-2816545c"><block wx:if="{{$root.g0}}"><view class="main recent_orders data-v-2816545c"><block wx:for="{{item.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['box','order_lists','data-v-2816545c',(item.m0+1===item.g1)?'item-last':'']}}"><view class="date_type data-v-2816545c"><text class="time data-v-2816545c">{{item.$orig.orderTime}}</text><text class="{{['type','status','data-v-2816545c',(item.$orig.status==2)?'status':'']}}">{{item.m1}}</text></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" class="orderBox data-v-2816545c" bindtap="__e"><view class="food_num data-v-2816545c"><scroll-view class="pic data-v-2816545c" style="width:100%;overflow:hidden;white-space:nowrap;" scroll-x="true"><block wx:for="{{item.$orig.orderDetailList}}" wx:for-item="num" wx:for-index="y" wx:key="y"><view class="food_num_item data-v-2816545c"><view class="img data-v-2816545c"><image src="{{num.image}}" class="data-v-2816545c"></image></view><view class="food data-v-2816545c">{{num.name}}</view></view></block></scroll-view></view><view class="numAndAum data-v-2816545c"><view class="data-v-2816545c"><text class="data-v-2816545c">{{"￥"+item.g2}}</text></view><view class="data-v-2816545c"><text class="data-v-2816545c">{{"共"+item.m2.count+"件"}}</text></view></view></view><view class="againBtn data-v-2816545c"><button class="new_btn data-v-2816545c" type="default" data-event-opts="{{[['tap',[['oneMoreOrder',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">再来一单</button><block wx:if="{{item.m3}}"><button class="new_btn btn data-v-2816545c" type="default" data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">去支付</button></block><block wx:if="{{item.$orig.status===2}}"><button class="new_btn btn data-v-2816545c" type="default" data-event-opts="{{[['tap',[['handleReminder',['center','$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">催单</button></block></view></view></block></view></block></scroll-view></swiper-item></block></swiper><uni-popup class="comPopupBox data-v-2816545c vue-ref" vue-id="411e5780-2" data-ref="commonPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-2816545c"><view class="text data-v-2816545c">{{textTip}}</view><block wx:if="{{showConfirm}}"><view class="btn data-v-2816545c"><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" bindtap="__e" class="data-v-2816545c">确认</view></view></block></view></uni-popup></view>