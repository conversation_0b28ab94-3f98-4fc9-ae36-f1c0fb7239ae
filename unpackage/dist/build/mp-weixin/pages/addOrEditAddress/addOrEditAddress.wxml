<view class="customer-box data-v-7afab60c"><uni-nav-bar vue-id="5b3c5900-1" left-icon="back" leftIcon="arrowleft" title="{{delId?'编辑收货地址':'新增收货地址'}}" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-7afab60c" bind:__l="__l"></uni-nav-bar><view class="add_edit data-v-7afab60c" style="{{'height:'+('calc(100% - '+statusBarHeight+' - 44px)')+';'}}"><form class="form_address data-v-7afab60c"><view class="uni-form-item uni-column form_item data-v-7afab60c"><view class="title data-v-7afab60c">联系人:</view><uni-easyinput bind:input="__e" class="uni-input data-v-7afab60c" vue-id="5b3c5900-2" placeholder-class="uni-place" placeholder="请填写收货人的姓名" minlength="2" maxlength="12" value="{{form.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" bind:__l="__l"></uni-easyinput><view class="radio data-v-7afab60c"><block wx:for="{{items}}" wx:for-item="item" wx:for-index="index" wx:key="value"><view data-event-opts="{{[['tap',[['sexChangeHandle',['$0'],[[['items','value',item.value,'value']]]]]]]}}" class="radio-item data-v-7afab60c" bindtap="__e"><block wx:if="{{item.value!=form.sex}}"><image class="radio-img data-v-7afab60c" src="../../static/icon-radio.png"></image></block><block wx:else><image class="radio-img data-v-7afab60c" src="../../static/icon-radio-selected.png"></image></block><text class="radio-label data-v-7afab60c">{{item.name}}</text></view></block></view></view><view class="uni-form-item uni-column form_item data-v-7afab60c"><view class="title data-v-7afab60c">手机号:</view><uni-easyinput bind:input="__e" class="uni-input data-v-7afab60c" vue-id="5b3c5900-3" type="number" placeholder-class="uni-place" placeholder="请填写收货人手机号码" maxlength="11" value="{{form.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" bind:__l="__l"></uni-easyinput></view><view class="uni-form-item uni-column form_item pad data-v-7afab60c"><view class="title data-v-7afab60c">收货地址:</view><view class="update-input data-v-7afab60c"><view data-event-opts="{{[['tap',[['openAddres',['$event']]]]]}}" class="update-adress data-v-7afab60c" bindtap="__e"><block wx:if="{{showInput}}"><text class="{{['uni-input','data-v-7afab60c',address!==''?'':'uni-place']}}">{{address!==""?address:"省/市/区"}}</text></block><text class="addressIcon data-v-7afab60c"><text class="{{['icon','data-v-7afab60c',showClass?'iconOn':'']}}"></text></text></view><textarea class="{{['detail','data-v-7afab60c',(platform=='ios')?'detail-ios':'']}}" placeholder-class="uni-place" placeholder="详细地址（精确到门牌号）" data-event-opts="{{[['input',[['__set_model',['$0','detail','$event',[]],['form']]]]]}}" value="{{form.detail}}" bindinput="__e"></textarea></view></view><view class="uni-form-item uni-column form_item tag-box data-v-7afab60c"><view class="title data-v-7afab60c">标签:</view><block wx:for="{{options}}" wx:for-item="item" wx:for-index="__i0__" wx:key="type"><text data-event-opts="{{[['tap',[['getTextOption',['$0'],[[['options','type',item.type]]]]]]]}}" class="{{['tag_text','data-v-7afab60c',(form.type===item.type)?'active':'']}}" bindtap="__e">{{item.name}}</text></block></view></form><view class="add_address data-v-7afab60c"><button class="add_btn data-v-7afab60c" type="primary" plain="true" data-event-opts="{{[['tap',[['addAddressFun']]]]}}" bindtap="__e">保存地址</button><block wx:if="{{showDel}}"><button class="del_btn data-v-7afab60c" type="default" plain="true" data-event-opts="{{[['tap',[['deleteAddressFun']]]]}}" bindtap="__e">删除地址</button></block></view></view><simple-address vue-id="5b3c5900-4" pickerValueDefault="{{cityPickerValueDefault}}" themeColor="#F58C21" data-ref="simpleAddress" data-event-opts="{{[['^onConfirm',[['onConfirm']]],['^isClass',[['isClass']]]]}}" bind:onConfirm="__e" bind:isClass="__e" class="data-v-7afab60c vue-ref" bind:__l="__l"></simple-address></view>