(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/vendor"],{"011a":function(e,l){function a(){try{var l=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){})))}catch(l){}return(e.exports=a=function(){return!!l},e.exports.__esModule=!0,e.exports["default"]=e.exports)()}e.exports=a,e.exports.__esModule=!0,e.exports["default"]=e.exports},"02f2":function(e,l,a){},"034b":function(e,l,a){"use strict";var t=a("34a8"),n=a.n(t);n.a},"0a99":function(e,l,a){"use strict";var t=a("1650"),n=a.n(t);n.a},"0bdb":function(e,l,a){var t=a("d551");function n(e,l){for(var a=0;a<l.length;a++){var n=l[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,t(n.key),n)}}e.exports=function(e,l,a){return l&&n(e.prototype,l),a&&n(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"0c6c":function(e,l,a){"use strict";var t=a("320a"),n=a.n(t);n.a},"0ee4":function(e,l){var a;a=function(){return this}();try{a=a||new Function("return this")()}catch(t){"object"===typeof window&&(a=window)}e.exports=a},"10a7":function(e,l,a){"use strict";var t=a("5b4d"),n=a.n(t);n.a},1650:function(e,l,a){},"1a8d":function(e,l,a){"use strict";var t=a("4318"),n=a.n(t);n.a},"1d42":function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var n=t(a("7eb4")),u=t(a("7ca3")),r=t(a("ee10")),o=a("ab49"),i=a("8f59"),v=a("d585"),s=a("9ec3"),b=t(a("3abc"));function c(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function f(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?c(Object(a),!0).forEach((function(l){(0,u.default)(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}var d={data:function(){return{platform:"ios",orderDishPrice:0,openPayType:!1,psersonUrl:"../../static/btn_waiter_sel.png",nickName:"",gender:0,phoneNumber:"",address:"",remark:"",arrivalTime:"",orderTime:"",addressBookId:"",addressLabel:"",tagLabel:"",orderDishNumber:0,showDisplay:!1,type:"center",expirationTime:"",tablewareData:"无需餐具",tableware:"",packAmount:0,value:[0,0],timeValue:[0,0],indicatorStyle:"height: 44px;color:#333",tabIndex:0,scrollinto:"tab0",scrollH:0,popleft:["今天","明天"],visible:!0,baseData:["无需餐具","1","2","3","4","5","6","7","8","9","10"],activeRadio:"无需餐具",radioGroup:["依据餐量提供","无需餐具"],popright:["立即派送","09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00","18:30","19:00","19:30","20:00","20:30","21:00","21:30","22:00","22:30","23:00"],newDateData:[],textTip:"",showConfirm:!1,phoneData:"15200000001",toDate:null,tomorrowStart:null,newDate:null,selectValue:0,selectDateValue:0,timeout:!1,isTomorrow:!1,status:0,num:0,weeks:[],scrollTop:0,addressList:[],isHandlePy:!1}},computed:{orderListDataes:function(){return this.orderListData()},orderDataes:function(){var e=[];if(!1===this.showDisplay){if(this.orderListDataes.length>3)for(var l=0;l<3;l++)e.push(this.orderListDataes[l]);else e=this.orderListDataes;return e}return this.orderListDataes}},created:function(){var e=this,l=new Date;this.toDate=new Date(l.toLocaleDateString()).getTime(),this.tomorrowStart=this.toDate+864e5,this.newDate=3600*l.getHours()+60*l.getMinutes();var a=[this.toDate,this.tomorrowStart];a.forEach((function(l){e.weeks.push((0,s.getWeekDate)(l))})),this.getAddressList()},mounted:function(){this.countdown()},components:{Pikers:function(){a.e("components/uni-piker/index").then(function(){return resolve(a("12ab"))}.bind(null,a)).catch(a.oe)},AddressPop:function(){Promise.all([a.e("common/vendor"),a.e("pages/order/components/address")]).then(function(){return resolve(a("22ef"))}.bind(null,a)).catch(a.oe)},DishDetail:function(){Promise.all([a.e("common/vendor"),a.e("pages/order/components/dishDetail")]).then(function(){return resolve(a("84f7"))}.bind(null,a)).catch(a.oe)},DishInfo:function(){Promise.all([a.e("common/vendor"),a.e("pages/order/components/dishInfo")]).then(function(){return resolve(a("140c"))}.bind(null,a)).catch(a.oe)}},onLoad:function(e){var l=this;return(0,r.default)(n.default.mark((function e(){var a;return n.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(l.initPlatform(),l.psersonUrl=l.$store.state.baseUserInfo&&l.$store.state.baseUserInfo.avatarUrl,l.nickName=l.$store.state.baseUserInfo&&l.$store.state.baseUserInfo.nickName,l.gender=l.$store.state.baseUserInfo&&l.$store.state.baseUserInfo.gender,l.remark=l.remarkData(),l.init(),!l.addressData()||!l.addressData().detail){e.next=17;break}l.addressBookId="",a=l.addressData(),l.address=a.provinceName+a.cityName+a.districtName+a.detail,l.phoneNumber=a.phone,l.nickName=a.consignee,l.gender=a.sex,l.addressBookId=a.id,l.addressLabel=(0,s.getLableVal)(a.label),e.next=19;break;case 17:return e.next=19,l.getAddressBookDefault();case 19:return e.next=21,l.getEstimatedDeliveryTime();case 21:l.getDateDate(),l.setArrivalTime(l.arrivalTime),l.setGender(l.gender);case 24:case"end":return e.stop()}}),e)})))()},onReady:function(){var l=this;e.getSystemInfo({success:function(a){l.scrollH=a.windowHeight-e.upx2px(100)}})},methods:f(f(f({},(0,i.mapState)(["orderListData","remarkData","addressData","storeInfo","shopInfo","deliveryFee"])),(0,i.mapMutations)(["setAddressBackUrl","setOrderData","setArrivalTime","setRemark","setGender"])),{},{deliveryFee:function(){var e=this.$store.state.deliveryFee;return null===e||void 0===e||isNaN(e)?0:Number(e)},init:function(){this.computOrderInfo()},initPlatform:function(){var l=e.getSystemInfoSync();this.platform=l.platform},getEstimatedDeliveryTime:function(){var e=this;return(0,r.default)(n.default.mark((function l(){var a;return n.default.wrap((function(l){while(1)switch(l.prev=l.next){case 0:return l.next=2,(0,o.getEstimatedDeliveryTime)({shopId:e.shopInfo().shopId,customerAddress:e.address});case 2:a=l.sent,e.arrivalTime=(0,b.default)(a.data).format("HH:mm"),e.orderTime=a.data;case 5:case"end":return l.stop()}}),l)})))()},getDateDate:function(){var e=(0,b.default)(this.orderTime),l=["立即派送"];if(!(e.hour()>=22&&e.minute()>30)){e=e.minute()>30?e.add(1,"hour").set("minute",0):e.set("minute",30);while(1){if(23===e.hour()&&30===e.minute())break;var a="".concat(e.format("HH"),":").concat(e.format("mm"));l.push("".concat(a)),e=e.add(30,"minute")}}this.newDateData=l},getAddressList:function(){var e=this;this.testValue=!1,(0,o.queryAddressBookList)().then((function(l){1===l.code&&(e.testValue=!0,e.addressList=l.data)}))},getAddressBookDefault:function(){var e=this;return(0,o.getAddressBookDefault)().then((function(l){1===l.code&&(e.addressBookId="",e.address=l.data.provinceName+l.data.cityName+l.data.districtName+l.data.detail,e.phoneNumber=l.data.phone,e.nickName=l.data.consignee,e.gender=l.data.sex,e.addressBookId=l.data.id,e.addressLabel=(0,s.getLableVal)(l.data.label),e.tagLabel=l.data.label)}))},goAddress:function(){this.setAddressBackUrl("/pages/order/index"),0===this.addressList.length?e.redirectTo({url:"/pages/addOrEditAddress/addOrEditAddress"}):e.redirectTo({url:"/pages/address/address"})},getNewImage:function(e){return"".concat(v.baseUrl,"/common/download?name=").concat(e)},computOrderInfo:function(){var e=this,l=this.orderListDataes;this.orderDishNumber=this.orderDishPrice=0,this.orderDishPrice=0,l.map((function(l,a){e.orderDishPrice+=l.number*l.amount,e.orderDishNumber+=l.number,console.log(l)}));var a=this.deliveryFee();this.orderDishPrice=this.orderDishPrice+a+this.orderDishNumber},goBack:function(){e.navigateBack({delta:1})},closeMask:function(){this.openPayType=!1},payOrderHandle:function(){var l,a=this;if(this.isHandlePy=!0,!this.address)return e.showToast({title:"请选择收货地址",icon:"none"}),!1;var t=(l={payMethod:1,addressBookId:this.addressBookId,remark:this.remark,estimatedDeliveryTime:"立即派送"===this.arrivalTime?(0,s.presentFormat)():(0,s.dateFormat)(this.isTomorrow,this.arrivalTime),deliveryStatus:"立即派送"===this.arrivalTime?1:0},(0,u.default)(l,"remark",this.remark),(0,u.default)(l,"tablewareStatus",this.status),(0,u.default)(l,"tablewareNumber",this.num),(0,u.default)(l,"packAmount",this.orderDishNumber),(0,u.default)(l,"amount",this.orderDishPrice),(0,u.default)(l,"shopId",this.shopInfo().shopId),(0,u.default)(l,"deliveryFee",this.deliveryFee()),l);(0,o.submitOrderSubmit)(t).then((function(l){1===l.code?(a.isHandlePy=!1,a.setOrderData(l.data),a.setRemark(""),e.navigateTo({url:"/pages/contactMerchant/index"})):e.showToast({title:l.msg||"操作失败",icon:"none"})}))},call:function(){e.makePhoneCall({phoneNumber:"114"})},handleContact:function(e){this.showConfirm=!1,this.openPopuos(e),this.textTip="请联系商家进行取消！"},handleRefund:function(e){this.showConfirm=!1,this.openPopuos(e),this.textTip="请联系商家进行退款！"},goRemark:function(){this.setAddressBackUrl("/pages/order/index"),e.redirectTo({url:"/pages/remark/index"})},openPopuos:function(e){this.$refs.popup.open(e)},closePopup:function(e){this.$refs.popup.close(e)},change:function(e){},handlePiker:function(){if(""!==this.tableware)this.num=Number(this.tableware),this.status=0,"无需餐具"===this.tableware&&(this.num=0,this.status=0),"依据餐量提供"===this.tableware&&(this.num=this.orderDishNumber,this.status=1),"依据餐量提供"!==this.tableware||"无需餐具"!==this.tableware?this.tablewareData=this.tableware+"份":this.tablewareData=this.tableware;else{var e=this.baseData[this.$refs.dishinfo.$refs.piker.defaultValue[0]];this.tablewareData=e,"依据餐量提供"===this.activeRadio?(this.num=this.orderDishNumber,this.status=1):(this.num=0,this.status=0)}},changeCont:function(e){this.tableware=e},handleRadio:function(e){this.activeRadio=e.detail.value},countdown:function(){Date.parse(new Date)},dateChange:function(e){1===e?(this.newDateData=this.popright.slice(1),this.isTomorrow=!0):(this.isTomorrow=!1,this.newDateData=[],this.getDateDate()),this.tabIndex!=e&&(this.tabIndex=e)},timeClick:function(e){this.selectValue=e.i,this.setTime(e.val)},setTime:function(e){this.arrivalTime="立即派送"===e?(0,b.default)(this.orderTime).format("HH:mm"):e,this.setArrivalTime(this.arrivalTime)},touchstart:function(e){e.changedTouches[0].clientY}})};l.default=d}).call(this,a("df3c")["default"])},"1e17":function(e,l,a){"use strict";var t=a("c0f2"),n=a.n(t);n.a},"229e":function(e,l,a){"use strict";(function(e,t){var n=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var u=n(a("7eb4")),r=n(a("3b2d")),o=n(a("af34")),i=n(a("ee10")),v=n(a("7ca3")),s=n(a("a567")),b=a("ab49"),c=a("8f59"),f=a("d585");function d(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function p(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?d(Object(a),!0).forEach((function(l){(0,v.default)(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}var h={data:function(){return{title:"Hello",openOrderCartList:!1,typeListData:[],dishListData:[],dishListItems:[],dishDetailes:{},openDetailPop:!1,openMoreNormPop:!1,moreNormDataes:null,tableInfo:null,moreNormDishdata:null,moreNormdata:null,dishMealData:null,openTablePeoPleNumber:1,orderData:0,typeIndex:0,openTablePop:!1,flavorDataes:[],orderDishNumber:0,orderDishPrice:0,params:{shopId:"f3deb",storeId:"1282344676983062530",tableId:"1282346960773238786"},rightIdAndType:{},phoneData:"************",tablewareNumber:0,shopStatus:null,scrollTop:0,menuHeight:0,menuItemHeight:0,itemId:"",arr:[],mid:null,shopConfigs:{1:{shopName:"瑞吉外卖总店",shopAddress:"北京市海淀区中关村大街1号",phoneNumber:"************"},2:{shopName:"测试店铺1",shopAddress:"测试地址1",phoneNumber:"18631449103"},3:{shopName:"测试店铺2",shopAddress:"测试地址2",phoneNumber:"18631449103"},4:{shopName:"测试店铺3",shopAddress:"测试地址3",phoneNumber:"18631449103"},default:{shopName:"测试店铺4",shopAddress:"测试地址4",phoneNumber:"18631449103"}}}},components:{navBar:function(){Promise.all([a.e("common/vendor"),a.e("pages/common/Navbar/navbar")]).then(function(){return resolve(a("2131"))}.bind(null,a)).catch(a.oe)},Phone:function(){Promise.all([a.e("common/vendor"),a.e("components/uni-phone/index")]).then(function(){return resolve(a("586a"))}.bind(null,a)).catch(a.oe)},popMask:function(){a.e("pages/index/components/popMask").then(function(){return resolve(a("a900"))}.bind(null,a)).catch(a.oe)},popCart:function(){a.e("pages/index/components/popCart").then(function(){return resolve(a("2181"))}.bind(null,a)).catch(a.oe)},dishDetail:function(){a.e("pages/index/components/dishDetail").then(function(){return resolve(a("e115"))}.bind(null,a)).catch(a.oe)}},computed:{orderListDataes:function(){return this.orderListData()},loaddingSt:function(){return this.lodding()},orderAndUserInfo:function(){var e=[];return Array.isArray(this.orderListDataes)&&this.orderListDataes.forEach((function(l,a){var t,n,u={};u.nickName=null!==(t=l.name)&&void 0!==t?t:"",u.avatarUrl=null!==(n=l.image)&&void 0!==n?n:"",u.dishList=[l];var r=e.findIndex((function(e){return e.nickName==u.nickName}));-1!=r?e[r].dishList.push(l):e.push(u)})),e},ht:function(){return e.getMenuButtonBoundingClientRect().top+e.getMenuButtonBoundingClientRect().height+7},currentShopInfo:function(){var e=this.mid||"2";return this.shopConfigs[e]||this.shopConfigs["2"]}},onReady:function(){this.getMenuItemTop()},onLoad:function(l){if(e.onNetworkStatusChange((function(l){0==l.isConnected&&e.navigateTo({url:"/pages/nonet/index"})})),l){if(l.mid)this.mid=l.mid,s.default.setMid(l.mid),console.log("onLoad: 新的mid参数已保存:",l.mid);else{var a=s.default.getMid();a?(this.mid=a,console.log("onLoad: 使用本地存储的mid参数:",a)):(this.mid="2",s.default.setMid("2"),console.log("onLoad: 设置默认mid参数为: 2"))}this.updateShopInfoToStore(),l.status||l.formOrder||this.getData()}},onShow:function(){var e=s.default.getMid();if(e&&e!==this.mid)return this.mid=e,console.log("onShow: 更新mid参数为:",e),this.updateShopInfoToStore(),void(this.token()&&this.init());this.mid||(this.mid="2",s.default.setMid("2"),console.log("onShow: 设置默认mid参数为: 2")),this.updateShopInfoToStore(),this.token()&&this.init()},methods:p(p(p({},(0,c.mapMutations)(["setShopInfo","setShopStatus","initdishListMut","setStoreInfo","setBaseUserInfo","setLodding","setToken","setDeliveryFee"])),(0,c.mapState)(["shopInfo","orderListData","baseUserInfo","lodding","token","deliveryFee"])),{},{deliveryFee:function(){var e=this.$store.state.deliveryFee;return null===e||void 0===e||isNaN(e)?0:Number(e)},loginSync:function(){return new Promise((function(l,a){e.login({success:function(e){"login:ok"===e.errMsg&&l(e.code)}})}))},getData:function(){var l=t.getMenuButtonBoundingClientRect(),a=this;this.getShopInfo(),this.selectHeight=l.height,""===this.token()&&e.showModal({title:"温馨提示",content:"亲，授权微信登录后才能点单！",showCancel:!1,success:function(l){if(l.confirm){var t="";e.login({provider:"weixin",success:function(e){"login:ok"===e.errMsg&&(t=e.code)}}),e.getUserProfile({desc:"登录",success:function(l){a.setLodding(!0),a.setBaseUserInfo(l.userInfo);var n={code:t};(0,b.userLogin)(n).then((function(e){1===e.code&&(a.setToken(e.data.token),a.setDeliveryFee(e.data.deliveryFee),a.setShopInfo({shopName:e.data.shopName,shopAddress:e.data.shopAddress,shopId:e.data.shopId}),a.init())})).catch((function(l){a.setLodding(!1),e.showToast({title:"登录失败",icon:"none"})}))},fail:function(e){a.setLodding(!1)}})}}})},init:function(){var l=this;return(0,i.default)(u.default.mark((function a(){var t,n;return u.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return l.setLodding(!0),0!==l.typeIndex&&(l.typeIndex=0),console.log("init: 当前使用的mid参数:",l.mid),a.prev=3,a.next=6,(0,b.getCategoryList)();case 6:if(t=a.sent,!t||1!==t.code){a.next=14;break}if(n=t.data,l.mid&&(console.log("init: 根据mid参数过滤数据:",l.mid),n=t.data.filter((function(e){return e.createUser&&e.createUser.toString()===l.mid.toString()})),console.log("init: 过滤后的数据数量:",n.length)),l.typeListData=(0,o.default)(n),!(n.length>0)){a.next=14;break}return a.next=14,l.getDishListDataes(n[l.typeIndex||0]);case 14:return a.next=16,l.getTableOrderDishListes();case 16:a.next=22;break;case 18:a.prev=18,a.t0=a["catch"](3),console.error("init: 数据加载失败:",a.t0),e.showToast({title:"数据加载失败",icon:"none"});case 22:return a.prev=22,l.setLodding(!1),a.finish(22);case 25:case"end":return a.stop()}}),a,null,[[3,18,22,25]])})))()},swichMenu:function(e,l){var a=this;return(0,i.default)(u.default.mark((function t(){return u.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!=a.arr.length){t.next=3;break}return t.next=3,a.getMenuItemTop();case 3:if(l!=a.typeIndex){t.next=5;break}return t.abrupt("return");case 5:return a.$nextTick((function(){this.typeIndex=l,this.leftMenuStatus(l)})),t.next=8,a.getDishListDataes(e,l);case 8:case"end":return t.stop()}}),t)})))()},getElRect:function(l,a){var t=this;new Promise((function(n,u){var r=e.createSelectorQuery().in(t);r.select("."+l).fields({size:!0},(function(e){e?(t[a]=e.height,n()):setTimeout((function(){t.getElRect(l)}),10)})).exec()}))},leftMenuStatus:function(e){var l=this;return(0,i.default)(u.default.mark((function a(){return u.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(l.typeIndex=e,0!=l.menuHeight&&0!=l.menuItemHeight){a.next=6;break}return a.next=4,l.getElRect("menu-scroll-view","menuHeight");case 4:return a.next=6,l.getElRect("type_item","menuItemHeight");case 6:l.scrollTop=e*l.menuItemHeight+l.menuItemHeight/2-l.menuHeight/2;case 7:case"end":return a.stop()}}),a)})))()},getMenuItemTop:function(){var l=this;new Promise((function(a){var t=e.createSelectorQuery();t.selectAll(".class-item").boundingClientRect((function(e){e.length||setTimeout((function(){l.getMenuItemTop()}),10)})).exec()}))},getDishListDataes:function(l,a){var t=this;return(0,i.default)(u.default.mark((function n(){var r,o,i;return u.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.setLodding(!0),t.rightIdAndType={},t.rightIdAndType={id:l.id,type:l.type},r={categoryId:l.id},n.prev=4,1!==l.type){n.next=12;break}return n.next=8,(0,b.dishListByCategoryId)(r);case 8:o=n.sent,o&&1===o.code&&(t.dishListData=o.data&&o.data.map((function(e){return p(p({},e),{},{type:1,newCardNumber:0})}))),n.next=16;break;case 12:return n.next=14,(0,b.querySetmeaList)(r);case 14:i=n.sent,i&&1===i.code&&(t.dishListData=i.data&&i.data.map((function(e){return p(p({},e),{},{type:2,newCardNumber:0})})));case 16:t.typeIndex=a,t.setOrderNum(),n.next=24;break;case 20:n.prev=20,n.t0=n["catch"](4),console.error("getDishListDataes: 菜品数据加载失败:",n.t0),e.showToast({title:"菜品数据加载失败",icon:"none"});case 24:return n.prev=24,t.setLodding(!1),n.finish(24);case 27:case"end":return n.stop()}}),n,null,[[4,20,24,27]])})))()},getShopInfo:function(){var l=this;return(0,i.default)(u.default.mark((function a(){var t;return u.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,(0,b.getShopStatus)();case 3:t=a.sent,l.shopStatus=t.data,console.log(t.data),l.setShopStatus(t.data),a.next=13;break;case 9:a.prev=9,a.t0=a["catch"](0),console.error("getShopInfo: 店铺信息获取失败:",a.t0),e.showToast({title:"店铺信息获取失败",icon:"none"});case 13:case"end":return a.stop()}}),a,null,[[0,9]])})))()},getNewImage:function(e){return"".concat(f.baseUrl,"/common/download?name=").concat(e)},getTableOrderDishListes:function(){var e=this;return(0,i.default)(u.default.mark((function l(){var a;return u.default.wrap((function(l){while(1)switch(l.prev=l.next){case 0:return l.prev=0,l.next=3,(0,b.getShoppingCartList)({});case 3:a=l.sent,1===a.code&&(e.initdishListMut(a.data),e.computOrderInfo()),l.next=10;break;case 7:l.prev=7,l.t0=l["catch"](0),console.error("getTableOrderDishListes: 购物车数据加载失败:",l.t0);case 10:case"end":return l.stop()}}),l,null,[[0,7]])})))()},goOrder:function(){e.navigateTo({url:"/pages/order/index"})},addDishAction:function(l,a){var t=this;return(0,i.default)(u.default.mark((function n(){var o,i,v,s,c,f;return u.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!(l&&"object"===(0,r.default)(l)&&l.obj&&l.item)){n.next=4;break}return o=l.obj,i=l.item,n.abrupt("return",t.addDishAction(o,i));case 4:if(!t.openMoreNormPop||t.flavorDataes&&!(t.flavorDataes.length<=0)){n.next=7;break}return e.showToast({title:"请选择规格",icon:"none"}),n.abrupt("return",!1);case 7:return t.setLodding(!0),t.openMoreNormPop=!1,t.tablewareNumber++,t.dishDetailes.dishNumber++,t.orderListDataes&&!t.orderListDataes.some((function(e){return e.id==l.dishId}))&&t.flavorDataes.length>0&&(l.flavorRemark=JSON.stringify(t.flavorDataes)),v="",s=[],l.flavorRemark&&(s=JSON.parse(l.flavorRemark)),v=""!==l.dishFlavor&&l.dishFlavor?l.dishFlavor:s.length>0?s.join(","):null,c={dishFlavor:v},1===l.type?c=p(p({},c),{},{dishId:l.id}):2===l.type?c={setmealId:l.id}:"购物车"===a&&(c=l.dishId?p(p({},c),{},{dishId:l.dishId}):{setmealId:l.setmealId}),n.prev=18,n.next=21,(0,b.newAddShoppingCartAdd)(c);case 21:if(f=n.sent,1!==f.code){n.next=28;break}return n.next=25,t.getTableOrderDishListes();case 25:return n.next=27,t.getDishListDataes(t.rightIdAndType);case 27:t.flavorDataes=[];case 28:n.next=34;break;case 30:n.prev=30,n.t0=n["catch"](18),console.error("addDishAction: 添加菜品失败:",n.t0),e.showToast({title:"添加菜品失败",icon:"none"});case 34:return n.prev=34,t.setLodding(!1),n.finish(34);case 37:case"end":return n.stop()}}),n,null,[[18,30,34,37]])})))()},addShop:function(e){console.log(e),this.dishDetailes=e,this.addDishAction(e,"普通")},redDishAction:function(l,a){var t=this;return(0,i.default)(u.default.mark((function n(){var o,i,v,s,c,f;return u.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!(l&&"object"===(0,r.default)(l)&&l.obj&&l.item)){n.next=4;break}return o=l.obj,i=l.item,n.abrupt("return",t.redDishAction(o,i));case 4:return t.setLodding(!0),t.tablewareNumber--,t.dishDetailes.dishNumber--,v="",s=[],l.flavorRemark&&(s=JSON.parse(l.flavorRemark)),v=""!==l.dishFlavor&&l.dishFlavor?l.dishFlavor:s.length>0?s[0]:null,c={dishFlavor:v},1===l.type?c=p(p({},c),{},{dishId:l.id}):2===l.type?c={setmealId:l.id}:"购物车"===a&&(c=l.dishId?p(p({},c),{},{dishId:l.dishId}):{setmealId:l.setmealId}),n.prev=13,n.next=16,(0,b.newShoppingCartSub)(c);case 16:if(f=n.sent,1!==f.code){n.next=22;break}return n.next=20,t.getTableOrderDishListes();case 20:return n.next=22,t.getDishListDataes(t.rightIdAndType);case 22:n.next=28;break;case 24:n.prev=24,n.t0=n["catch"](13),console.error("redDishAction: 减少菜品失败:",n.t0),e.showToast({title:"减少菜品失败",icon:"none"});case 28:return n.prev=28,t.setLodding(!1),n.finish(28);case 31:case"end":return n.stop()}}),n,null,[[13,24,28,31]])})))()},clearCardOrder:function(){var l=this;return(0,i.default)(u.default.mark((function a(){return u.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return l.setLodding(!0),a.prev=1,a.next=4,(0,b.delShoppingCart)();case 4:return l.openOrderCartList=!1,a.next=7,l.getTableOrderDishListes();case 7:return a.next=9,l.getDishListDataes(l.rightIdAndType);case 9:a.next=15;break;case 11:a.prev=11,a.t0=a["catch"](1),console.error("clearCardOrder: 清空购物车失败:",a.t0),e.showToast({title:"清空购物车失败",icon:"none"});case 15:return a.prev=15,l.setLodding(!1),a.finish(15);case 18:case"end":return a.stop()}}),a,null,[[1,11,15,18]])})))()},openDetailHandle:function(l){var a=this;return(0,i.default)(u.default.mark((function t(){var n;return u.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a.dishDetailes=l,2!==l.type){t.next=19;break}return a.setLodding(!0),t.prev=3,t.next=6,(0,b.querySetmealDishById)({id:l.id});case 6:n=t.sent,1===n.code&&(a.openDetailPop=!0,a.dishMealData=n.data),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](3),console.error("openDetailHandle: 套餐详情加载失败:",t.t0),e.showToast({title:"套餐详情加载失败",icon:"none"});case 14:return t.prev=14,a.setLodding(!1),t.finish(14);case 17:t.next=20;break;case 19:a.openDetailPop=!0;case 20:case"end":return t.stop()}}),t,null,[[3,10,14,17]])})))()},dishClose:function(){this.openDetailPop=!1},moreNormDataesHandle:function(e){var l=this;this.flavorDataes.splice(0),this.moreNormDishdata=e,this.openDetailPop=!1,this.openMoreNormPop=!0,this.moreNormdata=e.flavors.map((function(e){return p(p({},e),{},{value:JSON.parse(e.value)})})),this.moreNormdata.forEach((function(e){e.value&&e.value.length>0&&l.flavorDataes.push(e.value[0])}))},checkMoreNormPop:function(e){var l,a=this,t=e.obj,n=e.item,u=t.some((function(e){return l=a.flavorDataes.findIndex((function(l){return l==e})),-1!=l})),r=this.flavorDataes.findIndex((function(e){return e==n}));-1!=r||u?u?(this.flavorDataes.splice(l,1),this.flavorDataes.push(n)):this.flavorDataes.splice(r,1):this.flavorDataes.push(n)},closeMoreNorm:function(e){this.flavorDataes.splice(0,this.flavorDataes.length),this.openMoreNormPop=!1},computOrderInfo:function(){var e=this,l=this.orderListDataes;this.orderDishNumber=this.orderDishPrice=0,l.map((function(l,a){e.orderDishNumber+=l.number,e.orderDishPrice+=l.number*l.amount})),this.orderDishPrice=this.orderDishPrice},setOrderNum:function(){var e,l=this.dishListData,a=this.orderListDataes;(l&&l.map((function(e,l){e.dishNumber=0,e.flavors&&e.flavors.forEach((function(l,a){""===l.name&&e.flavors.splice(a,1)})),a.length>0&&a&&a.forEach((function(l,a){e.id===l.dishId&&(e.dishNumber=l.number),e.id===l.setmealId&&(e.dishNumber=l.number)}))})),0==this.dishListItems.length)?this.dishListItems=l:(e=this.dishListItems).splice.apply(e,[0,this.dishListItems.length].concat((0,o.default)(l)))},handlePhone:function(e){this.phoneData=this.currentShopInfo.phoneNumber,this.$refs.phone.$refs.popup.open(e)},closePopup:function(e){this.$refs.phone.$refs.popup.close(e)},disabledScroll:function(){return!1},testLoading:function(){var e=this;this.setLodding(!0),setTimeout((function(){e.setLodding(!1)}),3e3)},updateShopInfoToStore:function(){var e=this.currentShopInfo,l=this.mid||"2";this.setShopInfo({shopName:e.shopName,shopAddress:e.shopAddress,shopId:l,phoneNumber:e.phoneNumber}),this.phoneData=e.phoneNumber,console.log("店铺信息已更新 (mid="+l+"):",e)},testSwitchMid:function(e){this.mid=e,s.default.setMid(e),console.log("测试切换mid到:",e)}})};l.default=h}).call(this,a("df3c")["default"],a("3223")["default"])},"2ed8":function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=a("7281"),n=(0,t.createProductPageConfig)(7,"精选美味商品7",48.9);l.default=n},"2fc6":function(e,l,a){},"320a":function(e,l,a){},3223:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],n=["lanDebug","router","worklet"],u="undefined"!==typeof globalThis?globalThis:function(){return this}(),r=["w","x"].join(""),o=u[r],i=o.getLaunchOptionsSync?o.getLaunchOptionsSync():null;function v(e){return(!i||1154!==i.scene||!n.includes(e))&&(t.indexOf(e)>-1||"function"===typeof o[e])}u[r]=function(){var e={};for(var l in o)v(l)&&(e[l]=o[l]);return e}(),u[r].canIUse("getAppBaseInfo")||(u[r].getAppBaseInfo=u[r].getSystemInfoSync),u[r].canIUse("getWindowInfo")||(u[r].getWindowInfo=u[r].getSystemInfoSync),u[r].canIUse("getDeviceInfo")||(u[r].getDeviceInfo=u[r].getSystemInfoSync);var s=u[r];l.default=s},3240:function(e,l,a){"use strict";a.r(l),function(e){
/*!
 * Vue.js v2.6.11
 * (c) 2014-2024 Evan You
 * Released under the MIT License.
 */
var a=Object.freeze({});function t(e){return void 0===e||null===e}function n(e){return void 0!==e&&null!==e}function u(e){return!0===e}function r(e){return"string"===typeof e||"number"===typeof e||"symbol"===typeof e||"boolean"===typeof e}function o(e){return null!==e&&"object"===typeof e}var i=Object.prototype.toString;function v(e){return"[object Object]"===i.call(e)}function s(e){var l=parseFloat(String(e));return l>=0&&Math.floor(l)===l&&isFinite(e)}function b(e){return n(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function c(e){return null==e?"":Array.isArray(e)||v(e)&&e.toString===i?JSON.stringify(e,null,2):String(e)}function f(e){var l=parseFloat(e);return isNaN(l)?e:l}function d(e,l){for(var a=Object.create(null),t=e.split(","),n=0;n<t.length;n++)a[t[n]]=!0;return l?function(e){return a[e.toLowerCase()]}:function(e){return a[e]}}d("slot,component",!0);var p=d("key,ref,slot,slot-scope,is");function h(e,l){if(e.length){var a=e.indexOf(l);if(a>-1)return e.splice(a,1)}}var m=Object.prototype.hasOwnProperty;function g(e,l){return m.call(e,l)}function y(e){var l=Object.create(null);return function(a){var t=l[a];return t||(l[a]=e(a))}}var _=/-(\w)/g,w=y((function(e){return e.replace(_,(function(e,l){return l?l.toUpperCase():""}))})),O=y((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),x=/\B([A-Z])/g,$=y((function(e){return e.replace(x,"-$1").toLowerCase()}));var D=Function.prototype.bind?function(e,l){return e.bind(l)}:function(e,l){function a(a){var t=arguments.length;return t?t>1?e.apply(l,arguments):e.call(l,a):e.call(l)}return a._length=e.length,a};function k(e,l){l=l||0;var a=e.length-l,t=new Array(a);while(a--)t[a]=e[a+l];return t}function S(e,l){for(var a in l)e[a]=l[a];return e}function A(e){for(var l={},a=0;a<e.length;a++)e[a]&&S(l,e[a]);return l}function P(e,l,a){}var j=function(e,l,a){return!1},I=function(e){return e};function T(e,l){if(e===l)return!0;var a=o(e),t=o(l);if(!a||!t)return!a&&!t&&String(e)===String(l);try{var n=Array.isArray(e),u=Array.isArray(l);if(n&&u)return e.length===l.length&&e.every((function(e,a){return T(e,l[a])}));if(e instanceof Date&&l instanceof Date)return e.getTime()===l.getTime();if(n||u)return!1;var r=Object.keys(e),i=Object.keys(l);return r.length===i.length&&r.every((function(a){return T(e[a],l[a])}))}catch(v){return!1}}function E(e,l){for(var a=0;a<e.length;a++)if(T(e[a],l))return a;return-1}function L(e){var l=!1;return function(){l||(l=!0,e.apply(this,arguments))}}var C=["component","directive","filter"],M=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],N={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:j,isReservedAttr:j,isUnknownElement:j,getTagNamespace:P,parsePlatformTagName:I,mustUseProp:j,async:!0,_lifecycleHooks:M},B=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function U(e){var l=(e+"").charCodeAt(0);return 36===l||95===l}function V(e,l,a,t){Object.defineProperty(e,l,{value:a,enumerable:!!t,writable:!0,configurable:!0})}var R=new RegExp("[^"+B.source+".$_\\d]");var H,F="__proto__"in{},q="undefined"!==typeof window,G="undefined"!==typeof WXEnvironment&&!!WXEnvironment.platform,z=G&&WXEnvironment.platform.toLowerCase(),W=q&&window.navigator&&window.navigator.userAgent.toLowerCase(),J=W&&/msie|trident/.test(W),Y=(W&&W.indexOf("msie 9.0"),W&&W.indexOf("edge/")>0),K=(W&&W.indexOf("android"),W&&/iphone|ipad|ipod|ios/.test(W)||"ios"===z),Z=(W&&/chrome\/\d+/.test(W),W&&/phantomjs/.test(W),W&&W.match(/firefox\/(\d+)/),{}.watch);if(q)try{var X={};Object.defineProperty(X,"passive",{get:function(){}}),window.addEventListener("test-passive",null,X)}catch(Ma){}var Q=function(){return void 0===H&&(H=!q&&!G&&"undefined"!==typeof e&&(e["process"]&&"server"===e["process"].env.VUE_ENV)),H},ee=q&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function le(e){return"function"===typeof e&&/native code/.test(e.toString())}var ae,te="undefined"!==typeof Symbol&&le(Symbol)&&"undefined"!==typeof Reflect&&le(Reflect.ownKeys);ae="undefined"!==typeof Set&&le(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var ne=P,ue=0,re=function(){this.id=ue++,this.subs=[]};function oe(e){re.SharedObject.targetStack.push(e),re.SharedObject.target=e,re.target=e}function ie(){re.SharedObject.targetStack.pop(),re.SharedObject.target=re.SharedObject.targetStack[re.SharedObject.targetStack.length-1],re.target=re.SharedObject.target}re.prototype.addSub=function(e){this.subs.push(e)},re.prototype.removeSub=function(e){h(this.subs,e)},re.prototype.depend=function(){re.SharedObject.target&&re.SharedObject.target.addDep(this)},re.prototype.notify=function(){var e=this.subs.slice();for(var l=0,a=e.length;l<a;l++)e[l].update()},re.SharedObject={},re.SharedObject.target=null,re.SharedObject.targetStack=[];var ve=function(e,l,a,t,n,u,r,o){this.tag=e,this.data=l,this.children=a,this.text=t,this.elm=n,this.ns=void 0,this.context=u,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=l&&l.key,this.componentOptions=r,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=o,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},se={child:{configurable:!0}};se.child.get=function(){return this.componentInstance},Object.defineProperties(ve.prototype,se);var be=function(e){void 0===e&&(e="");var l=new ve;return l.text=e,l.isComment=!0,l};function ce(e){return new ve(void 0,void 0,void 0,String(e))}var fe=Array.prototype,de=Object.create(fe);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(e){var l=fe[e];V(de,e,(function(){var a=[],t=arguments.length;while(t--)a[t]=arguments[t];var n,u=l.apply(this,a),r=this.__ob__;switch(e){case"push":case"unshift":n=a;break;case"splice":n=a.slice(2);break}return n&&r.observeArray(n),r.dep.notify(),u}))}));var pe=Object.getOwnPropertyNames(de),he=!0;function me(e){he=e}var ge=function(e){this.value=e,this.dep=new re,this.vmCount=0,V(e,"__ob__",this),Array.isArray(e)?(F?e.push!==e.__proto__.push?ye(e,de,pe):function(e,l){e.__proto__=l}(e,de):ye(e,de,pe),this.observeArray(e)):this.walk(e)};function ye(e,l,a){for(var t=0,n=a.length;t<n;t++){var u=a[t];V(e,u,l[u])}}function _e(e,l){var a;if(o(e)&&!(e instanceof ve))return g(e,"__ob__")&&e.__ob__ instanceof ge?a=e.__ob__:!he||Q()||!Array.isArray(e)&&!v(e)||!Object.isExtensible(e)||e._isVue||e.__v_isMPComponent||(a=new ge(e)),l&&a&&a.vmCount++,a}function we(e,l,a,t,n){var u=new re,r=Object.getOwnPropertyDescriptor(e,l);if(!r||!1!==r.configurable){var o=r&&r.get,i=r&&r.set;o&&!i||2!==arguments.length||(a=e[l]);var v=!n&&_e(a);Object.defineProperty(e,l,{enumerable:!0,configurable:!0,get:function(){var l=o?o.call(e):a;return re.SharedObject.target&&(u.depend(),v&&(v.dep.depend(),Array.isArray(l)&&$e(l))),l},set:function(l){var t=o?o.call(e):a;l===t||l!==l&&t!==t||o&&!i||(i?i.call(e,l):a=l,v=!n&&_e(l),u.notify())}})}}function Oe(e,l,a){if(Array.isArray(e)&&s(l))return e.length=Math.max(e.length,l),e.splice(l,1,a),a;if(l in e&&!(l in Object.prototype))return e[l]=a,a;var t=e.__ob__;return e._isVue||t&&t.vmCount?a:t?(we(t.value,l,a),t.dep.notify(),a):(e[l]=a,a)}function xe(e,l){if(Array.isArray(e)&&s(l))e.splice(l,1);else{var a=e.__ob__;e._isVue||a&&a.vmCount||g(e,l)&&(delete e[l],a&&a.dep.notify())}}function $e(e){for(var l=void 0,a=0,t=e.length;a<t;a++)l=e[a],l&&l.__ob__&&l.__ob__.dep.depend(),Array.isArray(l)&&$e(l)}ge.prototype.walk=function(e){for(var l=Object.keys(e),a=0;a<l.length;a++)we(e,l[a])},ge.prototype.observeArray=function(e){for(var l=0,a=e.length;l<a;l++)_e(e[l])};var De=N.optionMergeStrategies;function ke(e,l){if(!l)return e;for(var a,t,n,u=te?Reflect.ownKeys(l):Object.keys(l),r=0;r<u.length;r++)a=u[r],"__ob__"!==a&&(t=e[a],n=l[a],g(e,a)?t!==n&&v(t)&&v(n)&&ke(t,n):Oe(e,a,n));return e}function Se(e,l,a){return a?function(){var t="function"===typeof l?l.call(a,a):l,n="function"===typeof e?e.call(a,a):e;return t?ke(t,n):n}:l?e?function(){return ke("function"===typeof l?l.call(this,this):l,"function"===typeof e?e.call(this,this):e)}:l:e}function Ae(e,l){var a=l?e?e.concat(l):Array.isArray(l)?l:[l]:e;return a?function(e){for(var l=[],a=0;a<e.length;a++)-1===l.indexOf(e[a])&&l.push(e[a]);return l}(a):a}function Pe(e,l,a,t){var n=Object.create(e||null);return l?S(n,l):n}De.data=function(e,l,a){return a?Se(e,l,a):l&&"function"!==typeof l?e:Se(e,l)},M.forEach((function(e){De[e]=Ae})),C.forEach((function(e){De[e+"s"]=Pe})),De.watch=function(e,l,a,t){if(e===Z&&(e=void 0),l===Z&&(l=void 0),!l)return Object.create(e||null);if(!e)return l;var n={};for(var u in S(n,e),l){var r=n[u],o=l[u];r&&!Array.isArray(r)&&(r=[r]),n[u]=r?r.concat(o):Array.isArray(o)?o:[o]}return n},De.props=De.methods=De.inject=De.computed=function(e,l,a,t){if(!e)return l;var n=Object.create(null);return S(n,e),l&&S(n,l),n},De.provide=Se;var je=function(e,l){return void 0===l?e:l};function Ie(e,l,a){if("function"===typeof l&&(l=l.options),function(e,l){var a=e.props;if(a){var t,n,u,r={};if(Array.isArray(a)){t=a.length;while(t--)n=a[t],"string"===typeof n&&(u=w(n),r[u]={type:null})}else if(v(a))for(var o in a)n=a[o],u=w(o),r[u]=v(n)?n:{type:n};else 0;e.props=r}}(l),function(e,l){var a=e.inject;if(a){var t=e.inject={};if(Array.isArray(a))for(var n=0;n<a.length;n++)t[a[n]]={from:a[n]};else if(v(a))for(var u in a){var r=a[u];t[u]=v(r)?S({from:u},r):{from:r}}else 0}}(l),function(e){var l=e.directives;if(l)for(var a in l){var t=l[a];"function"===typeof t&&(l[a]={bind:t,update:t})}}(l),!l._base&&(l.extends&&(e=Ie(e,l.extends,a)),l.mixins))for(var t=0,n=l.mixins.length;t<n;t++)e=Ie(e,l.mixins[t],a);var u,r={};for(u in e)o(u);for(u in l)g(e,u)||o(u);function o(t){var n=De[t]||je;r[t]=n(e[t],l[t],a,t)}return r}function Te(e,l,a,t){if("string"===typeof a){var n=e[l];if(g(n,a))return n[a];var u=w(a);if(g(n,u))return n[u];var r=O(u);if(g(n,r))return n[r];var o=n[a]||n[u]||n[r];return o}}function Ee(e,l,a,t){var n=l[e],u=!g(a,e),r=a[e],o=Me(Boolean,n.type);if(o>-1)if(u&&!g(n,"default"))r=!1;else if(""===r||r===$(e)){var i=Me(String,n.type);(i<0||o<i)&&(r=!0)}if(void 0===r){r=function(e,l,a){if(!g(l,"default"))return;var t=l.default;0;if(e&&e.$options.propsData&&void 0===e.$options.propsData[a]&&void 0!==e._props[a])return e._props[a];return"function"===typeof t&&"Function"!==Le(l.type)?t.call(e):t}(t,n,e);var v=he;me(!0),_e(r),me(v)}return r}function Le(e){var l=e&&e.toString().match(/^\s*function (\w+)/);return l?l[1]:""}function Ce(e,l){return Le(e)===Le(l)}function Me(e,l){if(!Array.isArray(l))return Ce(l,e)?0:-1;for(var a=0,t=l.length;a<t;a++)if(Ce(l[a],e))return a;return-1}function Ne(e,l,a){oe();try{if(l){var t=l;while(t=t.$parent){var n=t.$options.errorCaptured;if(n)for(var u=0;u<n.length;u++)try{var r=!1===n[u].call(t,e,l,a);if(r)return}catch(Ma){Ue(Ma,t,"errorCaptured hook")}}}Ue(e,l,a)}finally{ie()}}function Be(e,l,a,t,n){var u;try{u=a?e.apply(l,a):e.call(l),u&&!u._isVue&&b(u)&&!u._handled&&(u.catch((function(e){return Ne(e,t,n+" (Promise/async)")})),u._handled=!0)}catch(Ma){Ne(Ma,t,n)}return u}function Ue(e,l,a){if(N.errorHandler)try{return N.errorHandler.call(null,e,l,a)}catch(Ma){Ma!==e&&Ve(Ma,null,"config.errorHandler")}Ve(e,l,a)}function Ve(e,l,a){if(!q&&!G||"undefined"===typeof console)throw e;console.error(e)}var Re,He=[],Fe=!1;function qe(){Fe=!1;var e=He.slice(0);He.length=0;for(var l=0;l<e.length;l++)e[l]()}if("undefined"!==typeof Promise&&le(Promise)){var Ge=Promise.resolve();Re=function(){Ge.then(qe),K&&setTimeout(P)}}else if(J||"undefined"===typeof MutationObserver||!le(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Re="undefined"!==typeof setImmediate&&le(setImmediate)?function(){setImmediate(qe)}:function(){setTimeout(qe,0)};else{var ze=1,We=new MutationObserver(qe),Je=document.createTextNode(String(ze));We.observe(Je,{characterData:!0}),Re=function(){ze=(ze+1)%2,Je.data=String(ze)}}function Ye(e,l){var a;if(He.push((function(){if(e)try{e.call(l)}catch(Ma){Ne(Ma,l,"nextTick")}else a&&a(l)})),Fe||(Fe=!0,Re()),!e&&"undefined"!==typeof Promise)return new Promise((function(e){a=e}))}var Ke=new ae;function Ze(e){(function e(l,a){var t,n,u=Array.isArray(l);if(!u&&!o(l)||Object.isFrozen(l)||l instanceof ve)return;if(l.__ob__){var r=l.__ob__.dep.id;if(a.has(r))return;a.add(r)}if(u){t=l.length;while(t--)e(l[t],a)}else{n=Object.keys(l),t=n.length;while(t--)e(l[n[t]],a)}})(e,Ke),Ke.clear()}var Xe=y((function(e){var l="&"===e.charAt(0);e=l?e.slice(1):e;var a="~"===e.charAt(0);e=a?e.slice(1):e;var t="!"===e.charAt(0);return e=t?e.slice(1):e,{name:e,once:a,capture:t,passive:l}}));function Qe(e,l){function a(){var e=arguments,t=a.fns;if(!Array.isArray(t))return Be(t,null,arguments,l,"v-on handler");for(var n=t.slice(),u=0;u<n.length;u++)Be(n[u],null,e,l,"v-on handler")}return a.fns=e,a}function el(e,l,a,u){var r=l.options.mpOptions&&l.options.mpOptions.properties;if(t(r))return a;var o=l.options.mpOptions.externalClasses||[],i=e.attrs,v=e.props;if(n(i)||n(v))for(var s in r){var b=$(s),c=ll(a,v,s,b,!0)||ll(a,i,s,b,!1);c&&a[s]&&-1!==o.indexOf(b)&&u[w(a[s])]&&(a[s]=u[w(a[s])])}return a}function ll(e,l,a,t,u){if(n(l)){if(g(l,a))return e[a]=l[a],u||delete l[a],!0;if(g(l,t))return e[a]=l[t],u||delete l[t],!0}return!1}function al(e){return r(e)?[ce(e)]:Array.isArray(e)?function e(l,a){var o,i,v,s,b=[];for(o=0;o<l.length;o++)i=l[o],t(i)||"boolean"===typeof i||(v=b.length-1,s=b[v],Array.isArray(i)?i.length>0&&(i=e(i,(a||"")+"_"+o),tl(i[0])&&tl(s)&&(b[v]=ce(s.text+i[0].text),i.shift()),b.push.apply(b,i)):r(i)?tl(s)?b[v]=ce(s.text+i):""!==i&&b.push(ce(i)):tl(i)&&tl(s)?b[v]=ce(s.text+i.text):(u(l._isVList)&&n(i.tag)&&t(i.key)&&n(a)&&(i.key="__vlist"+a+"_"+o+"__"),b.push(i)));return b}(e):void 0}function tl(e){return n(e)&&n(e.text)&&function(e){return!1===e}(e.isComment)}function nl(e){var l=e.$options.provide;l&&(e._provided="function"===typeof l?l.call(e):l)}function ul(e){var l=rl(e.$options.inject,e);l&&(me(!1),Object.keys(l).forEach((function(a){we(e,a,l[a])})),me(!0))}function rl(e,l){if(e){for(var a=Object.create(null),t=te?Reflect.ownKeys(e):Object.keys(e),n=0;n<t.length;n++){var u=t[n];if("__ob__"!==u){var r=e[u].from,o=l;while(o){if(o._provided&&g(o._provided,r)){a[u]=o._provided[r];break}o=o.$parent}if(!o)if("default"in e[u]){var i=e[u].default;a[u]="function"===typeof i?i.call(l):i}else 0}}return a}}function ol(e,l){if(!e||!e.length)return{};for(var a={},t=0,n=e.length;t<n;t++){var u=e[t],r=u.data;if(r&&r.attrs&&r.attrs.slot&&delete r.attrs.slot,u.context!==l&&u.fnContext!==l||!r||null==r.slot)u.asyncMeta&&u.asyncMeta.data&&"page"===u.asyncMeta.data.slot?(a["page"]||(a["page"]=[])).push(u):(a.default||(a.default=[])).push(u);else{var o=r.slot,i=a[o]||(a[o]=[]);"template"===u.tag?i.push.apply(i,u.children||[]):i.push(u)}}for(var v in a)a[v].every(il)&&delete a[v];return a}function il(e){return e.isComment&&!e.asyncFactory||" "===e.text}function vl(e,l,t){var n,u=Object.keys(l).length>0,r=e?!!e.$stable:!u,o=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(r&&t&&t!==a&&o===t.$key&&!u&&!t.$hasNormal)return t;for(var i in n={},e)e[i]&&"$"!==i[0]&&(n[i]=sl(l,i,e[i]))}else n={};for(var v in l)v in n||(n[v]=bl(l,v));return e&&Object.isExtensible(e)&&(e._normalized=n),V(n,"$stable",r),V(n,"$key",o),V(n,"$hasNormal",u),n}function sl(e,l,a){var t=function(){var e=arguments.length?a.apply(null,arguments):a({});return e=e&&"object"===typeof e&&!Array.isArray(e)?[e]:al(e),e&&(0===e.length||1===e.length&&e[0].isComment)?void 0:e};return a.proxy&&Object.defineProperty(e,l,{get:t,enumerable:!0,configurable:!0}),t}function bl(e,l){return function(){return e[l]}}function cl(e,l){var a,t,u,r,i;if(Array.isArray(e)||"string"===typeof e)for(a=new Array(e.length),t=0,u=e.length;t<u;t++)a[t]=l(e[t],t,t,t);else if("number"===typeof e)for(a=new Array(e),t=0;t<e;t++)a[t]=l(t+1,t,t,t);else if(o(e))if(te&&e[Symbol.iterator]){a=[];var v=e[Symbol.iterator](),s=v.next();while(!s.done)a.push(l(s.value,a.length,t,t++)),s=v.next()}else for(r=Object.keys(e),a=new Array(r.length),t=0,u=r.length;t<u;t++)i=r[t],a[t]=l(e[i],i,t,t);return n(a)||(a=[]),a._isVList=!0,a}function fl(e,l,a,t){var n,u=this.$scopedSlots[e];u?(a=a||{},t&&(a=S(S({},t),a)),n=u(a,this,a._i)||l):n=this.$slots[e]||l;var r=a&&a.slot;return r?this.$createElement("template",{slot:r},n):n}function dl(e){return Te(this.$options,"filters",e)||I}function pl(e,l){return Array.isArray(e)?-1===e.indexOf(l):e!==l}function hl(e,l,a,t,n){var u=N.keyCodes[l]||a;return n&&t&&!N.keyCodes[l]?pl(n,t):u?pl(u,e):t?$(t)!==l:void 0}function ml(e,l,a,t,n){if(a)if(o(a)){var u;Array.isArray(a)&&(a=A(a));var r=function(r){if("class"===r||"style"===r||p(r))u=e;else{var o=e.attrs&&e.attrs.type;u=t||N.mustUseProp(l,o,r)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var i=w(r),v=$(r);if(!(i in u)&&!(v in u)&&(u[r]=a[r],n)){var s=e.on||(e.on={});s["update:"+r]=function(e){a[r]=e}}};for(var i in a)r(i)}else;return e}function gl(e,l){var a=this._staticTrees||(this._staticTrees=[]),t=a[e];return t&&!l||(t=a[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),_l(t,"__static__"+e,!1)),t}function yl(e,l,a){return _l(e,"__once__"+l+(a?"_"+a:""),!0),e}function _l(e,l,a){if(Array.isArray(e))for(var t=0;t<e.length;t++)e[t]&&"string"!==typeof e[t]&&wl(e[t],l+"_"+t,a);else wl(e,l,a)}function wl(e,l,a){e.isStatic=!0,e.key=l,e.isOnce=a}function Ol(e,l){if(l)if(v(l)){var a=e.on=e.on?S({},e.on):{};for(var t in l){var n=a[t],u=l[t];a[t]=n?[].concat(n,u):u}}else;return e}function xl(e,l,a,t){l=l||{$stable:!a};for(var n=0;n<e.length;n++){var u=e[n];Array.isArray(u)?xl(u,l,a):u&&(u.proxy&&(u.fn.proxy=!0),l[u.key]=u.fn)}return t&&(l.$key=t),l}function $l(e,l){for(var a=0;a<l.length;a+=2){var t=l[a];"string"===typeof t&&t&&(e[l[a]]=l[a+1])}return e}function Dl(e,l){return"string"===typeof e?l+e:e}function kl(e){e._o=yl,e._n=f,e._s=c,e._l=cl,e._t=fl,e._q=T,e._i=E,e._m=gl,e._f=dl,e._k=hl,e._b=ml,e._v=ce,e._e=be,e._u=xl,e._g=Ol,e._d=$l,e._p=Dl}function Sl(e,l,t,n,r){var o,i=this,v=r.options;g(n,"_uid")?(o=Object.create(n),o._original=n):(o=n,n=n._original);var s=u(v._compiled),b=!s;this.data=e,this.props=l,this.children=t,this.parent=n,this.listeners=e.on||a,this.injections=rl(v.inject,n),this.slots=function(){return i.$slots||vl(e.scopedSlots,i.$slots=ol(t,n)),i.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return vl(e.scopedSlots,this.slots())}}),s&&(this.$options=v,this.$slots=this.slots(),this.$scopedSlots=vl(e.scopedSlots,this.$slots)),v._scopeId?this._c=function(e,l,a,t){var u=Ll(o,e,l,a,t,b);return u&&!Array.isArray(u)&&(u.fnScopeId=v._scopeId,u.fnContext=n),u}:this._c=function(e,l,a,t){return Ll(o,e,l,a,t,b)}}function Al(e,l,a,t,n){var u=function(e){var l=new ve(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return l.ns=e.ns,l.isStatic=e.isStatic,l.key=e.key,l.isComment=e.isComment,l.fnContext=e.fnContext,l.fnOptions=e.fnOptions,l.fnScopeId=e.fnScopeId,l.asyncMeta=e.asyncMeta,l.isCloned=!0,l}(e);return u.fnContext=a,u.fnOptions=t,l.slot&&((u.data||(u.data={})).slot=l.slot),u}function Pl(e,l){for(var a in l)e[w(a)]=l[a]}kl(Sl.prototype);var jl={init:function(e,l){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var a=e;jl.prepatch(a,a)}else{var t=e.componentInstance=function(e,l){var a={_isComponent:!0,_parentVnode:e,parent:l},t=e.data.inlineTemplate;n(t)&&(a.render=t.render,a.staticRenderFns=t.staticRenderFns);return new e.componentOptions.Ctor(a)}(e,Fl);t.$mount(l?e.elm:void 0,l)}},prepatch:function(e,l){var t=l.componentOptions,n=l.componentInstance=e.componentInstance;(function(e,l,t,n,u){0;var r=n.data.scopedSlots,o=e.$scopedSlots,i=!!(r&&!r.$stable||o!==a&&!o.$stable||r&&e.$scopedSlots.$key!==r.$key),v=!!(u||e.$options._renderChildren||i);e.$options._parentVnode=n,e.$vnode=n,e._vnode&&(e._vnode.parent=n);if(e.$options._renderChildren=u,e.$attrs=n.data.attrs||a,e.$listeners=t||a,l&&e.$options.props){me(!1);for(var s=e._props,b=e.$options._propKeys||[],c=0;c<b.length;c++){var f=b[c],d=e.$options.props;s[f]=Ee(f,d,l,e)}me(!0),e.$options.propsData=l}e._$updateProperties&&e._$updateProperties(e),t=t||a;var p=e.$options._parentListeners;e.$options._parentListeners=t,Hl(e,t,p),v&&(e.$slots=ol(u,n.context),e.$forceUpdate());0})(n,t.propsData,t.listeners,l,t.children)},insert:function(e){var l=e.context,a=e.componentInstance;a._isMounted||(zl(a,"onServiceCreated"),zl(a,"onServiceAttached"),a._isMounted=!0,zl(a,"mounted")),e.data.keepAlive&&(l._isMounted?function(e){e._inactive=!1,Jl.push(e)}(a):Gl(a,!0))},destroy:function(e){var l=e.componentInstance;l._isDestroyed||(e.data.keepAlive?function e(l,a){if(a&&(l._directInactive=!0,ql(l)))return;if(!l._inactive){l._inactive=!0;for(var t=0;t<l.$children.length;t++)e(l.$children[t]);zl(l,"deactivated")}}(l,!0):l.$destroy())}},Il=Object.keys(jl);function Tl(e,l,r,i,v){if(!t(e)){var s=r.$options._base;if(o(e)&&(e=s.extend(e)),"function"===typeof e){var c;if(t(e.cid)&&(c=e,e=function(e,l){if(u(e.error)&&n(e.errorComp))return e.errorComp;if(n(e.resolved))return e.resolved;var a=Ml;a&&n(e.owners)&&-1===e.owners.indexOf(a)&&e.owners.push(a);if(u(e.loading)&&n(e.loadingComp))return e.loadingComp;if(a&&!n(e.owners)){var r=e.owners=[a],i=!0,v=null,s=null;a.$on("hook:destroyed",(function(){return h(r,a)}));var c=function(e){for(var l=0,a=r.length;l<a;l++)r[l].$forceUpdate();e&&(r.length=0,null!==v&&(clearTimeout(v),v=null),null!==s&&(clearTimeout(s),s=null))},f=L((function(a){e.resolved=Nl(a,l),i?r.length=0:c(!0)})),d=L((function(l){n(e.errorComp)&&(e.error=!0,c(!0))})),p=e(f,d);return o(p)&&(b(p)?t(e.resolved)&&p.then(f,d):b(p.component)&&(p.component.then(f,d),n(p.error)&&(e.errorComp=Nl(p.error,l)),n(p.loading)&&(e.loadingComp=Nl(p.loading,l),0===p.delay?e.loading=!0:v=setTimeout((function(){v=null,t(e.resolved)&&t(e.error)&&(e.loading=!0,c(!1))}),p.delay||200)),n(p.timeout)&&(s=setTimeout((function(){s=null,t(e.resolved)&&d(null)}),p.timeout)))),i=!1,e.loading?e.loadingComp:e.resolved}}(c,s),void 0===e))return function(e,l,a,t,n){var u=be();return u.asyncFactory=e,u.asyncMeta={data:l,context:a,children:t,tag:n},u}(c,l,r,i,v);l=l||{},fa(e),n(l.model)&&function(e,l){var a=e.model&&e.model.prop||"value",t=e.model&&e.model.event||"input";(l.attrs||(l.attrs={}))[a]=l.model.value;var u=l.on||(l.on={}),r=u[t],o=l.model.callback;n(r)?(Array.isArray(r)?-1===r.indexOf(o):r!==o)&&(u[t]=[o].concat(r)):u[t]=o}(e.options,l);var f=function(e,l,a,u){var r=l.options.props;if(t(r))return el(e,l,{},u);var o={},i=e.attrs,v=e.props;if(n(i)||n(v))for(var s in r){var b=$(s);ll(o,v,s,b,!0)||ll(o,i,s,b,!1)}return el(e,l,o,u)}(l,e,0,r);if(u(e.options.functional))return function(e,l,t,u,r){var o=e.options,i={},v=o.props;if(n(v))for(var s in v)i[s]=Ee(s,v,l||a);else n(t.attrs)&&Pl(i,t.attrs),n(t.props)&&Pl(i,t.props);var b=new Sl(t,i,r,u,e),c=o.render.call(null,b._c,b);if(c instanceof ve)return Al(c,t,b.parent,o,b);if(Array.isArray(c)){for(var f=al(c)||[],d=new Array(f.length),p=0;p<f.length;p++)d[p]=Al(f[p],t,b.parent,o,b);return d}}(e,f,l,r,i);var d=l.on;if(l.on=l.nativeOn,u(e.options.abstract)){var p=l.slot;l={},p&&(l.slot=p)}(function(e){for(var l=e.hook||(e.hook={}),a=0;a<Il.length;a++){var t=Il[a],n=l[t],u=jl[t];n===u||n&&n._merged||(l[t]=n?El(u,n):u)}})(l);var m=e.options.name||v,g=new ve("vue-component-"+e.cid+(m?"-"+m:""),l,void 0,void 0,void 0,r,{Ctor:e,propsData:f,listeners:d,tag:v,children:i},c);return g}}}function El(e,l){var a=function(a,t){e(a,t),l(a,t)};return a._merged=!0,a}function Ll(e,l,a,i,v,s){return(Array.isArray(a)||r(a))&&(v=i,i=a,a=void 0),u(s)&&(v=2),function(e,l,a,r,i){if(n(a)&&n(a.__ob__))return be();n(a)&&n(a.is)&&(l=a.is);if(!l)return be();0;Array.isArray(r)&&"function"===typeof r[0]&&(a=a||{},a.scopedSlots={default:r[0]},r.length=0);2===i?r=al(r):1===i&&(r=function(e){for(var l=0;l<e.length;l++)if(Array.isArray(e[l]))return Array.prototype.concat.apply([],e);return e}(r));var v,s;if("string"===typeof l){var b;s=e.$vnode&&e.$vnode.ns||N.getTagNamespace(l),v=N.isReservedTag(l)?new ve(N.parsePlatformTagName(l),a,r,void 0,void 0,e):a&&a.pre||!n(b=Te(e.$options,"components",l))?new ve(l,a,r,void 0,void 0,e):Tl(b,a,e,r,l)}else v=Tl(l,a,e,r);return Array.isArray(v)?v:n(v)?(n(s)&&function e(l,a,r){l.ns=a,"foreignObject"===l.tag&&(a=void 0,r=!0);if(n(l.children))for(var o=0,i=l.children.length;o<i;o++){var v=l.children[o];n(v.tag)&&(t(v.ns)||u(r)&&"svg"!==v.tag)&&e(v,a,r)}}(v,s),n(a)&&function(e){o(e.style)&&Ze(e.style);o(e.class)&&Ze(e.class)}(a),v):be()}(e,l,a,i,v)}var Cl,Ml=null;function Nl(e,l){return(e.__esModule||te&&"Module"===e[Symbol.toStringTag])&&(e=e.default),o(e)?l.extend(e):e}function Bl(e){return e.isComment&&e.asyncFactory}function Ul(e,l){Cl.$on(e,l)}function Vl(e,l){Cl.$off(e,l)}function Rl(e,l){var a=Cl;return function t(){var n=l.apply(null,arguments);null!==n&&a.$off(e,t)}}function Hl(e,l,a){Cl=e,function(e,l,a,n,r,o){var i,v,s,b;for(i in e)v=e[i],s=l[i],b=Xe(i),t(v)||(t(s)?(t(v.fns)&&(v=e[i]=Qe(v,o)),u(b.once)&&(v=e[i]=r(b.name,v,b.capture)),a(b.name,v,b.capture,b.passive,b.params)):v!==s&&(s.fns=v,e[i]=s));for(i in l)t(e[i])&&(b=Xe(i),n(b.name,l[i],b.capture))}(l,a||{},Ul,Vl,Rl,e),Cl=void 0}var Fl=null;function ql(e){while(e&&(e=e.$parent))if(e._inactive)return!0;return!1}function Gl(e,l){if(l){if(e._directInactive=!1,ql(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var a=0;a<e.$children.length;a++)Gl(e.$children[a]);zl(e,"activated")}}function zl(e,l){oe();var a=e.$options[l],t=l+" hook";if(a)for(var n=0,u=a.length;n<u;n++)Be(a[n],e,null,e,t);e._hasHookEvent&&e.$emit("hook:"+l),ie()}var Wl=[],Jl=[],Yl={},Kl=!1,Zl=!1,Xl=0;var Ql=Date.now;if(q&&!J){var ea=window.performance;ea&&"function"===typeof ea.now&&Ql()>document.createEvent("Event").timeStamp&&(Ql=function(){return ea.now()})}function la(){var e,l;for(Ql(),Zl=!0,Wl.sort((function(e,l){return e.id-l.id})),Xl=0;Xl<Wl.length;Xl++)e=Wl[Xl],e.before&&e.before(),l=e.id,Yl[l]=null,e.run();var a=Jl.slice(),t=Wl.slice();(function(){Xl=Wl.length=Jl.length=0,Yl={},Kl=Zl=!1})(),function(e){for(var l=0;l<e.length;l++)e[l]._inactive=!0,Gl(e[l],!0)}(a),function(e){var l=e.length;while(l--){var a=e[l],t=a.vm;t._watcher===a&&t._isMounted&&!t._isDestroyed&&zl(t,"updated")}}(t),ee&&N.devtools&&ee.emit("flush")}var aa=0,ta=function(e,l,a,t,n){this.vm=e,n&&(e._watcher=this),e._watchers.push(this),t?(this.deep=!!t.deep,this.user=!!t.user,this.lazy=!!t.lazy,this.sync=!!t.sync,this.before=t.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=a,this.id=++aa,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new ae,this.newDepIds=new ae,this.expression="","function"===typeof l?this.getter=l:(this.getter=function(e){if(!R.test(e)){var l=e.split(".");return function(e){for(var a=0;a<l.length;a++){if(!e)return;e=e[l[a]]}return e}}}(l),this.getter||(this.getter=P)),this.value=this.lazy?void 0:this.get()};ta.prototype.get=function(){var e;oe(this);var l=this.vm;try{e=this.getter.call(l,l)}catch(Ma){if(!this.user)throw Ma;Ne(Ma,l,'getter for watcher "'+this.expression+'"')}finally{this.deep&&Ze(e),ie(),this.cleanupDeps()}return e},ta.prototype.addDep=function(e){var l=e.id;this.newDepIds.has(l)||(this.newDepIds.add(l),this.newDeps.push(e),this.depIds.has(l)||e.addSub(this))},ta.prototype.cleanupDeps=function(){var e=this.deps.length;while(e--){var l=this.deps[e];this.newDepIds.has(l.id)||l.removeSub(this)}var a=this.depIds;this.depIds=this.newDepIds,this.newDepIds=a,this.newDepIds.clear(),a=this.deps,this.deps=this.newDeps,this.newDeps=a,this.newDeps.length=0},ta.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var l=e.id;if(null==Yl[l]){if(Yl[l]=!0,Zl){var a=Wl.length-1;while(a>Xl&&Wl[a].id>e.id)a--;Wl.splice(a+1,0,e)}else Wl.push(e);Kl||(Kl=!0,Ye(la))}}(this)},ta.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||o(e)||this.deep){var l=this.value;if(this.value=e,this.user)try{this.cb.call(this.vm,e,l)}catch(Ma){Ne(Ma,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,e,l)}}},ta.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},ta.prototype.depend=function(){var e=this.deps.length;while(e--)this.deps[e].depend()},ta.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||h(this.vm._watchers,this);var e=this.deps.length;while(e--)this.deps[e].removeSub(this);this.active=!1}};var na={enumerable:!0,configurable:!0,get:P,set:P};function ua(e,l,a){na.get=function(){return this[l][a]},na.set=function(e){this[l][a]=e},Object.defineProperty(e,a,na)}function ra(e){e._watchers=[];var l=e.$options;l.props&&function(e,l){var a=e.$options.propsData||{},t=e._props={},n=e.$options._propKeys=[],u=!e.$parent;u||me(!1);var r=function(u){n.push(u);var r=Ee(u,l,a,e);we(t,u,r),u in e||ua(e,"_props",u)};for(var o in l)r(o);me(!0)}(e,l.props),l.methods&&function(e,l){e.$options.props;for(var a in l)e[a]="function"!==typeof l[a]?P:D(l[a],e)}(e,l.methods),l.data?function(e){var l=e.$options.data;l=e._data="function"===typeof l?function(e,l){oe();try{return e.call(l,l)}catch(Ma){return Ne(Ma,l,"data()"),{}}finally{ie()}}(l,e):l||{},v(l)||(l={});var a=Object.keys(l),t=e.$options.props,n=(e.$options.methods,a.length);while(n--){var u=a[n];0,t&&g(t,u)||U(u)||ua(e,"_data",u)}_e(l,!0)}(e):_e(e._data={},!0),l.computed&&function(e,l){var a=e._computedWatchers=Object.create(null),t=Q();for(var n in l){var u=l[n],r="function"===typeof u?u:u.get;0,t||(a[n]=new ta(e,r||P,P,oa)),n in e||ia(e,n,u)}}(e,l.computed),l.watch&&l.watch!==Z&&function(e,l){for(var a in l){var t=l[a];if(Array.isArray(t))for(var n=0;n<t.length;n++)ba(e,a,t[n]);else ba(e,a,t)}}(e,l.watch)}var oa={lazy:!0};function ia(e,l,a){var t=!Q();"function"===typeof a?(na.get=t?va(l):sa(a),na.set=P):(na.get=a.get?t&&!1!==a.cache?va(l):sa(a.get):P,na.set=a.set||P),Object.defineProperty(e,l,na)}function va(e){return function(){var l=this._computedWatchers&&this._computedWatchers[e];if(l)return l.dirty&&l.evaluate(),re.SharedObject.target&&l.depend(),l.value}}function sa(e){return function(){return e.call(this,this)}}function ba(e,l,a,t){return v(a)&&(t=a,a=a.handler),"string"===typeof a&&(a=e[a]),e.$watch(l,a,t)}var ca=0;function fa(e){var l=e.options;if(e.super){var a=fa(e.super),t=e.superOptions;if(a!==t){e.superOptions=a;var n=function(e){var l,a=e.options,t=e.sealedOptions;for(var n in a)a[n]!==t[n]&&(l||(l={}),l[n]=a[n]);return l}(e);n&&S(e.extendOptions,n),l=e.options=Ie(a,e.extendOptions),l.name&&(l.components[l.name]=e)}}return l}function da(e){this._init(e)}function pa(e){e.cid=0;var l=1;e.extend=function(e){e=e||{};var a=this,t=a.cid,n=e._Ctor||(e._Ctor={});if(n[t])return n[t];var u=e.name||a.options.name;var r=function(e){this._init(e)};return r.prototype=Object.create(a.prototype),r.prototype.constructor=r,r.cid=l++,r.options=Ie(a.options,e),r["super"]=a,r.options.props&&function(e){var l=e.options.props;for(var a in l)ua(e.prototype,"_props",a)}(r),r.options.computed&&function(e){var l=e.options.computed;for(var a in l)ia(e.prototype,a,l[a])}(r),r.extend=a.extend,r.mixin=a.mixin,r.use=a.use,C.forEach((function(e){r[e]=a[e]})),u&&(r.options.components[u]=r),r.superOptions=a.options,r.extendOptions=e,r.sealedOptions=S({},r.options),n[t]=r,r}}function ha(e){return e&&(e.Ctor.options.name||e.tag)}function ma(e,l){return Array.isArray(e)?e.indexOf(l)>-1:"string"===typeof e?e.split(",").indexOf(l)>-1:!!function(e){return"[object RegExp]"===i.call(e)}(e)&&e.test(l)}function ga(e,l){var a=e.cache,t=e.keys,n=e._vnode;for(var u in a){var r=a[u];if(r){var o=ha(r.componentOptions);o&&!l(o)&&ya(a,u,t,n)}}}function ya(e,l,a,t){var n=e[l];!n||t&&n.tag===t.tag||n.componentInstance.$destroy(),e[l]=null,h(a,l)}(function(e){e.prototype._init=function(e){var l=this;l._uid=ca++,l._isVue=!0,e&&e._isComponent?function(e,l){var a=e.$options=Object.create(e.constructor.options),t=l._parentVnode;a.parent=l.parent,a._parentVnode=t;var n=t.componentOptions;a.propsData=n.propsData,a._parentListeners=n.listeners,a._renderChildren=n.children,a._componentTag=n.tag,l.render&&(a.render=l.render,a.staticRenderFns=l.staticRenderFns)}(l,e):l.$options=Ie(fa(l.constructor),e||{},l),l._renderProxy=l,l._self=l,function(e){var l=e.$options,a=l.parent;if(a&&!l.abstract){while(a.$options.abstract&&a.$parent)a=a.$parent;a.$children.push(e)}e.$parent=a,e.$root=a?a.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(l),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var l=e.$options._parentListeners;l&&Hl(e,l)}(l),function(e){e._vnode=null,e._staticTrees=null;var l=e.$options,t=e.$vnode=l._parentVnode,n=t&&t.context;e.$slots=ol(l._renderChildren,n),e.$scopedSlots=a,e._c=function(l,a,t,n){return Ll(e,l,a,t,n,!1)},e.$createElement=function(l,a,t,n){return Ll(e,l,a,t,n,!0)};var u=t&&t.data;we(e,"$attrs",u&&u.attrs||a,null,!0),we(e,"$listeners",l._parentListeners||a,null,!0)}(l),zl(l,"beforeCreate"),!l._$fallback&&ul(l),ra(l),!l._$fallback&&nl(l),!l._$fallback&&zl(l,"created"),l.$options.el&&l.$mount(l.$options.el)}})(da),function(e){var l={get:function(){return this._data}},a={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",l),Object.defineProperty(e.prototype,"$props",a),e.prototype.$set=Oe,e.prototype.$delete=xe,e.prototype.$watch=function(e,l,a){if(v(l))return ba(this,e,l,a);a=a||{},a.user=!0;var t=new ta(this,e,l,a);if(a.immediate)try{l.call(this,t.value)}catch(n){Ne(n,this,'callback for immediate watcher "'+t.expression+'"')}return function(){t.teardown()}}}(da),function(e){var l=/^hook:/;e.prototype.$on=function(e,a){var t=this;if(Array.isArray(e))for(var n=0,u=e.length;n<u;n++)t.$on(e[n],a);else(t._events[e]||(t._events[e]=[])).push(a),l.test(e)&&(t._hasHookEvent=!0);return t},e.prototype.$once=function(e,l){var a=this;function t(){a.$off(e,t),l.apply(a,arguments)}return t.fn=l,a.$on(e,t),a},e.prototype.$off=function(e,l){var a=this;if(!arguments.length)return a._events=Object.create(null),a;if(Array.isArray(e)){for(var t=0,n=e.length;t<n;t++)a.$off(e[t],l);return a}var u,r=a._events[e];if(!r)return a;if(!l)return a._events[e]=null,a;var o=r.length;while(o--)if(u=r[o],u===l||u.fn===l){r.splice(o,1);break}return a},e.prototype.$emit=function(e){var l=this,a=l._events[e];if(a){a=a.length>1?k(a):a;for(var t=k(arguments,1),n='event handler for "'+e+'"',u=0,r=a.length;u<r;u++)Be(a[u],l,t,l,n)}return l}}(da),function(e){e.prototype._update=function(e,l){var a=this,t=a.$el,n=a._vnode,u=function(e){var l=Fl;return Fl=e,function(){Fl=l}}(a);a._vnode=e,a.$el=n?a.__patch__(n,e):a.__patch__(a.$el,e,l,!1),u(),t&&(t.__vue__=null),a.$el&&(a.$el.__vue__=a),a.$vnode&&a.$parent&&a.$vnode===a.$parent._vnode&&(a.$parent.$el=a.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){zl(e,"beforeDestroy"),e._isBeingDestroyed=!0;var l=e.$parent;!l||l._isBeingDestroyed||e.$options.abstract||h(l.$children,e),e._watcher&&e._watcher.teardown();var a=e._watchers.length;while(a--)e._watchers[a].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),zl(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(da),function(e){kl(e.prototype),e.prototype.$nextTick=function(e){return Ye(e,this)},e.prototype._render=function(){var e,l=this,a=l.$options,t=a.render,n=a._parentVnode;n&&(l.$scopedSlots=vl(n.data.scopedSlots,l.$slots,l.$scopedSlots)),l.$vnode=n;try{Ml=l,e=t.call(l._renderProxy,l.$createElement)}catch(Ma){Ne(Ma,l,"render"),e=l._vnode}finally{Ml=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof ve||(e=be()),e.parent=n,e}}(da);var _a=[String,RegExp,Array],wa={name:"keep-alive",abstract:!0,props:{include:_a,exclude:_a,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)ya(this.cache,e,this.keys)},mounted:function(){var e=this;this.$watch("include",(function(l){ga(e,(function(e){return ma(l,e)}))})),this.$watch("exclude",(function(l){ga(e,(function(e){return!ma(l,e)}))}))},render:function(){var e=this.$slots.default,l=function(e){if(Array.isArray(e))for(var l=0;l<e.length;l++){var a=e[l];if(n(a)&&(n(a.componentOptions)||Bl(a)))return a}}(e),a=l&&l.componentOptions;if(a){var t=ha(a),u=this.include,r=this.exclude;if(u&&(!t||!ma(u,t))||r&&t&&ma(r,t))return l;var o=this.cache,i=this.keys,v=null==l.key?a.Ctor.cid+(a.tag?"::"+a.tag:""):l.key;o[v]?(l.componentInstance=o[v].componentInstance,h(i,v),i.push(v)):(o[v]=l,i.push(v),this.max&&i.length>parseInt(this.max)&&ya(o,i[0],i,this._vnode)),l.data.keepAlive=!0}return l||e&&e[0]}},Oa={KeepAlive:wa};(function(e){var l={get:function(){return N}};Object.defineProperty(e,"config",l),e.util={warn:ne,extend:S,mergeOptions:Ie,defineReactive:we},e.set=Oe,e.delete=xe,e.nextTick=Ye,e.observable=function(e){return _e(e),e},e.options=Object.create(null),C.forEach((function(l){e.options[l+"s"]=Object.create(null)})),e.options._base=e,S(e.options.components,Oa),function(e){e.use=function(e){var l=this._installedPlugins||(this._installedPlugins=[]);if(l.indexOf(e)>-1)return this;var a=k(arguments,1);return a.unshift(this),"function"===typeof e.install?e.install.apply(e,a):"function"===typeof e&&e.apply(null,a),l.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=Ie(this.options,e),this}}(e),pa(e),function(e){C.forEach((function(l){e[l]=function(e,a){return a?("component"===l&&v(a)&&(a.name=a.name||e,a=this.options._base.extend(a)),"directive"===l&&"function"===typeof a&&(a={bind:a,update:a}),this.options[l+"s"][e]=a,a):this.options[l+"s"][e]}}))}(e)})(da),Object.defineProperty(da.prototype,"$isServer",{get:Q}),Object.defineProperty(da.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(da,"FunctionalRenderContext",{value:Sl}),da.version="2.6.11";var xa="[object Array]",$a="[object Object]";function Da(e,l){var a={};return function e(l,a){if(l===a)return;var t=Sa(l),n=Sa(a);if(t==$a&&n==$a){if(Object.keys(l).length>=Object.keys(a).length)for(var u in a){var r=l[u];void 0===r?l[u]=null:e(r,a[u])}}else t==xa&&n==xa&&l.length>=a.length&&a.forEach((function(a,t){e(l[t],a)}))}(e,l),function e(l,a,t,n){if(l===a)return;var u=Sa(l),r=Sa(a);if(u==$a)if(r!=$a||Object.keys(l).length<Object.keys(a).length)ka(n,t,l);else{var o=function(u){var r=l[u],o=a[u],i=Sa(r),v=Sa(o);if(i!=xa&&i!=$a)r!==a[u]&&function(e,l){if(("[object Null]"===e||"[object Undefined]"===e)&&("[object Null]"===l||"[object Undefined]"===l))return!1;return!0}(i,v)&&ka(n,(""==t?"":t+".")+u,r);else if(i==xa)v!=xa||r.length<o.length?ka(n,(""==t?"":t+".")+u,r):r.forEach((function(l,a){e(l,o[a],(""==t?"":t+".")+u+"["+a+"]",n)}));else if(i==$a)if(v!=$a||Object.keys(r).length<Object.keys(o).length)ka(n,(""==t?"":t+".")+u,r);else for(var s in r)e(r[s],o[s],(""==t?"":t+".")+u+"."+s,n)};for(var i in l)o(i)}else u==xa?r!=xa||l.length<a.length?ka(n,t,l):l.forEach((function(l,u){e(l,a[u],t+"["+u+"]",n)})):ka(n,t,l)}(e,l,"",a),a}function ka(e,l,a){e[l]=a}function Sa(e){return Object.prototype.toString.call(e)}function Aa(e){if(e.__next_tick_callbacks&&e.__next_tick_callbacks.length){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"xiaopaotui",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var l=e.$scope;console.log("["+ +new Date+"]["+(l.is||l.route)+"]["+e._uid+"]:flushCallbacks["+e.__next_tick_callbacks.length+"]")}var a=e.__next_tick_callbacks.slice(0);e.__next_tick_callbacks.length=0;for(var t=0;t<a.length;t++)a[t]()}}function Pa(e,l){if(!e.__next_tick_pending&&!function(e){return Wl.find((function(l){return e._watcher===l}))}(e)){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"xiaopaotui",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var a=e.$scope;console.log("["+ +new Date+"]["+(a.is||a.route)+"]["+e._uid+"]:nextVueTick")}return Ye(l,e)}if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"xiaopaotui",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var t=e.$scope;console.log("["+ +new Date+"]["+(t.is||t.route)+"]["+e._uid+"]:nextMPTick")}var n;if(e.__next_tick_callbacks||(e.__next_tick_callbacks=[]),e.__next_tick_callbacks.push((function(){if(l)try{l.call(e)}catch(Ma){Ne(Ma,e,"nextTick")}else n&&n(e)})),!l&&"undefined"!==typeof Promise)return new Promise((function(e){n=e}))}function ja(e,l){return l&&(l._isVue||l.__v_isMPComponent)?{}:l}function Ia(){}function Ta(e){return Array.isArray(e)?function(e){for(var l,a="",t=0,u=e.length;t<u;t++)n(l=Ta(e[t]))&&""!==l&&(a&&(a+=" "),a+=l);return a}(e):o(e)?function(e){var l="";for(var a in e)e[a]&&(l&&(l+=" "),l+=a);return l}(e):"string"===typeof e?e:""}var Ea=y((function(e){var l={},a=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach((function(e){if(e){var t=e.split(a);t.length>1&&(l[t[0].trim()]=t[1].trim())}})),l}));var La=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];var Ca=["onLaunch","onShow","onHide","onUniNViewMessage","onPageNotFound","onThemeChange","onError","onUnhandledRejection","onInit","onLoad","onReady","onUnload","onPullDownRefresh","onReachBottom","onTabItemTap","onAddToFavorites","onShareTimeline","onShareAppMessage","onResize","onPageScroll","onNavigationBarButtonTap","onBackPress","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputClicked","onUploadDouyinVideo","onNFCReadMessage","onPageShow","onPageHide","onPageResize"];da.prototype.__patch__=function(e,l){var a=this;if(null!==l&&("page"===this.mpType||"component"===this.mpType)){var t=this.$scope,n=Object.create(null);try{n=function(e){var l=Object.create(null),a=[].concat(Object.keys(e._data||{}),Object.keys(e._computedWatchers||{}));a.reduce((function(l,a){return l[a]=e[a],l}),l);var t=e.__composition_api_state__||e.__secret_vfa_state__,n=t&&t.rawBindings;return n&&Object.keys(n).forEach((function(a){l[a]=e[a]})),Object.assign(l,e.$mp.data||{}),Array.isArray(e.$options.behaviors)&&-1!==e.$options.behaviors.indexOf("uni://form-field")&&(l["name"]=e.name,l["value"]=e.value),JSON.parse(JSON.stringify(l,ja))}(this)}catch(o){console.error(o)}n.__webviewId__=t.data.__webviewId__;var u=Object.create(null);Object.keys(n).forEach((function(e){u[e]=t.data[e]}));var r=!1===this.$shouldDiffData?n:Da(n,u);Object.keys(r).length?(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"xiaopaotui",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.log("["+ +new Date+"]["+(t.is||t.route)+"]["+this._uid+"]差量更新",JSON.stringify(r)),this.__next_tick_pending=!0,t.setData(r,(function(){a.__next_tick_pending=!1,Aa(a)}))):Aa(this)}},da.prototype.$mount=function(e,l){return function(e,l,a){return e.mpType?("app"===e.mpType&&(e.$options.render=Ia),e.$options.render||(e.$options.render=Ia),!e._$fallback&&zl(e,"beforeMount"),new ta(e,(function(){e._update(e._render(),a)}),P,{before:function(){e._isMounted&&!e._isDestroyed&&zl(e,"beforeUpdate")}},!0),a=!1,e):e}(this,0,l)},function(e){var l=e.extend;e.extend=function(e){e=e||{};var a=e.methods;return a&&Object.keys(a).forEach((function(l){-1!==Ca.indexOf(l)&&(e[l]=a[l],delete a[l])})),l.call(this,e)};var a=e.config.optionMergeStrategies,t=a.created;Ca.forEach((function(e){a[e]=t})),e.prototype.__lifecycle_hooks__=Ca}(da),function(e){e.config.errorHandler=function(l,a,t){e.util.warn("Error in "+t+': "'+l.toString()+'"',a),console.error(l);var n="function"===typeof getApp&&getApp();n&&n.onError&&n.onError(l)};var l=e.prototype.$emit;e.prototype.$emit=function(e){if(this.$scope&&e){var a=this.$scope["_triggerEvent"]||this.$scope["triggerEvent"];if(a)try{a.call(this.$scope,e,{__args__:k(arguments,1)})}catch(t){}}return l.apply(this,arguments)},e.prototype.$nextTick=function(e){return Pa(this,e)},La.forEach((function(l){e.prototype[l]=function(e){return this.$scope&&this.$scope[l]?this.$scope[l](e):"undefined"!==typeof my?"createSelectorQuery"===l?my.createSelectorQuery(e):"createIntersectionObserver"===l?my.createIntersectionObserver(e):void 0:void 0}})),e.prototype.__init_provide=nl,e.prototype.__init_injections=ul,e.prototype.__call_hook=function(e,l){var a=this;oe();var t,n=a.$options[e],u=e+" hook";if(n)for(var r=0,o=n.length;r<o;r++)t=Be(n[r],a,l?[l]:null,a,u);return a._hasHookEvent&&a.$emit("hook:"+e,l),ie(),t},e.prototype.__set_model=function(l,a,t,n){Array.isArray(n)&&(-1!==n.indexOf("trim")&&(t=t.trim()),-1!==n.indexOf("number")&&(t=this._n(t))),l||(l=this),e.set(l,a,t)},e.prototype.__set_sync=function(l,a,t){l||(l=this),e.set(l,a,t)},e.prototype.__get_orig=function(e){return v(e)&&e["$orig"]||e},e.prototype.__get_value=function(e,l){return function e(l,a){var t=a.split("."),n=t[0];return 0===n.indexOf("__$n")&&(n=parseInt(n.replace("__$n",""))),1===t.length?l[n]:e(l[n],t.slice(1).join("."))}(l||this,e)},e.prototype.__get_class=function(e,l){return function(e,l){return n(e)||n(l)?function(e,l){return e?l?e+" "+l:e:l||""}(e,Ta(l)):""}(l,e)},e.prototype.__get_style=function(e,l){if(!e&&!l)return"";var a=function(e){return Array.isArray(e)?A(e):"string"===typeof e?Ea(e):e}(e),t=l?S(l,a):a;return Object.keys(t).map((function(e){return $(e)+":"+t[e]})).join(";")},e.prototype.__map=function(e,l){var a,t,n,u,r;if(Array.isArray(e)){for(a=new Array(e.length),t=0,n=e.length;t<n;t++)a[t]=l(e[t],t);return a}if(o(e)){for(u=Object.keys(e),a=Object.create(null),t=0,n=u.length;t<n;t++)r=u[t],a[r]=l(e[r],r,t);return a}if("number"===typeof e){for(a=new Array(e),t=0,n=e;t<n;t++)a[t]=l(t,t);return a}return[]}}(da),l["default"]=da}.call(this,a("0ee4"))},"34a8":function(e,l,a){},"34cf":function(e,l,a){var t=a("ed45"),n=a("7172"),u=a("6382"),r=a("dd3e");e.exports=function(e,l){return t(e)||n(e,l)||u(e,l)||r()},e.exports.__esModule=!0,e.exports["default"]=e.exports},"356a":function(e,l,a){"use strict";a.r(l);var t=a("8b0d"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},"38b9":function(e,l,a){},"3abc":function(e,l,a){var t,n,u=a("3b2d");!function(r,o){"object"==u(l)&&"undefined"!=typeof e?e.exports=o():(t=o,n="function"===typeof t?t.call(l,a,l,e):t,void 0===n||(e.exports=n))}(0,(function(){"use strict";var e=6e4,l=36e5,a="millisecond",t="second",n="minute",r="hour",o="day",i="week",v="month",s="quarter",b="year",c="date",f="Invalid Date",d=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,p=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var l=["th","st","nd","rd"],a=e%100;return"["+e+(l[(a-20)%10]||l[a]||l[0])+"]"}},m=function(e,l,a){var t=String(e);return!t||t.length>=l?e:""+Array(l+1-t.length).join(a)+e},g={s:m,z:function(e){var l=-e.utcOffset(),a=Math.abs(l),t=Math.floor(a/60),n=a%60;return(l<=0?"+":"-")+m(t,2,"0")+":"+m(n,2,"0")},m:function e(l,a){if(l.date()<a.date())return-e(a,l);var t=12*(a.year()-l.year())+(a.month()-l.month()),n=l.clone().add(t,v),u=a-n<0,r=l.clone().add(t+(u?-1:1),v);return+(-(t+(a-n)/(u?n-r:r-n))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:v,y:b,w:i,d:o,D:c,h:r,m:n,s:t,ms:a,Q:s}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},y="en",_={};_[y]=h;var w=function(e){return e instanceof D},O=function e(l,a,t){var n;if(!l)return y;if("string"==typeof l){var u=l.toLowerCase();_[u]&&(n=u),a&&(_[u]=a,n=u);var r=l.split("-");if(!n&&r.length>1)return e(r[0])}else{var o=l.name;_[o]=l,n=o}return!t&&n&&(y=n),n||!t&&y},x=function(e,l){if(w(e))return e.clone();var a="object"==u(l)?l:{};return a.date=e,a.args=arguments,new D(a)},$=g;$.l=O,$.i=w,$.w=function(e,l){return x(e,{locale:l.$L,utc:l.$u,x:l.$x,$offset:l.$offset})};var D=function(){function u(e){this.$L=O(e.locale,null,!0),this.parse(e)}var h=u.prototype;return h.parse=function(e){this.$d=function(e){var l=e.date,a=e.utc;if(null===l)return new Date(NaN);if($.u(l))return new Date;if(l instanceof Date)return new Date(l);if("string"==typeof l&&!/Z$/i.test(l)){var t=l.match(d);if(t){var n=t[2]-1||0,u=(t[7]||"0").substring(0,3);return a?new Date(Date.UTC(t[1],n,t[3]||1,t[4]||0,t[5]||0,t[6]||0,u)):new Date(t[1],n,t[3]||1,t[4]||0,t[5]||0,t[6]||0,u)}}return new Date(l)}(e),this.$x=e.x||{},this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return $},h.isValid=function(){return!(this.$d.toString()===f)},h.isSame=function(e,l){var a=x(e);return this.startOf(l)<=a&&a<=this.endOf(l)},h.isAfter=function(e,l){return x(e)<this.startOf(l)},h.isBefore=function(e,l){return this.endOf(l)<x(e)},h.$g=function(e,l,a){return $.u(e)?this[l]:this.set(a,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,l){var a=this,u=!!$.u(l)||l,s=$.p(e),f=function(e,l){var t=$.w(a.$u?Date.UTC(a.$y,l,e):new Date(a.$y,l,e),a);return u?t:t.endOf(o)},d=function(e,l){return $.w(a.toDate()[e].apply(a.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(l)),a)},p=this.$W,h=this.$M,m=this.$D,g="set"+(this.$u?"UTC":"");switch(s){case b:return u?f(1,0):f(31,11);case v:return u?f(1,h):f(0,h+1);case i:var y=this.$locale().weekStart||0,_=(p<y?p+7:p)-y;return f(u?m-_:m+(6-_),h);case o:case c:return d(g+"Hours",0);case r:return d(g+"Minutes",1);case n:return d(g+"Seconds",2);case t:return d(g+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(e,l){var u,i=$.p(e),s="set"+(this.$u?"UTC":""),f=(u={},u[o]=s+"Date",u[c]=s+"Date",u[v]=s+"Month",u[b]=s+"FullYear",u[r]=s+"Hours",u[n]=s+"Minutes",u[t]=s+"Seconds",u[a]=s+"Milliseconds",u)[i],d=i===o?this.$D+(l-this.$W):l;if(i===v||i===b){var p=this.clone().set(c,1);p.$d[f](d),p.init(),this.$d=p.set(c,Math.min(this.$D,p.daysInMonth())).$d}else f&&this.$d[f](d);return this.init(),this},h.set=function(e,l){return this.clone().$set(e,l)},h.get=function(e){return this[$.p(e)]()},h.add=function(a,u){var s,c=this;a=Number(a);var f=$.p(u),d=function(e){var l=x(c);return $.w(l.date(l.date()+Math.round(e*a)),c)};if(f===v)return this.set(v,this.$M+a);if(f===b)return this.set(b,this.$y+a);if(f===o)return d(1);if(f===i)return d(7);var p=(s={},s[n]=e,s[r]=l,s[t]=1e3,s)[f]||1,h=this.$d.getTime()+a*p;return $.w(h,this)},h.subtract=function(e,l){return this.add(-1*e,l)},h.format=function(e){var l=this,a=this.$locale();if(!this.isValid())return a.invalidDate||f;var t=e||"YYYY-MM-DDTHH:mm:ssZ",n=$.z(this),u=this.$H,r=this.$m,o=this.$M,i=a.weekdays,v=a.months,s=a.meridiem,b=function(e,a,n,u){return e&&(e[a]||e(l,t))||n[a].slice(0,u)},c=function(e){return $.s(u%12||12,e,"0")},d=s||function(e,l,a){var t=e<12?"AM":"PM";return a?t.toLowerCase():t};return t.replace(p,(function(e,t){return t||function(e){switch(e){case"YY":return String(l.$y).slice(-2);case"YYYY":return $.s(l.$y,4,"0");case"M":return o+1;case"MM":return $.s(o+1,2,"0");case"MMM":return b(a.monthsShort,o,v,3);case"MMMM":return b(v,o);case"D":return l.$D;case"DD":return $.s(l.$D,2,"0");case"d":return String(l.$W);case"dd":return b(a.weekdaysMin,l.$W,i,2);case"ddd":return b(a.weekdaysShort,l.$W,i,3);case"dddd":return i[l.$W];case"H":return String(u);case"HH":return $.s(u,2,"0");case"h":return c(1);case"hh":return c(2);case"a":return d(u,r,!0);case"A":return d(u,r,!1);case"m":return String(r);case"mm":return $.s(r,2,"0");case"s":return String(l.$s);case"ss":return $.s(l.$s,2,"0");case"SSS":return $.s(l.$ms,3,"0");case"Z":return n}return null}(e)||n.replace(":","")}))},h.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},h.diff=function(a,u,c){var f,d=this,p=$.p(u),h=x(a),m=(h.utcOffset()-this.utcOffset())*e,g=this-h,y=function(){return $.m(d,h)};switch(p){case b:f=y()/12;break;case v:f=y();break;case s:f=y()/3;break;case i:f=(g-m)/6048e5;break;case o:f=(g-m)/864e5;break;case r:f=g/l;break;case n:f=g/e;break;case t:f=g/1e3;break;default:f=g}return c?f:$.a(f)},h.daysInMonth=function(){return this.endOf(v).$D},h.$locale=function(){return _[this.$L]},h.locale=function(e,l){if(!e)return this.$L;var a=this.clone(),t=O(e,l,!0);return t&&(a.$L=t),a},h.clone=function(){return $.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},u}(),k=D.prototype;return x.prototype=k,[["$ms",a],["$s",t],["$m",n],["$H",r],["$W",o],["$M",v],["$y",b],["$D",c]].forEach((function(e){k[e[1]]=function(l){return this.$g(l,e[0],e[1])}})),x.extend=function(e,l){return e.$i||(e(l,D,x),e.$i=!0),x},x.locale=O,x.isDayjs=w,x.unix=function(e){return x(1e3*e)},x.en=_[y],x.Ls=_,x.p={},x}))},"3b2d":function(e,l){function a(l){return e.exports=a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports["default"]=e.exports,a(l)}e.exports=a,e.exports.__esModule=!0,e.exports["default"]=e.exports},"3ca8":function(e,l,a){"use strict";a.r(l);var t=a("66f5"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},"418a":function(e,l,a){"use strict";var t=a("498f"),n=a.n(t);n.a},4318:function(e,l,a){},"47a9":function(e,l){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports["default"]=e.exports},"498f":function(e,l,a){},"4a9a":function(e,l,a){"use strict";var t=a("f80e"),n=a.n(t);n.a},"4e9e":function(e,l,a){},"514e":function(e,l,a){},5268:function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var n=t(a("a567")),u={data:function(){return{productInfo:{id:1,name:"精选美味商品1",price:29.9,image:"/static/logo_ruiji.png",description:"这是一款精心制作的美味商品，采用优质食材，口感丰富，营养均衡。"}}},onLoad:function(e){console.log("商品1页面加载"),e&&e.mid&&n.default.setMid(e.mid)},methods:{goBack:function(){e.navigateBack({delta:1})},goToOrder:function(){n.default.setMid("2"),n.default.navigateToWithRefresh("/pages/index/index")}}};l.default=u}).call(this,a("df3c")["default"])},5463:function(e,l,a){"use strict";var t=a("be66"),n=a.n(t);n.a},"54e9":function(e,l,a){},"565b":function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=a("7281"),n=(0,t.createProductPageConfig)(10,"精选美味商品10",58.9);l.default=n},"5abe":function(e,l,a){"use strict";a.r(l);var t=a("7090"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},"5b4d":function(e,l,a){},"5dd2":function(e,l,a){"use strict";var t=a("02f2"),n=a.n(t);n.a},6329:function(e,l,a){"use strict";var t=a("81fa"),n=a.n(t);n.a},6382:function(e,l,a){var t=a("6454");e.exports=function(e,l){if(e){if("string"===typeof e)return t(e,l);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?t(e,l):void 0}},e.exports.__esModule=!0,e.exports["default"]=e.exports},6454:function(e,l){e.exports=function(e,l){(null==l||l>e.length)&&(l=e.length);for(var a=0,t=new Array(l);a<l;a++)t[a]=e[a];return t},e.exports.__esModule=!0,e.exports["default"]=e.exports},"64ea":function(e,l,a){},6592:function(e,l,a){"use strict";var t=a("93f6"),n=a.n(t);n.a},"65ea":function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=[[{label:"市辖区",value:"1101"}],[{label:"市辖区",value:"1201"}],[{label:"石家庄市",value:"1301"},{label:"唐山市",value:"1302"},{label:"秦皇岛市",value:"1303"},{label:"邯郸市",value:"1304"},{label:"邢台市",value:"1305"},{label:"保定市",value:"1306"},{label:"张家口市",value:"1307"},{label:"承德市",value:"1308"},{label:"沧州市",value:"1309"},{label:"廊坊市",value:"1310"},{label:"衡水市",value:"1311"}],[{label:"太原市",value:"1401"},{label:"大同市",value:"1402"},{label:"阳泉市",value:"1403"},{label:"长治市",value:"1404"},{label:"晋城市",value:"1405"},{label:"朔州市",value:"1406"},{label:"晋中市",value:"1407"},{label:"运城市",value:"1408"},{label:"忻州市",value:"1409"},{label:"临汾市",value:"1410"},{label:"吕梁市",value:"1411"}],[{label:"呼和浩特市",value:"1501"},{label:"包头市",value:"1502"},{label:"乌海市",value:"1503"},{label:"赤峰市",value:"1504"},{label:"通辽市",value:"1505"},{label:"鄂尔多斯市",value:"1506"},{label:"呼伦贝尔市",value:"1507"},{label:"巴彦淖尔市",value:"1508"},{label:"乌兰察布市",value:"1509"},{label:"兴安盟",value:"1522"},{label:"锡林郭勒盟",value:"1525"},{label:"阿拉善盟",value:"1529"}],[{label:"沈阳市",value:"2101"},{label:"大连市",value:"2102"},{label:"鞍山市",value:"2103"},{label:"抚顺市",value:"2104"},{label:"本溪市",value:"2105"},{label:"丹东市",value:"2106"},{label:"锦州市",value:"2107"},{label:"营口市",value:"2108"},{label:"阜新市",value:"2109"},{label:"辽阳市",value:"2110"},{label:"盘锦市",value:"2111"},{label:"铁岭市",value:"2112"},{label:"朝阳市",value:"2113"},{label:"葫芦岛市",value:"2114"}],[{label:"长春市",value:"2201"},{label:"吉林市",value:"2202"},{label:"四平市",value:"2203"},{label:"辽源市",value:"2204"},{label:"通化市",value:"2205"},{label:"白山市",value:"2206"},{label:"松原市",value:"2207"},{label:"白城市",value:"2208"},{label:"延边朝鲜族自治州",value:"2224"}],[{label:"哈尔滨市",value:"2301"},{label:"齐齐哈尔市",value:"2302"},{label:"鸡西市",value:"2303"},{label:"鹤岗市",value:"2304"},{label:"双鸭山市",value:"2305"},{label:"大庆市",value:"2306"},{label:"伊春市",value:"2307"},{label:"佳木斯市",value:"2308"},{label:"七台河市",value:"2309"},{label:"牡丹江市",value:"2310"},{label:"黑河市",value:"2311"},{label:"绥化市",value:"2312"},{label:"大兴安岭地区",value:"2327"}],[{label:"市辖区",value:"3101"}],[{label:"南京市",value:"3201"},{label:"无锡市",value:"3202"},{label:"徐州市",value:"3203"},{label:"常州市",value:"3204"},{label:"苏州市",value:"3205"},{label:"南通市",value:"3206"},{label:"连云港市",value:"3207"},{label:"淮安市",value:"3208"},{label:"盐城市",value:"3209"},{label:"扬州市",value:"3210"},{label:"镇江市",value:"3211"},{label:"泰州市",value:"3212"},{label:"宿迁市",value:"3213"}],[{label:"杭州市",value:"3301"},{label:"宁波市",value:"3302"},{label:"温州市",value:"3303"},{label:"嘉兴市",value:"3304"},{label:"湖州市",value:"3305"},{label:"绍兴市",value:"3306"},{label:"金华市",value:"3307"},{label:"衢州市",value:"3308"},{label:"舟山市",value:"3309"},{label:"台州市",value:"3310"},{label:"丽水市",value:"3311"}],[{label:"合肥市",value:"3401"},{label:"芜湖市",value:"3402"},{label:"蚌埠市",value:"3403"},{label:"淮南市",value:"3404"},{label:"马鞍山市",value:"3405"},{label:"淮北市",value:"3406"},{label:"铜陵市",value:"3407"},{label:"安庆市",value:"3408"},{label:"黄山市",value:"3410"},{label:"滁州市",value:"3411"},{label:"阜阳市",value:"3412"},{label:"宿州市",value:"3413"},{label:"六安市",value:"3415"},{label:"亳州市",value:"3416"},{label:"池州市",value:"3417"},{label:"宣城市",value:"3418"}],[{label:"福州市",value:"3501"},{label:"厦门市",value:"3502"},{label:"莆田市",value:"3503"},{label:"三明市",value:"3504"},{label:"泉州市",value:"3505"},{label:"漳州市",value:"3506"},{label:"南平市",value:"3507"},{label:"龙岩市",value:"3508"},{label:"宁德市",value:"3509"}],[{label:"南昌市",value:"3601"},{label:"景德镇市",value:"3602"},{label:"萍乡市",value:"3603"},{label:"九江市",value:"3604"},{label:"新余市",value:"3605"},{label:"鹰潭市",value:"3606"},{label:"赣州市",value:"3607"},{label:"吉安市",value:"3608"},{label:"宜春市",value:"3609"},{label:"抚州市",value:"3610"},{label:"上饶市",value:"3611"}],[{label:"济南市",value:"3701"},{label:"青岛市",value:"3702"},{label:"淄博市",value:"3703"},{label:"枣庄市",value:"3704"},{label:"东营市",value:"3705"},{label:"烟台市",value:"3706"},{label:"潍坊市",value:"3707"},{label:"济宁市",value:"3708"},{label:"泰安市",value:"3709"},{label:"威海市",value:"3710"},{label:"日照市",value:"3711"},{label:"莱芜市",value:"3712"},{label:"临沂市",value:"3713"},{label:"德州市",value:"3714"},{label:"聊城市",value:"3715"},{label:"滨州市",value:"3716"},{label:"菏泽市",value:"3717"}],[{label:"郑州市",value:"4101"},{label:"开封市",value:"4102"},{label:"洛阳市",value:"4103"},{label:"平顶山市",value:"4104"},{label:"安阳市",value:"4105"},{label:"鹤壁市",value:"4106"},{label:"新乡市",value:"4107"},{label:"焦作市",value:"4108"},{label:"濮阳市",value:"4109"},{label:"许昌市",value:"4110"},{label:"漯河市",value:"4111"},{label:"三门峡市",value:"4112"},{label:"南阳市",value:"4113"},{label:"商丘市",value:"4114"},{label:"信阳市",value:"4115"},{label:"周口市",value:"4116"},{label:"驻马店市",value:"4117"},{label:"省直辖县级行政区划",value:"4190"}],[{label:"武汉市",value:"4201"},{label:"黄石市",value:"4202"},{label:"十堰市",value:"4203"},{label:"宜昌市",value:"4205"},{label:"襄阳市",value:"4206"},{label:"鄂州市",value:"4207"},{label:"荆门市",value:"4208"},{label:"孝感市",value:"4209"},{label:"荆州市",value:"4210"},{label:"黄冈市",value:"4211"},{label:"咸宁市",value:"4212"},{label:"随州市",value:"4213"},{label:"恩施土家族苗族自治州",value:"4228"},{label:"省直辖县级行政区划",value:"4290"}],[{label:"长沙市",value:"4301"},{label:"株洲市",value:"4302"},{label:"湘潭市",value:"4303"},{label:"衡阳市",value:"4304"},{label:"邵阳市",value:"4305"},{label:"岳阳市",value:"4306"},{label:"常德市",value:"4307"},{label:"张家界市",value:"4308"},{label:"益阳市",value:"4309"},{label:"郴州市",value:"4310"},{label:"永州市",value:"4311"},{label:"怀化市",value:"4312"},{label:"娄底市",value:"4313"},{label:"湘西土家族苗族自治州",value:"4331"}],[{label:"广州市",value:"4401"},{label:"韶关市",value:"4402"},{label:"深圳市",value:"4403"},{label:"珠海市",value:"4404"},{label:"汕头市",value:"4405"},{label:"佛山市",value:"4406"},{label:"江门市",value:"4407"},{label:"湛江市",value:"4408"},{label:"茂名市",value:"4409"},{label:"肇庆市",value:"4412"},{label:"惠州市",value:"4413"},{label:"梅州市",value:"4414"},{label:"汕尾市",value:"4415"},{label:"河源市",value:"4416"},{label:"阳江市",value:"4417"},{label:"清远市",value:"4418"},{label:"东莞市",value:"4419"},{label:"中山市",value:"4420"},{label:"潮州市",value:"4451"},{label:"揭阳市",value:"4452"},{label:"云浮市",value:"4453"}],[{label:"南宁市",value:"4501"},{label:"柳州市",value:"4502"},{label:"桂林市",value:"4503"},{label:"梧州市",value:"4504"},{label:"北海市",value:"4505"},{label:"防城港市",value:"4506"},{label:"钦州市",value:"4507"},{label:"贵港市",value:"4508"},{label:"玉林市",value:"4509"},{label:"百色市",value:"4510"},{label:"贺州市",value:"4511"},{label:"河池市",value:"4512"},{label:"来宾市",value:"4513"},{label:"崇左市",value:"4514"}],[{label:"海口市",value:"4601"},{label:"三亚市",value:"4602"},{label:"三沙市",value:"4603"},{label:"儋州市",value:"4604"},{label:"省直辖县级行政区划",value:"4690"}],[{label:"市辖区",value:"5001"},{label:"县",value:"5002"}],[{label:"成都市",value:"5101"},{label:"自贡市",value:"5103"},{label:"攀枝花市",value:"5104"},{label:"泸州市",value:"5105"},{label:"德阳市",value:"5106"},{label:"绵阳市",value:"5107"},{label:"广元市",value:"5108"},{label:"遂宁市",value:"5109"},{label:"内江市",value:"5110"},{label:"乐山市",value:"5111"},{label:"南充市",value:"5113"},{label:"眉山市",value:"5114"},{label:"宜宾市",value:"5115"},{label:"广安市",value:"5116"},{label:"达州市",value:"5117"},{label:"雅安市",value:"5118"},{label:"巴中市",value:"5119"},{label:"资阳市",value:"5120"},{label:"阿坝藏族羌族自治州",value:"5132"},{label:"甘孜藏族自治州",value:"5133"},{label:"凉山彝族自治州",value:"5134"}],[{label:"贵阳市",value:"5201"},{label:"六盘水市",value:"5202"},{label:"遵义市",value:"5203"},{label:"安顺市",value:"5204"},{label:"毕节市",value:"5205"},{label:"铜仁市",value:"5206"},{label:"黔西南布依族苗族自治州",value:"5223"},{label:"黔东南苗族侗族自治州",value:"5226"},{label:"黔南布依族苗族自治州",value:"5227"}],[{label:"昆明市",value:"5301"},{label:"曲靖市",value:"5303"},{label:"玉溪市",value:"5304"},{label:"保山市",value:"5305"},{label:"昭通市",value:"5306"},{label:"丽江市",value:"5307"},{label:"普洱市",value:"5308"},{label:"临沧市",value:"5309"},{label:"楚雄彝族自治州",value:"5323"},{label:"红河哈尼族彝族自治州",value:"5325"},{label:"文山壮族苗族自治州",value:"5326"},{label:"西双版纳傣族自治州",value:"5328"},{label:"大理白族自治州",value:"5329"},{label:"德宏傣族景颇族自治州",value:"5331"},{label:"怒江傈僳族自治州",value:"5333"},{label:"迪庆藏族自治州",value:"5334"}],[{label:"拉萨市",value:"5401"},{label:"日喀则市",value:"5402"},{label:"昌都市",value:"5403"},{label:"林芝市",value:"5404"},{label:"山南市",value:"5405"},{label:"那曲地区",value:"5424"},{label:"阿里地区",value:"5425"}],[{label:"西安市",value:"6101"},{label:"铜川市",value:"6102"},{label:"宝鸡市",value:"6103"},{label:"咸阳市",value:"6104"},{label:"渭南市",value:"6105"},{label:"延安市",value:"6106"},{label:"汉中市",value:"6107"},{label:"榆林市",value:"6108"},{label:"安康市",value:"6109"},{label:"商洛市",value:"6110"}],[{label:"兰州市",value:"6201"},{label:"嘉峪关市",value:"6202"},{label:"金昌市",value:"6203"},{label:"白银市",value:"6204"},{label:"天水市",value:"6205"},{label:"武威市",value:"6206"},{label:"张掖市",value:"6207"},{label:"平凉市",value:"6208"},{label:"酒泉市",value:"6209"},{label:"庆阳市",value:"6210"},{label:"定西市",value:"6211"},{label:"陇南市",value:"6212"},{label:"临夏回族自治州",value:"6229"},{label:"甘南藏族自治州",value:"6230"}],[{label:"西宁市",value:"6301"},{label:"海东市",value:"6302"},{label:"海北藏族自治州",value:"6322"},{label:"黄南藏族自治州",value:"6323"},{label:"海南藏族自治州",value:"6325"},{label:"果洛藏族自治州",value:"6326"},{label:"玉树藏族自治州",value:"6327"},{label:"海西蒙古族藏族自治州",value:"6328"}],[{label:"银川市",value:"6401"},{label:"石嘴山市",value:"6402"},{label:"吴忠市",value:"6403"},{label:"固原市",value:"6404"},{label:"中卫市",value:"6405"}],[{label:"乌鲁木齐市",value:"6501"},{label:"克拉玛依市",value:"6502"},{label:"吐鲁番市",value:"6504"},{label:"哈密市",value:"6505"},{label:"昌吉回族自治州",value:"6523"},{label:"博尔塔拉蒙古自治州",value:"6527"},{label:"巴音郭楞蒙古自治州",value:"6528"},{label:"阿克苏地区",value:"6529"},{label:"克孜勒苏柯尔克孜自治州",value:"6530"},{label:"喀什地区",value:"6531"},{label:"和田地区",value:"6532"},{label:"伊犁哈萨克自治州",value:"6540"},{label:"塔城地区",value:"6542"},{label:"阿勒泰地区",value:"6543"},{label:"自治区直辖县级行政区划",value:"6590"}],[{label:"台北",value:"6601"},{label:"高雄",value:"6602"},{label:"基隆",value:"6603"},{label:"台中",value:"6604"},{label:"台南",value:"6605"},{label:"新竹",value:"6606"},{label:"嘉义",value:"6607"},{label:"宜兰",value:"6608"},{label:"桃园",value:"6609"},{label:"苗栗",value:"6610"},{label:"彰化",value:"6611"},{label:"南投",value:"6612"},{label:"云林",value:"6613"},{label:"屏东",value:"6614"},{label:"台东",value:"6615"},{label:"花莲",value:"6616"},{label:"澎湖",value:"6617"}],[{label:"香港岛",value:"6701"},{label:"九龙",value:"6702"},{label:"新界",value:"6703"}],[{label:"澳门半岛",value:"6801"},{label:"氹仔岛",value:"6802"},{label:"路环岛",value:"6803"},{label:"路氹城",value:"6804"}],[{label:"钓鱼岛",value:"6901"}]];l.default=t},"66f5":function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=a("7281"),n=(0,t.createProductPageConfig)(9,"精选美味商品9",52.9);l.default=n},"67ad":function(e,l){e.exports=function(e,l){if(!(e instanceof l))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports["default"]=e.exports},6883:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=a("7281"),n=(0,t.createProductPageConfig)(5,"精选美味商品5",45.9);l.default=n},"6b63":function(e,l,a){"use strict";var t=a("7437"),n=a.n(t);n.a},"6dc9":function(e,l,a){},"6e29":function(e,l,a){},"6e37":function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=[{label:"北京市",value:"11"},{label:"天津市",value:"12"},{label:"河北省",value:"13"},{label:"山西省",value:"14"},{label:"内蒙古自治区",value:"15"},{label:"辽宁省",value:"21"},{label:"吉林省",value:"22"},{label:"黑龙江省",value:"23"},{label:"上海市",value:"31"},{label:"江苏省",value:"32"},{label:"浙江省",value:"33"},{label:"安徽省",value:"34"},{label:"福建省",value:"35"},{label:"江西省",value:"36"},{label:"山东省",value:"37"},{label:"河南省",value:"41"},{label:"湖北省",value:"42"},{label:"湖南省",value:"43"},{label:"广东省",value:"44"},{label:"广西壮族自治区",value:"45"},{label:"海南省",value:"46"},{label:"重庆市",value:"50"},{label:"四川省",value:"51"},{label:"贵州省",value:"52"},{label:"云南省",value:"53"},{label:"西藏自治区",value:"54"},{label:"陕西省",value:"61"},{label:"甘肃省",value:"62"},{label:"青海省",value:"63"},{label:"宁夏回族自治区",value:"64"},{label:"新疆维吾尔自治区",value:"65"},{label:"台湾",value:"66"},{label:"香港",value:"67"},{label:"澳门",value:"68"},{label:"钓鱼岛",value:"69"}];l.default=t},"6f91":function(e,l,a){"use strict";a.r(l);var t=a("1d42"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},7090:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=a("7281"),n=(0,t.createProductPageConfig)(6,"精选美味商品6",32.9);l.default=n},7150:function(e,l,a){},7172:function(e,l){e.exports=function(e,l){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var t,n,u,r,o=[],i=!0,v=!1;try{if(u=(a=a.call(e)).next,0===l){if(Object(a)!==a)return;i=!1}else for(;!(i=(t=u.call(a)).done)&&(o.push(t.value),o.length!==l);i=!0);}catch(e){v=!0,n=e}finally{try{if(!i&&null!=a["return"]&&(r=a["return"](),Object(r)!==r))return}finally{if(v)throw n}}return o}},e.exports.__esModule=!0,e.exports["default"]=e.exports},7281:function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.createProductPageConfig=function(l,a,t){return{data:function(){return{productInfo:{id:l,name:a,price:t,image:"/static/logo_ruiji.png",description:"这是一款精心制作的美味商品，采用优质食材，口感丰富，营养均衡。"}}},onLoad:function(e){console.log("".concat(a,"页面加载")),e&&e.mid&&n.default.setMid(e.mid)},methods:{goBack:function(){e.navigateBack({delta:1})},goToOrder:function(){n.default.navigateTo("/pages/index/index")}}}};var n=t(a("a567"))}).call(this,a("df3c")["default"])},7437:function(e,l,a){},"752b":function(e,l,a){},7647:function(e,l){function a(l,t){return e.exports=a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,l){return e.__proto__=l,e},e.exports.__esModule=!0,e.exports["default"]=e.exports,a(l,t)}e.exports=a,e.exports.__esModule=!0,e.exports["default"]=e.exports},"77f6":function(e,l,a){"use strict";var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var n=t(a("3240")),u=t(a("8f59"));n.default.use(u.default);var r=new u.default.Store({state:{storeInfo:{},shopInfo:"",orderListData:[],baseUserInfo:"",lodding:!1,sessionId:"",addressBackUrl:"",dishTypeIndex:0,shopPhone:"",shopStatus:{},orderData:{},token:"",arrivals:"",remarkData:"",addressData:{},deliveryFee:0,gender:0},mutations:{setStoreInfo:function(e,l){e.storeInfo=l},setShopInfo:function(e,l){e.shopInfo=l},initdishListMut:function(e,l){e.orderListData=l},setBaseUserInfo:function(e,l){e.baseUserInfo=l},setLodding:function(e,l){e.lodding=l},setSessionId:function(e,l){e.sessionId=l},setAddressBackUrl:function(e,l){e.addressBackUrl=l},setDishTypeIndex:function(e,l){e.dishTypeIndex=l},setShopPhone:function(e,l){e.shopPhone=l},setShopStatus:function(e,l){e.shopStatus=l},setOrderData:function(e,l){e.orderData=l},setToken:function(e,l){e.token=l},setArrivalTime:function(e,l){e.arrivals=l},setRemark:function(e,l){e.remarkData=l},setAddress:function(e,l){e.addressData=l},setDeliveryFee:function(e,l){null===l||void 0===l||isNaN(l)?e.deliveryFee=0:e.deliveryFee=Number(l)},setGender:function(e,l){e.gender=l}},actions:{}}),o=r;l.default=o},7891:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=[[[{label:"东城区",value:"110101"},{label:"西城区",value:"110102"},{label:"朝阳区",value:"110105"},{label:"丰台区",value:"110106"},{label:"石景山区",value:"110107"},{label:"海淀区",value:"110108"},{label:"门头沟区",value:"110109"},{label:"房山区",value:"110111"},{label:"通州区",value:"110112"},{label:"顺义区",value:"110113"},{label:"昌平区",value:"110114"},{label:"大兴区",value:"110115"},{label:"怀柔区",value:"110116"},{label:"平谷区",value:"110117"},{label:"密云区",value:"110118"},{label:"延庆区",value:"110119"}]],[[{label:"和平区",value:"120101"},{label:"河东区",value:"120102"},{label:"河西区",value:"120103"},{label:"南开区",value:"120104"},{label:"河北区",value:"120105"},{label:"红桥区",value:"120106"},{label:"东丽区",value:"120110"},{label:"西青区",value:"120111"},{label:"津南区",value:"120112"},{label:"北辰区",value:"120113"},{label:"武清区",value:"120114"},{label:"宝坻区",value:"120115"},{label:"滨海新区",value:"120116"},{label:"宁河区",value:"120117"},{label:"静海区",value:"120118"},{label:"蓟州区",value:"120119"}]],[[{label:"长安区",value:"130102"},{label:"桥西区",value:"130104"},{label:"新华区",value:"130105"},{label:"井陉矿区",value:"130107"},{label:"裕华区",value:"130108"},{label:"藁城区",value:"130109"},{label:"鹿泉区",value:"130110"},{label:"栾城区",value:"130111"},{label:"井陉县",value:"130121"},{label:"正定县",value:"130123"},{label:"行唐县",value:"130125"},{label:"灵寿县",value:"130126"},{label:"高邑县",value:"130127"},{label:"深泽县",value:"130128"},{label:"赞皇县",value:"130129"},{label:"无极县",value:"130130"},{label:"平山县",value:"130131"},{label:"元氏县",value:"130132"},{label:"赵县",value:"130133"},{label:"石家庄高新技术产业开发区",value:"130171"},{label:"石家庄循环化工园区",value:"130172"},{label:"辛集市",value:"130181"},{label:"晋州市",value:"130183"},{label:"新乐市",value:"130184"}],[{label:"路南区",value:"130202"},{label:"路北区",value:"130203"},{label:"古冶区",value:"130204"},{label:"开平区",value:"130205"},{label:"丰南区",value:"130207"},{label:"丰润区",value:"130208"},{label:"曹妃甸区",value:"130209"},{label:"滦县",value:"130223"},{label:"滦南县",value:"130224"},{label:"乐亭县",value:"130225"},{label:"迁西县",value:"130227"},{label:"玉田县",value:"130229"},{label:"唐山市芦台经济技术开发区",value:"130271"},{label:"唐山市汉沽管理区",value:"130272"},{label:"唐山高新技术产业开发区",value:"130273"},{label:"河北唐山海港经济开发区",value:"130274"},{label:"遵化市",value:"130281"},{label:"迁安市",value:"130283"}],[{label:"海港区",value:"130302"},{label:"山海关区",value:"130303"},{label:"北戴河区",value:"130304"},{label:"抚宁区",value:"130306"},{label:"青龙满族自治县",value:"130321"},{label:"昌黎县",value:"130322"},{label:"卢龙县",value:"130324"},{label:"秦皇岛市经济技术开发区",value:"130371"},{label:"北戴河新区",value:"130372"}],[{label:"邯山区",value:"130402"},{label:"丛台区",value:"130403"},{label:"复兴区",value:"130404"},{label:"峰峰矿区",value:"130406"},{label:"肥乡区",value:"130407"},{label:"永年区",value:"130408"},{label:"临漳县",value:"130423"},{label:"成安县",value:"130424"},{label:"大名县",value:"130425"},{label:"涉县",value:"130426"},{label:"磁县",value:"130427"},{label:"邱县",value:"130430"},{label:"鸡泽县",value:"130431"},{label:"广平县",value:"130432"},{label:"馆陶县",value:"130433"},{label:"魏县",value:"130434"},{label:"曲周县",value:"130435"},{label:"邯郸经济技术开发区",value:"130471"},{label:"邯郸冀南新区",value:"130473"},{label:"武安市",value:"130481"}],[{label:"桥东区",value:"130502"},{label:"桥西区",value:"130503"},{label:"邢台县",value:"130521"},{label:"临城县",value:"130522"},{label:"内丘县",value:"130523"},{label:"柏乡县",value:"130524"},{label:"隆尧县",value:"130525"},{label:"任县",value:"130526"},{label:"南和县",value:"130527"},{label:"宁晋县",value:"130528"},{label:"巨鹿县",value:"130529"},{label:"新河县",value:"130530"},{label:"广宗县",value:"130531"},{label:"平乡县",value:"130532"},{label:"威县",value:"130533"},{label:"清河县",value:"130534"},{label:"临西县",value:"130535"},{label:"河北邢台经济开发区",value:"130571"},{label:"南宫市",value:"130581"},{label:"沙河市",value:"130582"}],[{label:"竞秀区",value:"130602"},{label:"莲池区",value:"130606"},{label:"满城区",value:"130607"},{label:"清苑区",value:"130608"},{label:"徐水区",value:"130609"},{label:"涞水县",value:"130623"},{label:"阜平县",value:"130624"},{label:"定兴县",value:"130626"},{label:"唐县",value:"130627"},{label:"高阳县",value:"130628"},{label:"容城县",value:"130629"},{label:"涞源县",value:"130630"},{label:"望都县",value:"130631"},{label:"安新县",value:"130632"},{label:"易县",value:"130633"},{label:"曲阳县",value:"130634"},{label:"蠡县",value:"130635"},{label:"顺平县",value:"130636"},{label:"博野县",value:"130637"},{label:"雄县",value:"130638"},{label:"保定高新技术产业开发区",value:"130671"},{label:"保定白沟新城",value:"130672"},{label:"涿州市",value:"130681"},{label:"定州市",value:"130682"},{label:"安国市",value:"130683"},{label:"高碑店市",value:"130684"}],[{label:"桥东区",value:"130702"},{label:"桥西区",value:"130703"},{label:"宣化区",value:"130705"},{label:"下花园区",value:"130706"},{label:"万全区",value:"130708"},{label:"崇礼区",value:"130709"},{label:"张北县",value:"130722"},{label:"康保县",value:"130723"},{label:"沽源县",value:"130724"},{label:"尚义县",value:"130725"},{label:"蔚县",value:"130726"},{label:"阳原县",value:"130727"},{label:"怀安县",value:"130728"},{label:"怀来县",value:"130730"},{label:"涿鹿县",value:"130731"},{label:"赤城县",value:"130732"},{label:"张家口市高新技术产业开发区",value:"130771"},{label:"张家口市察北管理区",value:"130772"},{label:"张家口市塞北管理区",value:"130773"}],[{label:"双桥区",value:"130802"},{label:"双滦区",value:"130803"},{label:"鹰手营子矿区",value:"130804"},{label:"承德县",value:"130821"},{label:"兴隆县",value:"130822"},{label:"滦平县",value:"130824"},{label:"隆化县",value:"130825"},{label:"丰宁满族自治县",value:"130826"},{label:"宽城满族自治县",value:"130827"},{label:"围场满族蒙古族自治县",value:"130828"},{label:"承德高新技术产业开发区",value:"130871"},{label:"平泉市",value:"130881"}],[{label:"新华区",value:"130902"},{label:"运河区",value:"130903"},{label:"沧县",value:"130921"},{label:"青县",value:"130922"},{label:"东光县",value:"130923"},{label:"海兴县",value:"130924"},{label:"盐山县",value:"130925"},{label:"肃宁县",value:"130926"},{label:"南皮县",value:"130927"},{label:"吴桥县",value:"130928"},{label:"献县",value:"130929"},{label:"孟村回族自治县",value:"130930"},{label:"河北沧州经济开发区",value:"130971"},{label:"沧州高新技术产业开发区",value:"130972"},{label:"沧州渤海新区",value:"130973"},{label:"泊头市",value:"130981"},{label:"任丘市",value:"130982"},{label:"黄骅市",value:"130983"},{label:"河间市",value:"130984"}],[{label:"安次区",value:"131002"},{label:"广阳区",value:"131003"},{label:"固安县",value:"131022"},{label:"永清县",value:"131023"},{label:"香河县",value:"131024"},{label:"大城县",value:"131025"},{label:"文安县",value:"131026"},{label:"大厂回族自治县",value:"131028"},{label:"廊坊经济技术开发区",value:"131071"},{label:"霸州市",value:"131081"},{label:"三河市",value:"131082"}],[{label:"桃城区",value:"131102"},{label:"冀州区",value:"131103"},{label:"枣强县",value:"131121"},{label:"武邑县",value:"131122"},{label:"武强县",value:"131123"},{label:"饶阳县",value:"131124"},{label:"安平县",value:"131125"},{label:"故城县",value:"131126"},{label:"景县",value:"131127"},{label:"阜城县",value:"131128"},{label:"河北衡水经济开发区",value:"131171"},{label:"衡水滨湖新区",value:"131172"},{label:"深州市",value:"131182"}]],[[{label:"小店区",value:"140105"},{label:"迎泽区",value:"140106"},{label:"杏花岭区",value:"140107"},{label:"尖草坪区",value:"140108"},{label:"万柏林区",value:"140109"},{label:"晋源区",value:"140110"},{label:"清徐县",value:"140121"},{label:"阳曲县",value:"140122"},{label:"娄烦县",value:"140123"},{label:"山西转型综合改革示范区",value:"140171"},{label:"古交市",value:"140181"}],[{label:"城区",value:"140202"},{label:"矿区",value:"140203"},{label:"南郊区",value:"140211"},{label:"新荣区",value:"140212"},{label:"阳高县",value:"140221"},{label:"天镇县",value:"140222"},{label:"广灵县",value:"140223"},{label:"灵丘县",value:"140224"},{label:"浑源县",value:"140225"},{label:"左云县",value:"140226"},{label:"大同县",value:"140227"},{label:"山西大同经济开发区",value:"140271"}],[{label:"城区",value:"140302"},{label:"矿区",value:"140303"},{label:"郊区",value:"140311"},{label:"平定县",value:"140321"},{label:"盂县",value:"140322"},{label:"山西阳泉经济开发区",value:"140371"}],[{label:"城区",value:"140402"},{label:"郊区",value:"140411"},{label:"长治县",value:"140421"},{label:"襄垣县",value:"140423"},{label:"屯留县",value:"140424"},{label:"平顺县",value:"140425"},{label:"黎城县",value:"140426"},{label:"壶关县",value:"140427"},{label:"长子县",value:"140428"},{label:"武乡县",value:"140429"},{label:"沁县",value:"140430"},{label:"沁源县",value:"140431"},{label:"山西长治高新技术产业园区",value:"140471"},{label:"潞城市",value:"140481"}],[{label:"城区",value:"140502"},{label:"沁水县",value:"140521"},{label:"阳城县",value:"140522"},{label:"陵川县",value:"140524"},{label:"泽州县",value:"140525"},{label:"高平市",value:"140581"}],[{label:"朔城区",value:"140602"},{label:"平鲁区",value:"140603"},{label:"山阴县",value:"140621"},{label:"应县",value:"140622"},{label:"右玉县",value:"140623"},{label:"怀仁县",value:"140624"},{label:"山西朔州经济开发区",value:"140671"}],[{label:"榆次区",value:"140702"},{label:"榆社县",value:"140721"},{label:"左权县",value:"140722"},{label:"和顺县",value:"140723"},{label:"昔阳县",value:"140724"},{label:"寿阳县",value:"140725"},{label:"太谷县",value:"140726"},{label:"祁县",value:"140727"},{label:"平遥县",value:"140728"},{label:"灵石县",value:"140729"},{label:"介休市",value:"140781"}],[{label:"盐湖区",value:"140802"},{label:"临猗县",value:"140821"},{label:"万荣县",value:"140822"},{label:"闻喜县",value:"140823"},{label:"稷山县",value:"140824"},{label:"新绛县",value:"140825"},{label:"绛县",value:"140826"},{label:"垣曲县",value:"140827"},{label:"夏县",value:"140828"},{label:"平陆县",value:"140829"},{label:"芮城县",value:"140830"},{label:"永济市",value:"140881"},{label:"河津市",value:"140882"}],[{label:"忻府区",value:"140902"},{label:"定襄县",value:"140921"},{label:"五台县",value:"140922"},{label:"代县",value:"140923"},{label:"繁峙县",value:"140924"},{label:"宁武县",value:"140925"},{label:"静乐县",value:"140926"},{label:"神池县",value:"140927"},{label:"五寨县",value:"140928"},{label:"岢岚县",value:"140929"},{label:"河曲县",value:"140930"},{label:"保德县",value:"140931"},{label:"偏关县",value:"140932"},{label:"五台山风景名胜区",value:"140971"},{label:"原平市",value:"140981"}],[{label:"尧都区",value:"141002"},{label:"曲沃县",value:"141021"},{label:"翼城县",value:"141022"},{label:"襄汾县",value:"141023"},{label:"洪洞县",value:"141024"},{label:"古县",value:"141025"},{label:"安泽县",value:"141026"},{label:"浮山县",value:"141027"},{label:"吉县",value:"141028"},{label:"乡宁县",value:"141029"},{label:"大宁县",value:"141030"},{label:"隰县",value:"141031"},{label:"永和县",value:"141032"},{label:"蒲县",value:"141033"},{label:"汾西县",value:"141034"},{label:"侯马市",value:"141081"},{label:"霍州市",value:"141082"}],[{label:"离石区",value:"141102"},{label:"文水县",value:"141121"},{label:"交城县",value:"141122"},{label:"兴县",value:"141123"},{label:"临县",value:"141124"},{label:"柳林县",value:"141125"},{label:"石楼县",value:"141126"},{label:"岚县",value:"141127"},{label:"方山县",value:"141128"},{label:"中阳县",value:"141129"},{label:"交口县",value:"141130"},{label:"孝义市",value:"141181"},{label:"汾阳市",value:"141182"}]],[[{label:"新城区",value:"150102"},{label:"回民区",value:"150103"},{label:"玉泉区",value:"150104"},{label:"赛罕区",value:"150105"},{label:"土默特左旗",value:"150121"},{label:"托克托县",value:"150122"},{label:"和林格尔县",value:"150123"},{label:"清水河县",value:"150124"},{label:"武川县",value:"150125"},{label:"呼和浩特金海工业园区",value:"150171"},{label:"呼和浩特经济技术开发区",value:"150172"}],[{label:"东河区",value:"150202"},{label:"昆都仑区",value:"150203"},{label:"青山区",value:"150204"},{label:"石拐区",value:"150205"},{label:"白云鄂博矿区",value:"150206"},{label:"九原区",value:"150207"},{label:"土默特右旗",value:"150221"},{label:"固阳县",value:"150222"},{label:"达尔罕茂明安联合旗",value:"150223"},{label:"包头稀土高新技术产业开发区",value:"150271"}],[{label:"海勃湾区",value:"150302"},{label:"海南区",value:"150303"},{label:"乌达区",value:"150304"}],[{label:"红山区",value:"150402"},{label:"元宝山区",value:"150403"},{label:"松山区",value:"150404"},{label:"阿鲁科尔沁旗",value:"150421"},{label:"巴林左旗",value:"150422"},{label:"巴林右旗",value:"150423"},{label:"林西县",value:"150424"},{label:"克什克腾旗",value:"150425"},{label:"翁牛特旗",value:"150426"},{label:"喀喇沁旗",value:"150428"},{label:"宁城县",value:"150429"},{label:"敖汉旗",value:"150430"}],[{label:"科尔沁区",value:"150502"},{label:"科尔沁左翼中旗",value:"150521"},{label:"科尔沁左翼后旗",value:"150522"},{label:"开鲁县",value:"150523"},{label:"库伦旗",value:"150524"},{label:"奈曼旗",value:"150525"},{label:"扎鲁特旗",value:"150526"},{label:"通辽经济技术开发区",value:"150571"},{label:"霍林郭勒市",value:"150581"}],[{label:"东胜区",value:"150602"},{label:"康巴什区",value:"150603"},{label:"达拉特旗",value:"150621"},{label:"准格尔旗",value:"150622"},{label:"鄂托克前旗",value:"150623"},{label:"鄂托克旗",value:"150624"},{label:"杭锦旗",value:"150625"},{label:"乌审旗",value:"150626"},{label:"伊金霍洛旗",value:"150627"}],[{label:"海拉尔区",value:"150702"},{label:"扎赉诺尔区",value:"150703"},{label:"阿荣旗",value:"150721"},{label:"莫力达瓦达斡尔族自治旗",value:"150722"},{label:"鄂伦春自治旗",value:"150723"},{label:"鄂温克族自治旗",value:"150724"},{label:"陈巴尔虎旗",value:"150725"},{label:"新巴尔虎左旗",value:"150726"},{label:"新巴尔虎右旗",value:"150727"},{label:"满洲里市",value:"150781"},{label:"牙克石市",value:"150782"},{label:"扎兰屯市",value:"150783"},{label:"额尔古纳市",value:"150784"},{label:"根河市",value:"150785"}],[{label:"临河区",value:"150802"},{label:"五原县",value:"150821"},{label:"磴口县",value:"150822"},{label:"乌拉特前旗",value:"150823"},{label:"乌拉特中旗",value:"150824"},{label:"乌拉特后旗",value:"150825"},{label:"杭锦后旗",value:"150826"}],[{label:"集宁区",value:"150902"},{label:"卓资县",value:"150921"},{label:"化德县",value:"150922"},{label:"商都县",value:"150923"},{label:"兴和县",value:"150924"},{label:"凉城县",value:"150925"},{label:"察哈尔右翼前旗",value:"150926"},{label:"察哈尔右翼中旗",value:"150927"},{label:"察哈尔右翼后旗",value:"150928"},{label:"四子王旗",value:"150929"},{label:"丰镇市",value:"150981"}],[{label:"乌兰浩特市",value:"152201"},{label:"阿尔山市",value:"152202"},{label:"科尔沁右翼前旗",value:"152221"},{label:"科尔沁右翼中旗",value:"152222"},{label:"扎赉特旗",value:"152223"},{label:"突泉县",value:"152224"}],[{label:"二连浩特市",value:"152501"},{label:"锡林浩特市",value:"152502"},{label:"阿巴嘎旗",value:"152522"},{label:"苏尼特左旗",value:"152523"},{label:"苏尼特右旗",value:"152524"},{label:"东乌珠穆沁旗",value:"152525"},{label:"西乌珠穆沁旗",value:"152526"},{label:"太仆寺旗",value:"152527"},{label:"镶黄旗",value:"152528"},{label:"正镶白旗",value:"152529"},{label:"正蓝旗",value:"152530"},{label:"多伦县",value:"152531"},{label:"乌拉盖管委会",value:"152571"}],[{label:"阿拉善左旗",value:"152921"},{label:"阿拉善右旗",value:"152922"},{label:"额济纳旗",value:"152923"},{label:"内蒙古阿拉善经济开发区",value:"152971"}]],[[{label:"和平区",value:"210102"},{label:"沈河区",value:"210103"},{label:"大东区",value:"210104"},{label:"皇姑区",value:"210105"},{label:"铁西区",value:"210106"},{label:"苏家屯区",value:"210111"},{label:"浑南区",value:"210112"},{label:"沈北新区",value:"210113"},{label:"于洪区",value:"210114"},{label:"辽中区",value:"210115"},{label:"康平县",value:"210123"},{label:"法库县",value:"210124"},{label:"新民市",value:"210181"}],[{label:"中山区",value:"210202"},{label:"西岗区",value:"210203"},{label:"沙河口区",value:"210204"},{label:"甘井子区",value:"210211"},{label:"旅顺口区",value:"210212"},{label:"金州区",value:"210213"},{label:"普兰店区",value:"210214"},{label:"长海县",value:"210224"},{label:"瓦房店市",value:"210281"},{label:"庄河市",value:"210283"}],[{label:"铁东区",value:"210302"},{label:"铁西区",value:"210303"},{label:"立山区",value:"210304"},{label:"千山区",value:"210311"},{label:"台安县",value:"210321"},{label:"岫岩满族自治县",value:"210323"},{label:"海城市",value:"210381"}],[{label:"新抚区",value:"210402"},{label:"东洲区",value:"210403"},{label:"望花区",value:"210404"},{label:"顺城区",value:"210411"},{label:"抚顺县",value:"210421"},{label:"新宾满族自治县",value:"210422"},{label:"清原满族自治县",value:"210423"}],[{label:"平山区",value:"210502"},{label:"溪湖区",value:"210503"},{label:"明山区",value:"210504"},{label:"南芬区",value:"210505"},{label:"本溪满族自治县",value:"210521"},{label:"桓仁满族自治县",value:"210522"}],[{label:"元宝区",value:"210602"},{label:"振兴区",value:"210603"},{label:"振安区",value:"210604"},{label:"宽甸满族自治县",value:"210624"},{label:"东港市",value:"210681"},{label:"凤城市",value:"210682"}],[{label:"古塔区",value:"210702"},{label:"凌河区",value:"210703"},{label:"太和区",value:"210711"},{label:"黑山县",value:"210726"},{label:"义县",value:"210727"},{label:"凌海市",value:"210781"},{label:"北镇市",value:"210782"}],[{label:"站前区",value:"210802"},{label:"西市区",value:"210803"},{label:"鲅鱼圈区",value:"210804"},{label:"老边区",value:"210811"},{label:"盖州市",value:"210881"},{label:"大石桥市",value:"210882"}],[{label:"海州区",value:"210902"},{label:"新邱区",value:"210903"},{label:"太平区",value:"210904"},{label:"清河门区",value:"210905"},{label:"细河区",value:"210911"},{label:"阜新蒙古族自治县",value:"210921"},{label:"彰武县",value:"210922"}],[{label:"白塔区",value:"211002"},{label:"文圣区",value:"211003"},{label:"宏伟区",value:"211004"},{label:"弓长岭区",value:"211005"},{label:"太子河区",value:"211011"},{label:"辽阳县",value:"211021"},{label:"灯塔市",value:"211081"}],[{label:"双台子区",value:"211102"},{label:"兴隆台区",value:"211103"},{label:"大洼区",value:"211104"},{label:"盘山县",value:"211122"}],[{label:"银州区",value:"211202"},{label:"清河区",value:"211204"},{label:"铁岭县",value:"211221"},{label:"西丰县",value:"211223"},{label:"昌图县",value:"211224"},{label:"调兵山市",value:"211281"},{label:"开原市",value:"211282"}],[{label:"双塔区",value:"211302"},{label:"龙城区",value:"211303"},{label:"朝阳县",value:"211321"},{label:"建平县",value:"211322"},{label:"喀喇沁左翼蒙古族自治县",value:"211324"},{label:"北票市",value:"211381"},{label:"凌源市",value:"211382"}],[{label:"连山区",value:"211402"},{label:"龙港区",value:"211403"},{label:"南票区",value:"211404"},{label:"绥中县",value:"211421"},{label:"建昌县",value:"211422"},{label:"兴城市",value:"211481"}]],[[{label:"南关区",value:"220102"},{label:"宽城区",value:"220103"},{label:"朝阳区",value:"220104"},{label:"二道区",value:"220105"},{label:"绿园区",value:"220106"},{label:"双阳区",value:"220112"},{label:"九台区",value:"220113"},{label:"农安县",value:"220122"},{label:"长春经济技术开发区",value:"220171"},{label:"长春净月高新技术产业开发区",value:"220172"},{label:"长春高新技术产业开发区",value:"220173"},{label:"长春汽车经济技术开发区",value:"220174"},{label:"榆树市",value:"220182"},{label:"德惠市",value:"220183"}],[{label:"昌邑区",value:"220202"},{label:"龙潭区",value:"220203"},{label:"船营区",value:"220204"},{label:"丰满区",value:"220211"},{label:"永吉县",value:"220221"},{label:"吉林经济开发区",value:"220271"},{label:"吉林高新技术产业开发区",value:"220272"},{label:"吉林中国新加坡食品区",value:"220273"},{label:"蛟河市",value:"220281"},{label:"桦甸市",value:"220282"},{label:"舒兰市",value:"220283"},{label:"磐石市",value:"220284"}],[{label:"铁西区",value:"220302"},{label:"铁东区",value:"220303"},{label:"梨树县",value:"220322"},{label:"伊通满族自治县",value:"220323"},{label:"公主岭市",value:"220381"},{label:"双辽市",value:"220382"}],[{label:"龙山区",value:"220402"},{label:"西安区",value:"220403"},{label:"东丰县",value:"220421"},{label:"东辽县",value:"220422"}],[{label:"东昌区",value:"220502"},{label:"二道江区",value:"220503"},{label:"通化县",value:"220521"},{label:"辉南县",value:"220523"},{label:"柳河县",value:"220524"},{label:"梅河口市",value:"220581"},{label:"集安市",value:"220582"}],[{label:"浑江区",value:"220602"},{label:"江源区",value:"220605"},{label:"抚松县",value:"220621"},{label:"靖宇县",value:"220622"},{label:"长白朝鲜族自治县",value:"220623"},{label:"临江市",value:"220681"}],[{label:"宁江区",value:"220702"},{label:"前郭尔罗斯蒙古族自治县",value:"220721"},{label:"长岭县",value:"220722"},{label:"乾安县",value:"220723"},{label:"吉林松原经济开发区",value:"220771"},{label:"扶余市",value:"220781"}],[{label:"洮北区",value:"220802"},{label:"镇赉县",value:"220821"},{label:"通榆县",value:"220822"},{label:"吉林白城经济开发区",value:"220871"},{label:"洮南市",value:"220881"},{label:"大安市",value:"220882"}],[{label:"延吉市",value:"222401"},{label:"图们市",value:"222402"},{label:"敦化市",value:"222403"},{label:"珲春市",value:"222404"},{label:"龙井市",value:"222405"},{label:"和龙市",value:"222406"},{label:"汪清县",value:"222424"},{label:"安图县",value:"222426"}]],[[{label:"道里区",value:"230102"},{label:"南岗区",value:"230103"},{label:"道外区",value:"230104"},{label:"平房区",value:"230108"},{label:"松北区",value:"230109"},{label:"香坊区",value:"230110"},{label:"呼兰区",value:"230111"},{label:"阿城区",value:"230112"},{label:"双城区",value:"230113"},{label:"依兰县",value:"230123"},{label:"方正县",value:"230124"},{label:"宾县",value:"230125"},{label:"巴彦县",value:"230126"},{label:"木兰县",value:"230127"},{label:"通河县",value:"230128"},{label:"延寿县",value:"230129"},{label:"尚志市",value:"230183"},{label:"五常市",value:"230184"}],[{label:"龙沙区",value:"230202"},{label:"建华区",value:"230203"},{label:"铁锋区",value:"230204"},{label:"昂昂溪区",value:"230205"},{label:"富拉尔基区",value:"230206"},{label:"碾子山区",value:"230207"},{label:"梅里斯达斡尔族区",value:"230208"},{label:"龙江县",value:"230221"},{label:"依安县",value:"230223"},{label:"泰来县",value:"230224"},{label:"甘南县",value:"230225"},{label:"富裕县",value:"230227"},{label:"克山县",value:"230229"},{label:"克东县",value:"230230"},{label:"拜泉县",value:"230231"},{label:"讷河市",value:"230281"}],[{label:"鸡冠区",value:"230302"},{label:"恒山区",value:"230303"},{label:"滴道区",value:"230304"},{label:"梨树区",value:"230305"},{label:"城子河区",value:"230306"},{label:"麻山区",value:"230307"},{label:"鸡东县",value:"230321"},{label:"虎林市",value:"230381"},{label:"密山市",value:"230382"}],[{label:"向阳区",value:"230402"},{label:"工农区",value:"230403"},{label:"南山区",value:"230404"},{label:"兴安区",value:"230405"},{label:"东山区",value:"230406"},{label:"兴山区",value:"230407"},{label:"萝北县",value:"230421"},{label:"绥滨县",value:"230422"}],[{label:"尖山区",value:"230502"},{label:"岭东区",value:"230503"},{label:"四方台区",value:"230505"},{label:"宝山区",value:"230506"},{label:"集贤县",value:"230521"},{label:"友谊县",value:"230522"},{label:"宝清县",value:"230523"},{label:"饶河县",value:"230524"}],[{label:"萨尔图区",value:"230602"},{label:"龙凤区",value:"230603"},{label:"让胡路区",value:"230604"},{label:"红岗区",value:"230605"},{label:"大同区",value:"230606"},{label:"肇州县",value:"230621"},{label:"肇源县",value:"230622"},{label:"林甸县",value:"230623"},{label:"杜尔伯特蒙古族自治县",value:"230624"},{label:"大庆高新技术产业开发区",value:"230671"}],[{label:"伊春区",value:"230702"},{label:"南岔区",value:"230703"},{label:"友好区",value:"230704"},{label:"西林区",value:"230705"},{label:"翠峦区",value:"230706"},{label:"新青区",value:"230707"},{label:"美溪区",value:"230708"},{label:"金山屯区",value:"230709"},{label:"五营区",value:"230710"},{label:"乌马河区",value:"230711"},{label:"汤旺河区",value:"230712"},{label:"带岭区",value:"230713"},{label:"乌伊岭区",value:"230714"},{label:"红星区",value:"230715"},{label:"上甘岭区",value:"230716"},{label:"嘉荫县",value:"230722"},{label:"铁力市",value:"230781"}],[{label:"向阳区",value:"230803"},{label:"前进区",value:"230804"},{label:"东风区",value:"230805"},{label:"郊区",value:"230811"},{label:"桦南县",value:"230822"},{label:"桦川县",value:"230826"},{label:"汤原县",value:"230828"},{label:"同江市",value:"230881"},{label:"富锦市",value:"230882"},{label:"抚远市",value:"230883"}],[{label:"新兴区",value:"230902"},{label:"桃山区",value:"230903"},{label:"茄子河区",value:"230904"},{label:"勃利县",value:"230921"}],[{label:"东安区",value:"231002"},{label:"阳明区",value:"231003"},{label:"爱民区",value:"231004"},{label:"西安区",value:"231005"},{label:"林口县",value:"231025"},{label:"牡丹江经济技术开发区",value:"231071"},{label:"绥芬河市",value:"231081"},{label:"海林市",value:"231083"},{label:"宁安市",value:"231084"},{label:"穆棱市",value:"231085"},{label:"东宁市",value:"231086"}],[{label:"爱辉区",value:"231102"},{label:"嫩江县",value:"231121"},{label:"逊克县",value:"231123"},{label:"孙吴县",value:"231124"},{label:"北安市",value:"231181"},{label:"五大连池市",value:"231182"}],[{label:"北林区",value:"231202"},{label:"望奎县",value:"231221"},{label:"兰西县",value:"231222"},{label:"青冈县",value:"231223"},{label:"庆安县",value:"231224"},{label:"明水县",value:"231225"},{label:"绥棱县",value:"231226"},{label:"安达市",value:"231281"},{label:"肇东市",value:"231282"},{label:"海伦市",value:"231283"}],[{label:"加格达奇区",value:"232701"},{label:"松岭区",value:"232702"},{label:"新林区",value:"232703"},{label:"呼中区",value:"232704"},{label:"呼玛县",value:"232721"},{label:"塔河县",value:"232722"},{label:"漠河县",value:"232723"}]],[[{label:"黄浦区",value:"310101"},{label:"徐汇区",value:"310104"},{label:"长宁区",value:"310105"},{label:"静安区",value:"310106"},{label:"普陀区",value:"310107"},{label:"虹口区",value:"310109"},{label:"杨浦区",value:"310110"},{label:"闵行区",value:"310112"},{label:"宝山区",value:"310113"},{label:"嘉定区",value:"310114"},{label:"浦东新区",value:"310115"},{label:"金山区",value:"310116"},{label:"松江区",value:"310117"},{label:"青浦区",value:"310118"},{label:"奉贤区",value:"310120"},{label:"崇明区",value:"310151"}]],[[{label:"玄武区",value:"320102"},{label:"秦淮区",value:"320104"},{label:"建邺区",value:"320105"},{label:"鼓楼区",value:"320106"},{label:"浦口区",value:"320111"},{label:"栖霞区",value:"320113"},{label:"雨花台区",value:"320114"},{label:"江宁区",value:"320115"},{label:"六合区",value:"320116"},{label:"溧水区",value:"320117"},{label:"高淳区",value:"320118"}],[{label:"锡山区",value:"320205"},{label:"惠山区",value:"320206"},{label:"滨湖区",value:"320211"},{label:"梁溪区",value:"320213"},{label:"新吴区",value:"320214"},{label:"江阴市",value:"320281"},{label:"宜兴市",value:"320282"}],[{label:"鼓楼区",value:"320302"},{label:"云龙区",value:"320303"},{label:"贾汪区",value:"320305"},{label:"泉山区",value:"320311"},{label:"铜山区",value:"320312"},{label:"丰县",value:"320321"},{label:"沛县",value:"320322"},{label:"睢宁县",value:"320324"},{label:"徐州经济技术开发区",value:"320371"},{label:"新沂市",value:"320381"},{label:"邳州市",value:"320382"}],[{label:"天宁区",value:"320402"},{label:"钟楼区",value:"320404"},{label:"新北区",value:"320411"},{label:"武进区",value:"320412"},{label:"金坛区",value:"320413"},{label:"溧阳市",value:"320481"}],[{label:"虎丘区",value:"320505"},{label:"吴中区",value:"320506"},{label:"相城区",value:"320507"},{label:"姑苏区",value:"320508"},{label:"吴江区",value:"320509"},{label:"苏州工业园区",value:"320571"},{label:"常熟市",value:"320581"},{label:"张家港市",value:"320582"},{label:"昆山市",value:"320583"},{label:"太仓市",value:"320585"}],[{label:"崇川区",value:"320602"},{label:"港闸区",value:"320611"},{label:"通州区",value:"320612"},{label:"海安县",value:"320621"},{label:"如东县",value:"320623"},{label:"南通经济技术开发区",value:"320671"},{label:"启东市",value:"320681"},{label:"如皋市",value:"320682"},{label:"海门市",value:"320684"}],[{label:"连云区",value:"320703"},{label:"海州区",value:"320706"},{label:"赣榆区",value:"320707"},{label:"东海县",value:"320722"},{label:"灌云县",value:"320723"},{label:"灌南县",value:"320724"},{label:"连云港经济技术开发区",value:"320771"},{label:"连云港高新技术产业开发区",value:"320772"}],[{label:"淮安区",value:"320803"},{label:"淮阴区",value:"320804"},{label:"清江浦区",value:"320812"},{label:"洪泽区",value:"320813"},{label:"涟水县",value:"320826"},{label:"盱眙县",value:"320830"},{label:"金湖县",value:"320831"},{label:"淮安经济技术开发区",value:"320871"}],[{label:"亭湖区",value:"320902"},{label:"盐都区",value:"320903"},{label:"大丰区",value:"320904"},{label:"响水县",value:"320921"},{label:"滨海县",value:"320922"},{label:"阜宁县",value:"320923"},{label:"射阳县",value:"320924"},{label:"建湖县",value:"320925"},{label:"盐城经济技术开发区",value:"320971"},{label:"东台市",value:"320981"}],[{label:"广陵区",value:"321002"},{label:"邗江区",value:"321003"},{label:"江都区",value:"321012"},{label:"宝应县",value:"321023"},{label:"扬州经济技术开发区",value:"321071"},{label:"仪征市",value:"321081"},{label:"高邮市",value:"321084"}],[{label:"京口区",value:"321102"},{label:"润州区",value:"321111"},{label:"丹徒区",value:"321112"},{label:"镇江新区",value:"321171"},{label:"丹阳市",value:"321181"},{label:"扬中市",value:"321182"},{label:"句容市",value:"321183"}],[{label:"海陵区",value:"321202"},{label:"高港区",value:"321203"},{label:"姜堰区",value:"321204"},{label:"泰州医药高新技术产业开发区",value:"321271"},{label:"兴化市",value:"321281"},{label:"靖江市",value:"321282"},{label:"泰兴市",value:"321283"}],[{label:"宿城区",value:"321302"},{label:"宿豫区",value:"321311"},{label:"沭阳县",value:"321322"},{label:"泗阳县",value:"321323"},{label:"泗洪县",value:"321324"},{label:"宿迁经济技术开发区",value:"321371"}]],[[{label:"上城区",value:"330102"},{label:"下城区",value:"330103"},{label:"江干区",value:"330104"},{label:"拱墅区",value:"330105"},{label:"西湖区",value:"330106"},{label:"滨江区",value:"330108"},{label:"萧山区",value:"330109"},{label:"余杭区",value:"330110"},{label:"富阳区",value:"330111"},{label:"临安区",value:"330112"},{label:"桐庐县",value:"330122"},{label:"淳安县",value:"330127"},{label:"建德市",value:"330182"}],[{label:"海曙区",value:"330203"},{label:"江北区",value:"330205"},{label:"北仑区",value:"330206"},{label:"镇海区",value:"330211"},{label:"鄞州区",value:"330212"},{label:"奉化区",value:"330213"},{label:"象山县",value:"330225"},{label:"宁海县",value:"330226"},{label:"余姚市",value:"330281"},{label:"慈溪市",value:"330282"}],[{label:"鹿城区",value:"330302"},{label:"龙湾区",value:"330303"},{label:"瓯海区",value:"330304"},{label:"洞头区",value:"330305"},{label:"永嘉县",value:"330324"},{label:"平阳县",value:"330326"},{label:"苍南县",value:"330327"},{label:"文成县",value:"330328"},{label:"泰顺县",value:"330329"},{label:"温州经济技术开发区",value:"330371"},{label:"瑞安市",value:"330381"},{label:"乐清市",value:"330382"}],[{label:"南湖区",value:"330402"},{label:"秀洲区",value:"330411"},{label:"嘉善县",value:"330421"},{label:"海盐县",value:"330424"},{label:"海宁市",value:"330481"},{label:"平湖市",value:"330482"},{label:"桐乡市",value:"330483"}],[{label:"吴兴区",value:"330502"},{label:"南浔区",value:"330503"},{label:"德清县",value:"330521"},{label:"长兴县",value:"330522"},{label:"安吉县",value:"330523"}],[{label:"越城区",value:"330602"},{label:"柯桥区",value:"330603"},{label:"上虞区",value:"330604"},{label:"新昌县",value:"330624"},{label:"诸暨市",value:"330681"},{label:"嵊州市",value:"330683"}],[{label:"婺城区",value:"330702"},{label:"金东区",value:"330703"},{label:"武义县",value:"330723"},{label:"浦江县",value:"330726"},{label:"磐安县",value:"330727"},{label:"兰溪市",value:"330781"},{label:"义乌市",value:"330782"},{label:"东阳市",value:"330783"},{label:"永康市",value:"330784"}],[{label:"柯城区",value:"330802"},{label:"衢江区",value:"330803"},{label:"常山县",value:"330822"},{label:"开化县",value:"330824"},{label:"龙游县",value:"330825"},{label:"江山市",value:"330881"}],[{label:"定海区",value:"330902"},{label:"普陀区",value:"330903"},{label:"岱山县",value:"330921"},{label:"嵊泗县",value:"330922"}],[{label:"椒江区",value:"331002"},{label:"黄岩区",value:"331003"},{label:"路桥区",value:"331004"},{label:"三门县",value:"331022"},{label:"天台县",value:"331023"},{label:"仙居县",value:"331024"},{label:"温岭市",value:"331081"},{label:"临海市",value:"331082"},{label:"玉环市",value:"331083"}],[{label:"莲都区",value:"331102"},{label:"青田县",value:"331121"},{label:"缙云县",value:"331122"},{label:"遂昌县",value:"331123"},{label:"松阳县",value:"331124"},{label:"云和县",value:"331125"},{label:"庆元县",value:"331126"},{label:"景宁畲族自治县",value:"331127"},{label:"龙泉市",value:"331181"}]],[[{label:"瑶海区",value:"340102"},{label:"庐阳区",value:"340103"},{label:"蜀山区",value:"340104"},{label:"包河区",value:"340111"},{label:"长丰县",value:"340121"},{label:"肥东县",value:"340122"},{label:"肥西县",value:"340123"},{label:"庐江县",value:"340124"},{label:"合肥高新技术产业开发区",value:"340171"},{label:"合肥经济技术开发区",value:"340172"},{label:"合肥新站高新技术产业开发区",value:"340173"},{label:"巢湖市",value:"340181"}],[{label:"镜湖区",value:"340202"},{label:"弋江区",value:"340203"},{label:"鸠江区",value:"340207"},{label:"三山区",value:"340208"},{label:"芜湖县",value:"340221"},{label:"繁昌县",value:"340222"},{label:"南陵县",value:"340223"},{label:"无为县",value:"340225"},{label:"芜湖经济技术开发区",value:"340271"},{label:"安徽芜湖长江大桥经济开发区",value:"340272"}],[{label:"龙子湖区",value:"340302"},{label:"蚌山区",value:"340303"},{label:"禹会区",value:"340304"},{label:"淮上区",value:"340311"},{label:"怀远县",value:"340321"},{label:"五河县",value:"340322"},{label:"固镇县",value:"340323"},{label:"蚌埠市高新技术开发区",value:"340371"},{label:"蚌埠市经济开发区",value:"340372"}],[{label:"大通区",value:"340402"},{label:"田家庵区",value:"340403"},{label:"谢家集区",value:"340404"},{label:"八公山区",value:"340405"},{label:"潘集区",value:"340406"},{label:"凤台县",value:"340421"},{label:"寿县",value:"340422"}],[{label:"花山区",value:"340503"},{label:"雨山区",value:"340504"},{label:"博望区",value:"340506"},{label:"当涂县",value:"340521"},{label:"含山县",value:"340522"},{label:"和县",value:"340523"}],[{label:"杜集区",value:"340602"},{label:"相山区",value:"340603"},{label:"烈山区",value:"340604"},{label:"濉溪县",value:"340621"}],[{label:"铜官区",value:"340705"},{label:"义安区",value:"340706"},{label:"郊区",value:"340711"},{label:"枞阳县",value:"340722"}],[{label:"迎江区",value:"340802"},{label:"大观区",value:"340803"},{label:"宜秀区",value:"340811"},{label:"怀宁县",value:"340822"},{label:"潜山县",value:"340824"},{label:"太湖县",value:"340825"},{label:"宿松县",value:"340826"},{label:"望江县",value:"340827"},{label:"岳西县",value:"340828"},{label:"安徽安庆经济开发区",value:"340871"},{label:"桐城市",value:"340881"}],[{label:"屯溪区",value:"341002"},{label:"黄山区",value:"341003"},{label:"徽州区",value:"341004"},{label:"歙县",value:"341021"},{label:"休宁县",value:"341022"},{label:"黟县",value:"341023"},{label:"祁门县",value:"341024"}],[{label:"琅琊区",value:"341102"},{label:"南谯区",value:"341103"},{label:"来安县",value:"341122"},{label:"全椒县",value:"341124"},{label:"定远县",value:"341125"},{label:"凤阳县",value:"341126"},{label:"苏滁现代产业园",value:"341171"},{label:"滁州经济技术开发区",value:"341172"},{label:"天长市",value:"341181"},{label:"明光市",value:"341182"}],[{label:"颍州区",value:"341202"},{label:"颍东区",value:"341203"},{label:"颍泉区",value:"341204"},{label:"临泉县",value:"341221"},{label:"太和县",value:"341222"},{label:"阜南县",value:"341225"},{label:"颍上县",value:"341226"},{label:"阜阳合肥现代产业园区",value:"341271"},{label:"阜阳经济技术开发区",value:"341272"},{label:"界首市",value:"341282"}],[{label:"埇桥区",value:"341302"},{label:"砀山县",value:"341321"},{label:"萧县",value:"341322"},{label:"灵璧县",value:"341323"},{label:"泗县",value:"341324"},{label:"宿州马鞍山现代产业园区",value:"341371"},{label:"宿州经济技术开发区",value:"341372"}],[{label:"金安区",value:"341502"},{label:"裕安区",value:"341503"},{label:"叶集区",value:"341504"},{label:"霍邱县",value:"341522"},{label:"舒城县",value:"341523"},{label:"金寨县",value:"341524"},{label:"霍山县",value:"341525"}],[{label:"谯城区",value:"341602"},{label:"涡阳县",value:"341621"},{label:"蒙城县",value:"341622"},{label:"利辛县",value:"341623"}],[{label:"贵池区",value:"341702"},{label:"东至县",value:"341721"},{label:"石台县",value:"341722"},{label:"青阳县",value:"341723"}],[{label:"宣州区",value:"341802"},{label:"郎溪县",value:"341821"},{label:"广德县",value:"341822"},{label:"泾县",value:"341823"},{label:"绩溪县",value:"341824"},{label:"旌德县",value:"341825"},{label:"宣城市经济开发区",value:"341871"},{label:"宁国市",value:"341881"}]],[[{label:"鼓楼区",value:"350102"},{label:"台江区",value:"350103"},{label:"仓山区",value:"350104"},{label:"马尾区",value:"350105"},{label:"晋安区",value:"350111"},{label:"闽侯县",value:"350121"},{label:"连江县",value:"350122"},{label:"罗源县",value:"350123"},{label:"闽清县",value:"350124"},{label:"永泰县",value:"350125"},{label:"平潭县",value:"350128"},{label:"福清市",value:"350181"},{label:"长乐市",value:"350182"}],[{label:"思明区",value:"350203"},{label:"海沧区",value:"350205"},{label:"湖里区",value:"350206"},{label:"集美区",value:"350211"},{label:"同安区",value:"350212"},{label:"翔安区",value:"350213"}],[{label:"城厢区",value:"350302"},{label:"涵江区",value:"350303"},{label:"荔城区",value:"350304"},{label:"秀屿区",value:"350305"},{label:"仙游县",value:"350322"}],[{label:"梅列区",value:"350402"},{label:"三元区",value:"350403"},{label:"明溪县",value:"350421"},{label:"清流县",value:"350423"},{label:"宁化县",value:"350424"},{label:"大田县",value:"350425"},{label:"尤溪县",value:"350426"},{label:"沙县",value:"350427"},{label:"将乐县",value:"350428"},{label:"泰宁县",value:"350429"},{label:"建宁县",value:"350430"},{label:"永安市",value:"350481"}],[{label:"鲤城区",value:"350502"},{label:"丰泽区",value:"350503"},{label:"洛江区",value:"350504"},{label:"泉港区",value:"350505"},{label:"惠安县",value:"350521"},{label:"安溪县",value:"350524"},{label:"永春县",value:"350525"},{label:"德化县",value:"350526"},{label:"金门县",value:"350527"},{label:"石狮市",value:"350581"},{label:"晋江市",value:"350582"},{label:"南安市",value:"350583"}],[{label:"芗城区",value:"350602"},{label:"龙文区",value:"350603"},{label:"云霄县",value:"350622"},{label:"漳浦县",value:"350623"},{label:"诏安县",value:"350624"},{label:"长泰县",value:"350625"},{label:"东山县",value:"350626"},{label:"南靖县",value:"350627"},{label:"平和县",value:"350628"},{label:"华安县",value:"350629"},{label:"龙海市",value:"350681"}],[{label:"延平区",value:"350702"},{label:"建阳区",value:"350703"},{label:"顺昌县",value:"350721"},{label:"浦城县",value:"350722"},{label:"光泽县",value:"350723"},{label:"松溪县",value:"350724"},{label:"政和县",value:"350725"},{label:"邵武市",value:"350781"},{label:"武夷山市",value:"350782"},{label:"建瓯市",value:"350783"}],[{label:"新罗区",value:"350802"},{label:"永定区",value:"350803"},{label:"长汀县",value:"350821"},{label:"上杭县",value:"350823"},{label:"武平县",value:"350824"},{label:"连城县",value:"350825"},{label:"漳平市",value:"350881"}],[{label:"蕉城区",value:"350902"},{label:"霞浦县",value:"350921"},{label:"古田县",value:"350922"},{label:"屏南县",value:"350923"},{label:"寿宁县",value:"350924"},{label:"周宁县",value:"350925"},{label:"柘荣县",value:"350926"},{label:"福安市",value:"350981"},{label:"福鼎市",value:"350982"}]],[[{label:"东湖区",value:"360102"},{label:"西湖区",value:"360103"},{label:"青云谱区",value:"360104"},{label:"湾里区",value:"360105"},{label:"青山湖区",value:"360111"},{label:"新建区",value:"360112"},{label:"南昌县",value:"360121"},{label:"安义县",value:"360123"},{label:"进贤县",value:"360124"}],[{label:"昌江区",value:"360202"},{label:"珠山区",value:"360203"},{label:"浮梁县",value:"360222"},{label:"乐平市",value:"360281"}],[{label:"安源区",value:"360302"},{label:"湘东区",value:"360313"},{label:"莲花县",value:"360321"},{label:"上栗县",value:"360322"},{label:"芦溪县",value:"360323"}],[{label:"濂溪区",value:"360402"},{label:"浔阳区",value:"360403"},{label:"柴桑区",value:"360404"},{label:"武宁县",value:"360423"},{label:"修水县",value:"360424"},{label:"永修县",value:"360425"},{label:"德安县",value:"360426"},{label:"都昌县",value:"360428"},{label:"湖口县",value:"360429"},{label:"彭泽县",value:"360430"},{label:"瑞昌市",value:"360481"},{label:"共青城市",value:"360482"},{label:"庐山市",value:"360483"}],[{label:"渝水区",value:"360502"},{label:"分宜县",value:"360521"}],[{label:"月湖区",value:"360602"},{label:"余江县",value:"360622"},{label:"贵溪市",value:"360681"}],[{label:"章贡区",value:"360702"},{label:"南康区",value:"360703"},{label:"赣县区",value:"360704"},{label:"信丰县",value:"360722"},{label:"大余县",value:"360723"},{label:"上犹县",value:"360724"},{label:"崇义县",value:"360725"},{label:"安远县",value:"360726"},{label:"龙南县",value:"360727"},{label:"定南县",value:"360728"},{label:"全南县",value:"360729"},{label:"宁都县",value:"360730"},{label:"于都县",value:"360731"},{label:"兴国县",value:"360732"},{label:"会昌县",value:"360733"},{label:"寻乌县",value:"360734"},{label:"石城县",value:"360735"},{label:"瑞金市",value:"360781"}],[{label:"吉州区",value:"360802"},{label:"青原区",value:"360803"},{label:"吉安县",value:"360821"},{label:"吉水县",value:"360822"},{label:"峡江县",value:"360823"},{label:"新干县",value:"360824"},{label:"永丰县",value:"360825"},{label:"泰和县",value:"360826"},{label:"遂川县",value:"360827"},{label:"万安县",value:"360828"},{label:"安福县",value:"360829"},{label:"永新县",value:"360830"},{label:"井冈山市",value:"360881"}],[{label:"袁州区",value:"360902"},{label:"奉新县",value:"360921"},{label:"万载县",value:"360922"},{label:"上高县",value:"360923"},{label:"宜丰县",value:"360924"},{label:"靖安县",value:"360925"},{label:"铜鼓县",value:"360926"},{label:"丰城市",value:"360981"},{label:"樟树市",value:"360982"},{label:"高安市",value:"360983"}],[{label:"临川区",value:"361002"},{label:"东乡区",value:"361003"},{label:"南城县",value:"361021"},{label:"黎川县",value:"361022"},{label:"南丰县",value:"361023"},{label:"崇仁县",value:"361024"},{label:"乐安县",value:"361025"},{label:"宜黄县",value:"361026"},{label:"金溪县",value:"361027"},{label:"资溪县",value:"361028"},{label:"广昌县",value:"361030"}],[{label:"信州区",value:"361102"},{label:"广丰区",value:"361103"},{label:"上饶县",value:"361121"},{label:"玉山县",value:"361123"},{label:"铅山县",value:"361124"},{label:"横峰县",value:"361125"},{label:"弋阳县",value:"361126"},{label:"余干县",value:"361127"},{label:"鄱阳县",value:"361128"},{label:"万年县",value:"361129"},{label:"婺源县",value:"361130"},{label:"德兴市",value:"361181"}]],[[{label:"历下区",value:"370102"},{label:"市中区",value:"370103"},{label:"槐荫区",value:"370104"},{label:"天桥区",value:"370105"},{label:"历城区",value:"370112"},{label:"长清区",value:"370113"},{label:"章丘区",value:"370114"},{label:"平阴县",value:"370124"},{label:"济阳县",value:"370125"},{label:"商河县",value:"370126"},{label:"济南高新技术产业开发区",value:"370171"}],[{label:"市南区",value:"370202"},{label:"市北区",value:"370203"},{label:"黄岛区",value:"370211"},{label:"崂山区",value:"370212"},{label:"李沧区",value:"370213"},{label:"城阳区",value:"370214"},{label:"即墨区",value:"370215"},{label:"青岛高新技术产业开发区",value:"370271"},{label:"胶州市",value:"370281"},{label:"平度市",value:"370283"},{label:"莱西市",value:"370285"}],[{label:"淄川区",value:"370302"},{label:"张店区",value:"370303"},{label:"博山区",value:"370304"},{label:"临淄区",value:"370305"},{label:"周村区",value:"370306"},{label:"桓台县",value:"370321"},{label:"高青县",value:"370322"},{label:"沂源县",value:"370323"}],[{label:"市中区",value:"370402"},{label:"薛城区",value:"370403"},{label:"峄城区",value:"370404"},{label:"台儿庄区",value:"370405"},{label:"山亭区",value:"370406"},{label:"滕州市",value:"370481"}],[{label:"东营区",value:"370502"},{label:"河口区",value:"370503"},{label:"垦利区",value:"370505"},{label:"利津县",value:"370522"},{label:"广饶县",value:"370523"},{label:"东营经济技术开发区",value:"370571"},{label:"东营港经济开发区",value:"370572"}],[{label:"芝罘区",value:"370602"},{label:"福山区",value:"370611"},{label:"牟平区",value:"370612"},{label:"莱山区",value:"370613"},{label:"长岛县",value:"370634"},{label:"烟台高新技术产业开发区",value:"370671"},{label:"烟台经济技术开发区",value:"370672"},{label:"龙口市",value:"370681"},{label:"莱阳市",value:"370682"},{label:"莱州市",value:"370683"},{label:"蓬莱市",value:"370684"},{label:"招远市",value:"370685"},{label:"栖霞市",value:"370686"},{label:"海阳市",value:"370687"}],[{label:"潍城区",value:"370702"},{label:"寒亭区",value:"370703"},{label:"坊子区",value:"370704"},{label:"奎文区",value:"370705"},{label:"临朐县",value:"370724"},{label:"昌乐县",value:"370725"},{label:"潍坊滨海经济技术开发区",value:"370772"},{label:"青州市",value:"370781"},{label:"诸城市",value:"370782"},{label:"寿光市",value:"370783"},{label:"安丘市",value:"370784"},{label:"高密市",value:"370785"},{label:"昌邑市",value:"370786"}],[{label:"任城区",value:"370811"},{label:"兖州区",value:"370812"},{label:"微山县",value:"370826"},{label:"鱼台县",value:"370827"},{label:"金乡县",value:"370828"},{label:"嘉祥县",value:"370829"},{label:"汶上县",value:"370830"},{label:"泗水县",value:"370831"},{label:"梁山县",value:"370832"},{label:"济宁高新技术产业开发区",value:"370871"},{label:"曲阜市",value:"370881"},{label:"邹城市",value:"370883"}],[{label:"泰山区",value:"370902"},{label:"岱岳区",value:"370911"},{label:"宁阳县",value:"370921"},{label:"东平县",value:"370923"},{label:"新泰市",value:"370982"},{label:"肥城市",value:"370983"}],[{label:"环翠区",value:"371002"},{label:"文登区",value:"371003"},{label:"威海火炬高技术产业开发区",value:"371071"},{label:"威海经济技术开发区",value:"371072"},{label:"威海临港经济技术开发区",value:"371073"},{label:"荣成市",value:"371082"},{label:"乳山市",value:"371083"}],[{label:"东港区",value:"371102"},{label:"岚山区",value:"371103"},{label:"五莲县",value:"371121"},{label:"莒县",value:"371122"},{label:"日照经济技术开发区",value:"371171"},{label:"日照国际海洋城",value:"371172"}],[{label:"莱城区",value:"371202"},{label:"钢城区",value:"371203"}],[{label:"兰山区",value:"371302"},{label:"罗庄区",value:"371311"},{label:"河东区",value:"371312"},{label:"沂南县",value:"371321"},{label:"郯城县",value:"371322"},{label:"沂水县",value:"371323"},{label:"兰陵县",value:"371324"},{label:"费县",value:"371325"},{label:"平邑县",value:"371326"},{label:"莒南县",value:"371327"},{label:"蒙阴县",value:"371328"},{label:"临沭县",value:"371329"},{label:"临沂高新技术产业开发区",value:"371371"},{label:"临沂经济技术开发区",value:"371372"},{label:"临沂临港经济开发区",value:"371373"}],[{label:"德城区",value:"371402"},{label:"陵城区",value:"371403"},{label:"宁津县",value:"371422"},{label:"庆云县",value:"371423"},{label:"临邑县",value:"371424"},{label:"齐河县",value:"371425"},{label:"平原县",value:"371426"},{label:"夏津县",value:"371427"},{label:"武城县",value:"371428"},{label:"德州经济技术开发区",value:"371471"},{label:"德州运河经济开发区",value:"371472"},{label:"乐陵市",value:"371481"},{label:"禹城市",value:"371482"}],[{label:"东昌府区",value:"371502"},{label:"阳谷县",value:"371521"},{label:"莘县",value:"371522"},{label:"茌平县",value:"371523"},{label:"东阿县",value:"371524"},{label:"冠县",value:"371525"},{label:"高唐县",value:"371526"},{label:"临清市",value:"371581"}],[{label:"滨城区",value:"371602"},{label:"沾化区",value:"371603"},{label:"惠民县",value:"371621"},{label:"阳信县",value:"371622"},{label:"无棣县",value:"371623"},{label:"博兴县",value:"371625"},{label:"邹平县",value:"371626"}],[{label:"牡丹区",value:"371702"},{label:"定陶区",value:"371703"},{label:"曹县",value:"371721"},{label:"单县",value:"371722"},{label:"成武县",value:"371723"},{label:"巨野县",value:"371724"},{label:"郓城县",value:"371725"},{label:"鄄城县",value:"371726"},{label:"东明县",value:"371728"},{label:"菏泽经济技术开发区",value:"371771"},{label:"菏泽高新技术开发区",value:"371772"}]],[[{label:"中原区",value:"410102"},{label:"二七区",value:"410103"},{label:"管城回族区",value:"410104"},{label:"金水区",value:"410105"},{label:"上街区",value:"410106"},{label:"惠济区",value:"410108"},{label:"中牟县",value:"410122"},{label:"郑州经济技术开发区",value:"410171"},{label:"郑州高新技术产业开发区",value:"410172"},{label:"郑州航空港经济综合实验区",value:"410173"},{label:"巩义市",value:"410181"},{label:"荥阳市",value:"410182"},{label:"新密市",value:"410183"},{label:"新郑市",value:"410184"},{label:"登封市",value:"410185"}],[{label:"龙亭区",value:"410202"},{label:"顺河回族区",value:"410203"},{label:"鼓楼区",value:"410204"},{label:"禹王台区",value:"410205"},{label:"祥符区",value:"410212"},{label:"杞县",value:"410221"},{label:"通许县",value:"410222"},{label:"尉氏县",value:"410223"},{label:"兰考县",value:"410225"}],[{label:"老城区",value:"410302"},{label:"西工区",value:"410303"},{label:"瀍河回族区",value:"410304"},{label:"涧西区",value:"410305"},{label:"吉利区",value:"410306"},{label:"洛龙区",value:"410311"},{label:"孟津县",value:"410322"},{label:"新安县",value:"410323"},{label:"栾川县",value:"410324"},{label:"嵩县",value:"410325"},{label:"汝阳县",value:"410326"},{label:"宜阳县",value:"410327"},{label:"洛宁县",value:"410328"},{label:"伊川县",value:"410329"},{label:"洛阳高新技术产业开发区",value:"410371"},{label:"偃师市",value:"410381"}],[{label:"新华区",value:"410402"},{label:"卫东区",value:"410403"},{label:"石龙区",value:"410404"},{label:"湛河区",value:"410411"},{label:"宝丰县",value:"410421"},{label:"叶县",value:"410422"},{label:"鲁山县",value:"410423"},{label:"郏县",value:"410425"},{label:"平顶山高新技术产业开发区",value:"410471"},{label:"平顶山市新城区",value:"410472"},{label:"舞钢市",value:"410481"},{label:"汝州市",value:"410482"}],[{label:"文峰区",value:"410502"},{label:"北关区",value:"410503"},{label:"殷都区",value:"410505"},{label:"龙安区",value:"410506"},{label:"安阳县",value:"410522"},{label:"汤阴县",value:"410523"},{label:"滑县",value:"410526"},{label:"内黄县",value:"410527"},{label:"安阳高新技术产业开发区",value:"410571"},{label:"林州市",value:"410581"}],[{label:"鹤山区",value:"410602"},{label:"山城区",value:"410603"},{label:"淇滨区",value:"410611"},{label:"浚县",value:"410621"},{label:"淇县",value:"410622"},{label:"鹤壁经济技术开发区",value:"410671"}],[{label:"红旗区",value:"410702"},{label:"卫滨区",value:"410703"},{label:"凤泉区",value:"410704"},{label:"牧野区",value:"410711"},{label:"新乡县",value:"410721"},{label:"获嘉县",value:"410724"},{label:"原阳县",value:"410725"},{label:"延津县",value:"410726"},{label:"封丘县",value:"410727"},{label:"长垣县",value:"410728"},{label:"新乡高新技术产业开发区",value:"410771"},{label:"新乡经济技术开发区",value:"410772"},{label:"新乡市平原城乡一体化示范区",value:"410773"},{label:"卫辉市",value:"410781"},{label:"辉县市",value:"410782"}],[{label:"解放区",value:"410802"},{label:"中站区",value:"410803"},{label:"马村区",value:"410804"},{label:"山阳区",value:"410811"},{label:"修武县",value:"410821"},{label:"博爱县",value:"410822"},{label:"武陟县",value:"410823"},{label:"温县",value:"410825"},{label:"焦作城乡一体化示范区",value:"410871"},{label:"沁阳市",value:"410882"},{label:"孟州市",value:"410883"}],[{label:"华龙区",value:"410902"},{label:"清丰县",value:"410922"},{label:"南乐县",value:"410923"},{label:"范县",value:"410926"},{label:"台前县",value:"410927"},{label:"濮阳县",value:"410928"},{label:"河南濮阳工业园区",value:"410971"},{label:"濮阳经济技术开发区",value:"410972"}],[{label:"魏都区",value:"411002"},{label:"建安区",value:"411003"},{label:"鄢陵县",value:"411024"},{label:"襄城县",value:"411025"},{label:"许昌经济技术开发区",value:"411071"},{label:"禹州市",value:"411081"},{label:"长葛市",value:"411082"}],[{label:"源汇区",value:"411102"},{label:"郾城区",value:"411103"},{label:"召陵区",value:"411104"},{label:"舞阳县",value:"411121"},{label:"临颍县",value:"411122"},{label:"漯河经济技术开发区",value:"411171"}],[{label:"湖滨区",value:"411202"},{label:"陕州区",value:"411203"},{label:"渑池县",value:"411221"},{label:"卢氏县",value:"411224"},{label:"河南三门峡经济开发区",value:"411271"},{label:"义马市",value:"411281"},{label:"灵宝市",value:"411282"}],[{label:"宛城区",value:"411302"},{label:"卧龙区",value:"411303"},{label:"南召县",value:"411321"},{label:"方城县",value:"411322"},{label:"西峡县",value:"411323"},{label:"镇平县",value:"411324"},{label:"内乡县",value:"411325"},{label:"淅川县",value:"411326"},{label:"社旗县",value:"411327"},{label:"唐河县",value:"411328"},{label:"新野县",value:"411329"},{label:"桐柏县",value:"411330"},{label:"南阳高新技术产业开发区",value:"411371"},{label:"南阳市城乡一体化示范区",value:"411372"},{label:"邓州市",value:"411381"}],[{label:"梁园区",value:"411402"},{label:"睢阳区",value:"411403"},{label:"民权县",value:"411421"},{label:"睢县",value:"411422"},{label:"宁陵县",value:"411423"},{label:"柘城县",value:"411424"},{label:"虞城县",value:"411425"},{label:"夏邑县",value:"411426"},{label:"豫东综合物流产业聚集区",value:"411471"},{label:"河南商丘经济开发区",value:"411472"},{label:"永城市",value:"411481"}],[{label:"浉河区",value:"411502"},{label:"平桥区",value:"411503"},{label:"罗山县",value:"411521"},{label:"光山县",value:"411522"},{label:"新县",value:"411523"},{label:"商城县",value:"411524"},{label:"固始县",value:"411525"},{label:"潢川县",value:"411526"},{label:"淮滨县",value:"411527"},{label:"息县",value:"411528"},{label:"信阳高新技术产业开发区",value:"411571"}],[{label:"川汇区",value:"411602"},{label:"扶沟县",value:"411621"},{label:"西华县",value:"411622"},{label:"商水县",value:"411623"},{label:"沈丘县",value:"411624"},{label:"郸城县",value:"411625"},{label:"淮阳县",value:"411626"},{label:"太康县",value:"411627"},{label:"鹿邑县",value:"411628"},{label:"河南周口经济开发区",value:"411671"},{label:"项城市",value:"411681"}],[{label:"驿城区",value:"411702"},{label:"西平县",value:"411721"},{label:"上蔡县",value:"411722"},{label:"平舆县",value:"411723"},{label:"正阳县",value:"411724"},{label:"确山县",value:"411725"},{label:"泌阳县",value:"411726"},{label:"汝南县",value:"411727"},{label:"遂平县",value:"411728"},{label:"新蔡县",value:"411729"},{label:"河南驻马店经济开发区",value:"411771"}],[{label:"济源市",value:"419001"}]],[[{label:"江岸区",value:"420102"},{label:"江汉区",value:"420103"},{label:"硚口区",value:"420104"},{label:"汉阳区",value:"420105"},{label:"武昌区",value:"420106"},{label:"青山区",value:"420107"},{label:"洪山区",value:"420111"},{label:"东西湖区",value:"420112"},{label:"汉南区",value:"420113"},{label:"蔡甸区",value:"420114"},{label:"江夏区",value:"420115"},{label:"黄陂区",value:"420116"},{label:"新洲区",value:"420117"}],[{label:"黄石港区",value:"420202"},{label:"西塞山区",value:"420203"},{label:"下陆区",value:"420204"},{label:"铁山区",value:"420205"},{label:"阳新县",value:"420222"},{label:"大冶市",value:"420281"}],[{label:"茅箭区",value:"420302"},{label:"张湾区",value:"420303"},{label:"郧阳区",value:"420304"},{label:"郧西县",value:"420322"},{label:"竹山县",value:"420323"},{label:"竹溪县",value:"420324"},{label:"房县",value:"420325"},{label:"丹江口市",value:"420381"}],[{label:"西陵区",value:"420502"},{label:"伍家岗区",value:"420503"},{label:"点军区",value:"420504"},{label:"猇亭区",value:"420505"},{label:"夷陵区",value:"420506"},{label:"远安县",value:"420525"},{label:"兴山县",value:"420526"},{label:"秭归县",value:"420527"},{label:"长阳土家族自治县",value:"420528"},{label:"五峰土家族自治县",value:"420529"},{label:"宜都市",value:"420581"},{label:"当阳市",value:"420582"},{label:"枝江市",value:"420583"}],[{label:"襄城区",value:"420602"},{label:"樊城区",value:"420606"},{label:"襄州区",value:"420607"},{label:"南漳县",value:"420624"},{label:"谷城县",value:"420625"},{label:"保康县",value:"420626"},{label:"老河口市",value:"420682"},{label:"枣阳市",value:"420683"},{label:"宜城市",value:"420684"}],[{label:"梁子湖区",value:"420702"},{label:"华容区",value:"420703"},{label:"鄂城区",value:"420704"}],[{label:"东宝区",value:"420802"},{label:"掇刀区",value:"420804"},{label:"京山县",value:"420821"},{label:"沙洋县",value:"420822"},{label:"钟祥市",value:"420881"}],[{label:"孝南区",value:"420902"},{label:"孝昌县",value:"420921"},{label:"大悟县",value:"420922"},{label:"云梦县",value:"420923"},{label:"应城市",value:"420981"},{label:"安陆市",value:"420982"},{label:"汉川市",value:"420984"}],[{label:"沙市区",value:"421002"},{label:"荆州区",value:"421003"},{label:"公安县",value:"421022"},{label:"监利县",value:"421023"},{label:"江陵县",value:"421024"},{label:"荆州经济技术开发区",value:"421071"},{label:"石首市",value:"421081"},{label:"洪湖市",value:"421083"},{label:"松滋市",value:"421087"}],[{label:"黄州区",value:"421102"},{label:"团风县",value:"421121"},{label:"红安县",value:"421122"},{label:"罗田县",value:"421123"},{label:"英山县",value:"421124"},{label:"浠水县",value:"421125"},{label:"蕲春县",value:"421126"},{label:"黄梅县",value:"421127"},{label:"龙感湖管理区",value:"421171"},{label:"麻城市",value:"421181"},{label:"武穴市",value:"421182"}],[{label:"咸安区",value:"421202"},{label:"嘉鱼县",value:"421221"},{label:"通城县",value:"421222"},{label:"崇阳县",value:"421223"},{label:"通山县",value:"421224"},{label:"赤壁市",value:"421281"}],[{label:"曾都区",value:"421303"},{label:"随县",value:"421321"},{label:"广水市",value:"421381"}],[{label:"恩施市",value:"422801"},{label:"利川市",value:"422802"},{label:"建始县",value:"422822"},{label:"巴东县",value:"422823"},{label:"宣恩县",value:"422825"},{label:"咸丰县",value:"422826"},{label:"来凤县",value:"422827"},{label:"鹤峰县",value:"422828"}],[{label:"仙桃市",value:"429004"},{label:"潜江市",value:"429005"},{label:"天门市",value:"429006"},{label:"神农架林区",value:"429021"}]],[[{label:"芙蓉区",value:"430102"},{label:"天心区",value:"430103"},{label:"岳麓区",value:"430104"},{label:"开福区",value:"430105"},{label:"雨花区",value:"430111"},{label:"望城区",value:"430112"},{label:"长沙县",value:"430121"},{label:"浏阳市",value:"430181"},{label:"宁乡市",value:"430182"}],[{label:"荷塘区",value:"430202"},{label:"芦淞区",value:"430203"},{label:"石峰区",value:"430204"},{label:"天元区",value:"430211"},{label:"株洲县",value:"430221"},{label:"攸县",value:"430223"},{label:"茶陵县",value:"430224"},{label:"炎陵县",value:"430225"},{label:"云龙示范区",value:"430271"},{label:"醴陵市",value:"430281"}],[{label:"雨湖区",value:"430302"},{label:"岳塘区",value:"430304"},{label:"湘潭县",value:"430321"},{label:"湖南湘潭高新技术产业园区",value:"430371"},{label:"湘潭昭山示范区",value:"430372"},{label:"湘潭九华示范区",value:"430373"},{label:"湘乡市",value:"430381"},{label:"韶山市",value:"430382"}],[{label:"珠晖区",value:"430405"},{label:"雁峰区",value:"430406"},{label:"石鼓区",value:"430407"},{label:"蒸湘区",value:"430408"},{label:"南岳区",value:"430412"},{label:"衡阳县",value:"430421"},{label:"衡南县",value:"430422"},{label:"衡山县",value:"430423"},{label:"衡东县",value:"430424"},{label:"祁东县",value:"430426"},{label:"衡阳综合保税区",value:"430471"},{label:"湖南衡阳高新技术产业园区",value:"430472"},{label:"湖南衡阳松木经济开发区",value:"430473"},{label:"耒阳市",value:"430481"},{label:"常宁市",value:"430482"}],[{label:"双清区",value:"430502"},{label:"大祥区",value:"430503"},{label:"北塔区",value:"430511"},{label:"邵东县",value:"430521"},{label:"新邵县",value:"430522"},{label:"邵阳县",value:"430523"},{label:"隆回县",value:"430524"},{label:"洞口县",value:"430525"},{label:"绥宁县",value:"430527"},{label:"新宁县",value:"430528"},{label:"城步苗族自治县",value:"430529"},{label:"武冈市",value:"430581"}],[{label:"岳阳楼区",value:"430602"},{label:"云溪区",value:"430603"},{label:"君山区",value:"430611"},{label:"岳阳县",value:"430621"},{label:"华容县",value:"430623"},{label:"湘阴县",value:"430624"},{label:"平江县",value:"430626"},{label:"岳阳市屈原管理区",value:"430671"},{label:"汨罗市",value:"430681"},{label:"临湘市",value:"430682"}],[{label:"武陵区",value:"430702"},{label:"鼎城区",value:"430703"},{label:"安乡县",value:"430721"},{label:"汉寿县",value:"430722"},{label:"澧县",value:"430723"},{label:"临澧县",value:"430724"},{label:"桃源县",value:"430725"},{label:"石门县",value:"430726"},{label:"常德市西洞庭管理区",value:"430771"},{label:"津市市",value:"430781"}],[{label:"永定区",value:"430802"},{label:"武陵源区",value:"430811"},{label:"慈利县",value:"430821"},{label:"桑植县",value:"430822"}],[{label:"资阳区",value:"430902"},{label:"赫山区",value:"430903"},{label:"南县",value:"430921"},{label:"桃江县",value:"430922"},{label:"安化县",value:"430923"},{label:"益阳市大通湖管理区",value:"430971"},{label:"湖南益阳高新技术产业园区",value:"430972"},{label:"沅江市",value:"430981"}],[{label:"北湖区",value:"431002"},{label:"苏仙区",value:"431003"},{label:"桂阳县",value:"431021"},{label:"宜章县",value:"431022"},{label:"永兴县",value:"431023"},{label:"嘉禾县",value:"431024"},{label:"临武县",value:"431025"},{label:"汝城县",value:"431026"},{label:"桂东县",value:"431027"},{label:"安仁县",value:"431028"},{label:"资兴市",value:"431081"}],[{label:"零陵区",value:"431102"},{label:"冷水滩区",value:"431103"},{label:"祁阳县",value:"431121"},{label:"东安县",value:"431122"},{label:"双牌县",value:"431123"},{label:"道县",value:"431124"},{label:"江永县",value:"431125"},{label:"宁远县",value:"431126"},{label:"蓝山县",value:"431127"},{label:"新田县",value:"431128"},{label:"江华瑶族自治县",value:"431129"},{label:"永州经济技术开发区",value:"431171"},{label:"永州市金洞管理区",value:"431172"},{label:"永州市回龙圩管理区",value:"431173"}],[{label:"鹤城区",value:"431202"},{label:"中方县",value:"431221"},{label:"沅陵县",value:"431222"},{label:"辰溪县",value:"431223"},{label:"溆浦县",value:"431224"},{label:"会同县",value:"431225"},{label:"麻阳苗族自治县",value:"431226"},{label:"新晃侗族自治县",value:"431227"},{label:"芷江侗族自治县",value:"431228"},{label:"靖州苗族侗族自治县",value:"431229"},{label:"通道侗族自治县",value:"431230"},{label:"怀化市洪江管理区",value:"431271"},{label:"洪江市",value:"431281"}],[{label:"娄星区",value:"431302"},{label:"双峰县",value:"431321"},{label:"新化县",value:"431322"},{label:"冷水江市",value:"431381"},{label:"涟源市",value:"431382"}],[{label:"吉首市",value:"433101"},{label:"泸溪县",value:"433122"},{label:"凤凰县",value:"433123"},{label:"花垣县",value:"433124"},{label:"保靖县",value:"433125"},{label:"古丈县",value:"433126"},{label:"永顺县",value:"433127"},{label:"龙山县",value:"433130"},{label:"湖南吉首经济开发区",value:"433172"},{label:"湖南永顺经济开发区",value:"433173"}]],[[{label:"荔湾区",value:"440103"},{label:"越秀区",value:"440104"},{label:"海珠区",value:"440105"},{label:"天河区",value:"440106"},{label:"白云区",value:"440111"},{label:"黄埔区",value:"440112"},{label:"番禺区",value:"440113"},{label:"花都区",value:"440114"},{label:"南沙区",value:"440115"},{label:"从化区",value:"440117"},{label:"增城区",value:"440118"}],[{label:"武江区",value:"440203"},{label:"浈江区",value:"440204"},{label:"曲江区",value:"440205"},{label:"始兴县",value:"440222"},{label:"仁化县",value:"440224"},{label:"翁源县",value:"440229"},{label:"乳源瑶族自治县",value:"440232"},{label:"新丰县",value:"440233"},{label:"乐昌市",value:"440281"},{label:"南雄市",value:"440282"}],[{label:"罗湖区",value:"440303"},{label:"福田区",value:"440304"},{label:"南山区",value:"440305"},{label:"宝安区",value:"440306"},{label:"龙岗区",value:"440307"},{label:"盐田区",value:"440308"},{label:"龙华区",value:"440309"},{label:"坪山区",value:"440310"}],[{label:"香洲区",value:"440402"},{label:"斗门区",value:"440403"},{label:"金湾区",value:"440404"}],[{label:"龙湖区",value:"440507"},{label:"金平区",value:"440511"},{label:"濠江区",value:"440512"},{label:"潮阳区",value:"440513"},{label:"潮南区",value:"440514"},{label:"澄海区",value:"440515"},{label:"南澳县",value:"440523"}],[{label:"禅城区",value:"440604"},{label:"南海区",value:"440605"},{label:"顺德区",value:"440606"},{label:"三水区",value:"440607"},{label:"高明区",value:"440608"}],[{label:"蓬江区",value:"440703"},{label:"江海区",value:"440704"},{label:"新会区",value:"440705"},{label:"台山市",value:"440781"},{label:"开平市",value:"440783"},{label:"鹤山市",value:"440784"},{label:"恩平市",value:"440785"}],[{label:"赤坎区",value:"440802"},{label:"霞山区",value:"440803"},{label:"坡头区",value:"440804"},{label:"麻章区",value:"440811"},{label:"遂溪县",value:"440823"},{label:"徐闻县",value:"440825"},{label:"廉江市",value:"440881"},{label:"雷州市",value:"440882"},{label:"吴川市",value:"440883"}],[{label:"茂南区",value:"440902"},{label:"电白区",value:"440904"},{label:"高州市",value:"440981"},{label:"化州市",value:"440982"},{label:"信宜市",value:"440983"}],[{label:"端州区",value:"441202"},{label:"鼎湖区",value:"441203"},{label:"高要区",value:"441204"},{label:"广宁县",value:"441223"},{label:"怀集县",value:"441224"},{label:"封开县",value:"441225"},{label:"德庆县",value:"441226"},{label:"四会市",value:"441284"}],[{label:"惠城区",value:"441302"},{label:"惠阳区",value:"441303"},{label:"博罗县",value:"441322"},{label:"惠东县",value:"441323"},{label:"龙门县",value:"441324"}],[{label:"梅江区",value:"441402"},{label:"梅县区",value:"441403"},{label:"大埔县",value:"441422"},{label:"丰顺县",value:"441423"},{label:"五华县",value:"441424"},{label:"平远县",value:"441426"},{label:"蕉岭县",value:"441427"},{label:"兴宁市",value:"441481"}],[{label:"城区",value:"441502"},{label:"海丰县",value:"441521"},{label:"陆河县",value:"441523"},{label:"陆丰市",value:"441581"}],[{label:"源城区",value:"441602"},{label:"紫金县",value:"441621"},{label:"龙川县",value:"441622"},{label:"连平县",value:"441623"},{label:"和平县",value:"441624"},{label:"东源县",value:"441625"}],[{label:"江城区",value:"441702"},{label:"阳东区",value:"441704"},{label:"阳西县",value:"441721"},{label:"阳春市",value:"441781"}],[{label:"清城区",value:"441802"},{label:"清新区",value:"441803"},{label:"佛冈县",value:"441821"},{label:"阳山县",value:"441823"},{label:"连山壮族瑶族自治县",value:"441825"},{label:"连南瑶族自治县",value:"441826"},{label:"英德市",value:"441881"},{label:"连州市",value:"441882"}],[{label:"东莞市",value:"441900"}],[{label:"中山市",value:"442000"}],[{label:"湘桥区",value:"445102"},{label:"潮安区",value:"445103"},{label:"饶平县",value:"445122"}],[{label:"榕城区",value:"445202"},{label:"揭东区",value:"445203"},{label:"揭西县",value:"445222"},{label:"惠来县",value:"445224"},{label:"普宁市",value:"445281"}],[{label:"云城区",value:"445302"},{label:"云安区",value:"445303"},{label:"新兴县",value:"445321"},{label:"郁南县",value:"445322"},{label:"罗定市",value:"445381"}]],[[{label:"兴宁区",value:"450102"},{label:"青秀区",value:"450103"},{label:"江南区",value:"450105"},{label:"西乡塘区",value:"450107"},{label:"良庆区",value:"450108"},{label:"邕宁区",value:"450109"},{label:"武鸣区",value:"450110"},{label:"隆安县",value:"450123"},{label:"马山县",value:"450124"},{label:"上林县",value:"450125"},{label:"宾阳县",value:"450126"},{label:"横县",value:"450127"}],[{label:"城中区",value:"450202"},{label:"鱼峰区",value:"450203"},{label:"柳南区",value:"450204"},{label:"柳北区",value:"450205"},{label:"柳江区",value:"450206"},{label:"柳城县",value:"450222"},{label:"鹿寨县",value:"450223"},{label:"融安县",value:"450224"},{label:"融水苗族自治县",value:"450225"},{label:"三江侗族自治县",value:"450226"}],[{label:"秀峰区",value:"450302"},{label:"叠彩区",value:"450303"},{label:"象山区",value:"450304"},{label:"七星区",value:"450305"},{label:"雁山区",value:"450311"},{label:"临桂区",value:"450312"},{label:"阳朔县",value:"450321"},{label:"灵川县",value:"450323"},{label:"全州县",value:"450324"},{label:"兴安县",value:"450325"},{label:"永福县",value:"450326"},{label:"灌阳县",value:"450327"},{label:"龙胜各族自治县",value:"450328"},{label:"资源县",value:"450329"},{label:"平乐县",value:"450330"},{label:"荔浦县",value:"450331"},{label:"恭城瑶族自治县",value:"450332"}],[{label:"万秀区",value:"450403"},{label:"长洲区",value:"450405"},{label:"龙圩区",value:"450406"},{label:"苍梧县",value:"450421"},{label:"藤县",value:"450422"},{label:"蒙山县",value:"450423"},{label:"岑溪市",value:"450481"}],[{label:"海城区",value:"450502"},{label:"银海区",value:"450503"},{label:"铁山港区",value:"450512"},{label:"合浦县",value:"450521"}],[{label:"港口区",value:"450602"},{label:"防城区",value:"450603"},{label:"上思县",value:"450621"},{label:"东兴市",value:"450681"}],[{label:"钦南区",value:"450702"},{label:"钦北区",value:"450703"},{label:"灵山县",value:"450721"},{label:"浦北县",value:"450722"}],[{label:"港北区",value:"450802"},{label:"港南区",value:"450803"},{label:"覃塘区",value:"450804"},{label:"平南县",value:"450821"},{label:"桂平市",value:"450881"}],[{label:"玉州区",value:"450902"},{label:"福绵区",value:"450903"},{label:"容县",value:"450921"},{label:"陆川县",value:"450922"},{label:"博白县",value:"450923"},{label:"兴业县",value:"450924"},{label:"北流市",value:"450981"}],[{label:"右江区",value:"451002"},{label:"田阳县",value:"451021"},{label:"田东县",value:"451022"},{label:"平果县",value:"451023"},{label:"德保县",value:"451024"},{label:"那坡县",value:"451026"},{label:"凌云县",value:"451027"},{label:"乐业县",value:"451028"},{label:"田林县",value:"451029"},{label:"西林县",value:"451030"},{label:"隆林各族自治县",value:"451031"},{label:"靖西市",value:"451081"}],[{label:"八步区",value:"451102"},{label:"平桂区",value:"451103"},{label:"昭平县",value:"451121"},{label:"钟山县",value:"451122"},{label:"富川瑶族自治县",value:"451123"}],[{label:"金城江区",value:"451202"},{label:"宜州区",value:"451203"},{label:"南丹县",value:"451221"},{label:"天峨县",value:"451222"},{label:"凤山县",value:"451223"},{label:"东兰县",value:"451224"},{label:"罗城仫佬族自治县",value:"451225"},{label:"环江毛南族自治县",value:"451226"},{label:"巴马瑶族自治县",value:"451227"},{label:"都安瑶族自治县",value:"451228"},{label:"大化瑶族自治县",value:"451229"}],[{label:"兴宾区",value:"451302"},{label:"忻城县",value:"451321"},{label:"象州县",value:"451322"},{label:"武宣县",value:"451323"},{label:"金秀瑶族自治县",value:"451324"},{label:"合山市",value:"451381"}],[{label:"江州区",value:"451402"},{label:"扶绥县",value:"451421"},{label:"宁明县",value:"451422"},{label:"龙州县",value:"451423"},{label:"大新县",value:"451424"},{label:"天等县",value:"451425"},{label:"凭祥市",value:"451481"}]],[[{label:"秀英区",value:"460105"},{label:"龙华区",value:"460106"},{label:"琼山区",value:"460107"},{label:"美兰区",value:"460108"}],[{label:"海棠区",value:"460202"},{label:"吉阳区",value:"460203"},{label:"天涯区",value:"460204"},{label:"崖州区",value:"460205"}],[{label:"西沙群岛",value:"460321"},{label:"南沙群岛",value:"460322"},{label:"中沙群岛的岛礁及其海域",value:"460323"}],[{label:"儋州市",value:"460400"}],[{label:"五指山市",value:"469001"},{label:"琼海市",value:"469002"},{label:"文昌市",value:"469005"},{label:"万宁市",value:"469006"},{label:"东方市",value:"469007"},{label:"定安县",value:"469021"},{label:"屯昌县",value:"469022"},{label:"澄迈县",value:"469023"},{label:"临高县",value:"469024"},{label:"白沙黎族自治县",value:"469025"},{label:"昌江黎族自治县",value:"469026"},{label:"乐东黎族自治县",value:"469027"},{label:"陵水黎族自治县",value:"469028"},{label:"保亭黎族苗族自治县",value:"469029"},{label:"琼中黎族苗族自治县",value:"469030"}]],[[{label:"万州区",value:"500101"},{label:"涪陵区",value:"500102"},{label:"渝中区",value:"500103"},{label:"大渡口区",value:"500104"},{label:"江北区",value:"500105"},{label:"沙坪坝区",value:"500106"},{label:"九龙坡区",value:"500107"},{label:"南岸区",value:"500108"},{label:"北碚区",value:"500109"},{label:"綦江区",value:"500110"},{label:"大足区",value:"500111"},{label:"渝北区",value:"500112"},{label:"巴南区",value:"500113"},{label:"黔江区",value:"500114"},{label:"长寿区",value:"500115"},{label:"江津区",value:"500116"},{label:"合川区",value:"500117"},{label:"永川区",value:"500118"},{label:"南川区",value:"500119"},{label:"璧山区",value:"500120"},{label:"铜梁区",value:"500151"},{label:"潼南区",value:"500152"},{label:"荣昌区",value:"500153"},{label:"开州区",value:"500154"},{label:"梁平区",value:"500155"},{label:"武隆区",value:"500156"}],[{label:"城口县",value:"500229"},{label:"丰都县",value:"500230"},{label:"垫江县",value:"500231"},{label:"忠县",value:"500233"},{label:"云阳县",value:"500235"},{label:"奉节县",value:"500236"},{label:"巫山县",value:"500237"},{label:"巫溪县",value:"500238"},{label:"石柱土家族自治县",value:"500240"},{label:"秀山土家族苗族自治县",value:"500241"},{label:"酉阳土家族苗族自治县",value:"500242"},{label:"彭水苗族土家族自治县",value:"500243"}]],[[{label:"锦江区",value:"510104"},{label:"青羊区",value:"510105"},{label:"金牛区",value:"510106"},{label:"武侯区",value:"510107"},{label:"成华区",value:"510108"},{label:"龙泉驿区",value:"510112"},{label:"青白江区",value:"510113"},{label:"新都区",value:"510114"},{label:"温江区",value:"510115"},{label:"双流区",value:"510116"},{label:"郫都区",value:"510117"},{label:"金堂县",value:"510121"},{label:"大邑县",value:"510129"},{label:"蒲江县",value:"510131"},{label:"新津县",value:"510132"},{label:"都江堰市",value:"510181"},{label:"彭州市",value:"510182"},{label:"邛崃市",value:"510183"},{label:"崇州市",value:"510184"},{label:"简阳市",value:"510185"}],[{label:"自流井区",value:"510302"},{label:"贡井区",value:"510303"},{label:"大安区",value:"510304"},{label:"沿滩区",value:"510311"},{label:"荣县",value:"510321"},{label:"富顺县",value:"510322"}],[{label:"东区",value:"510402"},{label:"西区",value:"510403"},{label:"仁和区",value:"510411"},{label:"米易县",value:"510421"},{label:"盐边县",value:"510422"}],[{label:"江阳区",value:"510502"},{label:"纳溪区",value:"510503"},{label:"龙马潭区",value:"510504"},{label:"泸县",value:"510521"},{label:"合江县",value:"510522"},{label:"叙永县",value:"510524"},{label:"古蔺县",value:"510525"}],[{label:"旌阳区",value:"510603"},{label:"罗江区",value:"510604"},{label:"中江县",value:"510623"},{label:"广汉市",value:"510681"},{label:"什邡市",value:"510682"},{label:"绵竹市",value:"510683"}],[{label:"涪城区",value:"510703"},{label:"游仙区",value:"510704"},{label:"安州区",value:"510705"},{label:"三台县",value:"510722"},{label:"盐亭县",value:"510723"},{label:"梓潼县",value:"510725"},{label:"北川羌族自治县",value:"510726"},{label:"平武县",value:"510727"},{label:"江油市",value:"510781"}],[{label:"利州区",value:"510802"},{label:"昭化区",value:"510811"},{label:"朝天区",value:"510812"},{label:"旺苍县",value:"510821"},{label:"青川县",value:"510822"},{label:"剑阁县",value:"510823"},{label:"苍溪县",value:"510824"}],[{label:"船山区",value:"510903"},{label:"安居区",value:"510904"},{label:"蓬溪县",value:"510921"},{label:"射洪县",value:"510922"},{label:"大英县",value:"510923"}],[{label:"市中区",value:"511002"},{label:"东兴区",value:"511011"},{label:"威远县",value:"511024"},{label:"资中县",value:"511025"},{label:"内江经济开发区",value:"511071"},{label:"隆昌市",value:"511083"}],[{label:"市中区",value:"511102"},{label:"沙湾区",value:"511111"},{label:"五通桥区",value:"511112"},{label:"金口河区",value:"511113"},{label:"犍为县",value:"511123"},{label:"井研县",value:"511124"},{label:"夹江县",value:"511126"},{label:"沐川县",value:"511129"},{label:"峨边彝族自治县",value:"511132"},{label:"马边彝族自治县",value:"511133"},{label:"峨眉山市",value:"511181"}],[{label:"顺庆区",value:"511302"},{label:"高坪区",value:"511303"},{label:"嘉陵区",value:"511304"},{label:"南部县",value:"511321"},{label:"营山县",value:"511322"},{label:"蓬安县",value:"511323"},{label:"仪陇县",value:"511324"},{label:"西充县",value:"511325"},{label:"阆中市",value:"511381"}],[{label:"东坡区",value:"511402"},{label:"彭山区",value:"511403"},{label:"仁寿县",value:"511421"},{label:"洪雅县",value:"511423"},{label:"丹棱县",value:"511424"},{label:"青神县",value:"511425"}],[{label:"翠屏区",value:"511502"},{label:"南溪区",value:"511503"},{label:"宜宾县",value:"511521"},{label:"江安县",value:"511523"},{label:"长宁县",value:"511524"},{label:"高县",value:"511525"},{label:"珙县",value:"511526"},{label:"筠连县",value:"511527"},{label:"兴文县",value:"511528"},{label:"屏山县",value:"511529"}],[{label:"广安区",value:"511602"},{label:"前锋区",value:"511603"},{label:"岳池县",value:"511621"},{label:"武胜县",value:"511622"},{label:"邻水县",value:"511623"},{label:"华蓥市",value:"511681"}],[{label:"通川区",value:"511702"},{label:"达川区",value:"511703"},{label:"宣汉县",value:"511722"},{label:"开江县",value:"511723"},{label:"大竹县",value:"511724"},{label:"渠县",value:"511725"},{label:"达州经济开发区",value:"511771"},{label:"万源市",value:"511781"}],[{label:"雨城区",value:"511802"},{label:"名山区",value:"511803"},{label:"荥经县",value:"511822"},{label:"汉源县",value:"511823"},{label:"石棉县",value:"511824"},{label:"天全县",value:"511825"},{label:"芦山县",value:"511826"},{label:"宝兴县",value:"511827"}],[{label:"巴州区",value:"511902"},{label:"恩阳区",value:"511903"},{label:"通江县",value:"511921"},{label:"南江县",value:"511922"},{label:"平昌县",value:"511923"},{label:"巴中经济开发区",value:"511971"}],[{label:"雁江区",value:"512002"},{label:"安岳县",value:"512021"},{label:"乐至县",value:"512022"}],[{label:"马尔康市",value:"513201"},{label:"汶川县",value:"513221"},{label:"理县",value:"513222"},{label:"茂县",value:"513223"},{label:"松潘县",value:"513224"},{label:"九寨沟县",value:"513225"},{label:"金川县",value:"513226"},{label:"小金县",value:"513227"},{label:"黑水县",value:"513228"},{label:"壤塘县",value:"513230"},{label:"阿坝县",value:"513231"},{label:"若尔盖县",value:"513232"},{label:"红原县",value:"513233"}],[{label:"康定市",value:"513301"},{label:"泸定县",value:"513322"},{label:"丹巴县",value:"513323"},{label:"九龙县",value:"513324"},{label:"雅江县",value:"513325"},{label:"道孚县",value:"513326"},{label:"炉霍县",value:"513327"},{label:"甘孜县",value:"513328"},{label:"新龙县",value:"513329"},{label:"德格县",value:"513330"},{label:"白玉县",value:"513331"},{label:"石渠县",value:"513332"},{label:"色达县",value:"513333"},{label:"理塘县",value:"513334"},{label:"巴塘县",value:"513335"},{label:"乡城县",value:"513336"},{label:"稻城县",value:"513337"},{label:"得荣县",value:"513338"}],[{label:"西昌市",value:"513401"},{label:"木里藏族自治县",value:"513422"},{label:"盐源县",value:"513423"},{label:"德昌县",value:"513424"},{label:"会理县",value:"513425"},{label:"会东县",value:"513426"},{label:"宁南县",value:"513427"},{label:"普格县",value:"513428"},{label:"布拖县",value:"513429"},{label:"金阳县",value:"513430"},{label:"昭觉县",value:"513431"},{label:"喜德县",value:"513432"},{label:"冕宁县",value:"513433"},{label:"越西县",value:"513434"},{label:"甘洛县",value:"513435"},{label:"美姑县",value:"513436"},{label:"雷波县",value:"513437"}]],[[{label:"南明区",value:"520102"},{label:"云岩区",value:"520103"},{label:"花溪区",value:"520111"},{label:"乌当区",value:"520112"},{label:"白云区",value:"520113"},{label:"观山湖区",value:"520115"},{label:"开阳县",value:"520121"},{label:"息烽县",value:"520122"},{label:"修文县",value:"520123"},{label:"清镇市",value:"520181"}],[{label:"钟山区",value:"520201"},{label:"六枝特区",value:"520203"},{label:"水城县",value:"520221"},{label:"盘州市",value:"520281"}],[{label:"红花岗区",value:"520302"},{label:"汇川区",value:"520303"},{label:"播州区",value:"520304"},{label:"桐梓县",value:"520322"},{label:"绥阳县",value:"520323"},{label:"正安县",value:"520324"},{label:"道真仡佬族苗族自治县",value:"520325"},{label:"务川仡佬族苗族自治县",value:"520326"},{label:"凤冈县",value:"520327"},{label:"湄潭县",value:"520328"},{label:"余庆县",value:"520329"},{label:"习水县",value:"520330"},{label:"赤水市",value:"520381"},{label:"仁怀市",value:"520382"}],[{label:"西秀区",value:"520402"},{label:"平坝区",value:"520403"},{label:"普定县",value:"520422"},{label:"镇宁布依族苗族自治县",value:"520423"},{label:"关岭布依族苗族自治县",value:"520424"},{label:"紫云苗族布依族自治县",value:"520425"}],[{label:"七星关区",value:"520502"},{label:"大方县",value:"520521"},{label:"黔西县",value:"520522"},{label:"金沙县",value:"520523"},{label:"织金县",value:"520524"},{label:"纳雍县",value:"520525"},{label:"威宁彝族回族苗族自治县",value:"520526"},{label:"赫章县",value:"520527"}],[{label:"碧江区",value:"520602"},{label:"万山区",value:"520603"},{label:"江口县",value:"520621"},{label:"玉屏侗族自治县",value:"520622"},{label:"石阡县",value:"520623"},{label:"思南县",value:"520624"},{label:"印江土家族苗族自治县",value:"520625"},{label:"德江县",value:"520626"},{label:"沿河土家族自治县",value:"520627"},{label:"松桃苗族自治县",value:"520628"}],[{label:"兴义市",value:"522301"},{label:"兴仁县",value:"522322"},{label:"普安县",value:"522323"},{label:"晴隆县",value:"522324"},{label:"贞丰县",value:"522325"},{label:"望谟县",value:"522326"},{label:"册亨县",value:"522327"},{label:"安龙县",value:"522328"}],[{label:"凯里市",value:"522601"},{label:"黄平县",value:"522622"},{label:"施秉县",value:"522623"},{label:"三穗县",value:"522624"},{label:"镇远县",value:"522625"},{label:"岑巩县",value:"522626"},{label:"天柱县",value:"522627"},{label:"锦屏县",value:"522628"},{label:"剑河县",value:"522629"},{label:"台江县",value:"522630"},{label:"黎平县",value:"522631"},{label:"榕江县",value:"522632"},{label:"从江县",value:"522633"},{label:"雷山县",value:"522634"},{label:"麻江县",value:"522635"},{label:"丹寨县",value:"522636"}],[{label:"都匀市",value:"522701"},{label:"福泉市",value:"522702"},{label:"荔波县",value:"522722"},{label:"贵定县",value:"522723"},{label:"瓮安县",value:"522725"},{label:"独山县",value:"522726"},{label:"平塘县",value:"522727"},{label:"罗甸县",value:"522728"},{label:"长顺县",value:"522729"},{label:"龙里县",value:"522730"},{label:"惠水县",value:"522731"},{label:"三都水族自治县",value:"522732"}]],[[{label:"五华区",value:"530102"},{label:"盘龙区",value:"530103"},{label:"官渡区",value:"530111"},{label:"西山区",value:"530112"},{label:"东川区",value:"530113"},{label:"呈贡区",value:"530114"},{label:"晋宁区",value:"530115"},{label:"富民县",value:"530124"},{label:"宜良县",value:"530125"},{label:"石林彝族自治县",value:"530126"},{label:"嵩明县",value:"530127"},{label:"禄劝彝族苗族自治县",value:"530128"},{label:"寻甸回族彝族自治县",value:"530129"},{label:"安宁市",value:"530181"}],[{label:"麒麟区",value:"530302"},{label:"沾益区",value:"530303"},{label:"马龙县",value:"530321"},{label:"陆良县",value:"530322"},{label:"师宗县",value:"530323"},{label:"罗平县",value:"530324"},{label:"富源县",value:"530325"},{label:"会泽县",value:"530326"},{label:"宣威市",value:"530381"}],[{label:"红塔区",value:"530402"},{label:"江川区",value:"530403"},{label:"澄江县",value:"530422"},{label:"通海县",value:"530423"},{label:"华宁县",value:"530424"},{label:"易门县",value:"530425"},{label:"峨山彝族自治县",value:"530426"},{label:"新平彝族傣族自治县",value:"530427"},{label:"元江哈尼族彝族傣族自治县",value:"530428"}],[{label:"隆阳区",value:"530502"},{label:"施甸县",value:"530521"},{label:"龙陵县",value:"530523"},{label:"昌宁县",value:"530524"},{label:"腾冲市",value:"530581"}],[{label:"昭阳区",value:"530602"},{label:"鲁甸县",value:"530621"},{label:"巧家县",value:"530622"},{label:"盐津县",value:"530623"},{label:"大关县",value:"530624"},{label:"永善县",value:"530625"},{label:"绥江县",value:"530626"},{label:"镇雄县",value:"530627"},{label:"彝良县",value:"530628"},{label:"威信县",value:"530629"},{label:"水富县",value:"530630"}],[{label:"古城区",value:"530702"},{label:"玉龙纳西族自治县",value:"530721"},{label:"永胜县",value:"530722"},{label:"华坪县",value:"530723"},{label:"宁蒗彝族自治县",value:"530724"}],[{label:"思茅区",value:"530802"},{label:"宁洱哈尼族彝族自治县",value:"530821"},{label:"墨江哈尼族自治县",value:"530822"},{label:"景东彝族自治县",value:"530823"},{label:"景谷傣族彝族自治县",value:"530824"},{label:"镇沅彝族哈尼族拉祜族自治县",value:"530825"},{label:"江城哈尼族彝族自治县",value:"530826"},{label:"孟连傣族拉祜族佤族自治县",value:"530827"},{label:"澜沧拉祜族自治县",value:"530828"},{label:"西盟佤族自治县",value:"530829"}],[{label:"临翔区",value:"530902"},{label:"凤庆县",value:"530921"},{label:"云县",value:"530922"},{label:"永德县",value:"530923"},{label:"镇康县",value:"530924"},{label:"双江拉祜族佤族布朗族傣族自治县",value:"530925"},{label:"耿马傣族佤族自治县",value:"530926"},{label:"沧源佤族自治县",value:"530927"}],[{label:"楚雄市",value:"532301"},{label:"双柏县",value:"532322"},{label:"牟定县",value:"532323"},{label:"南华县",value:"532324"},{label:"姚安县",value:"532325"},{label:"大姚县",value:"532326"},{label:"永仁县",value:"532327"},{label:"元谋县",value:"532328"},{label:"武定县",value:"532329"},{label:"禄丰县",value:"532331"}],[{label:"个旧市",value:"532501"},{label:"开远市",value:"532502"},{label:"蒙自市",value:"532503"},{label:"弥勒市",value:"532504"},{label:"屏边苗族自治县",value:"532523"},{label:"建水县",value:"532524"},{label:"石屏县",value:"532525"},{label:"泸西县",value:"532527"},{label:"元阳县",value:"532528"},{label:"红河县",value:"532529"},{label:"金平苗族瑶族傣族自治县",value:"532530"},{label:"绿春县",value:"532531"},{label:"河口瑶族自治县",value:"532532"}],[{label:"文山市",value:"532601"},{label:"砚山县",value:"532622"},{label:"西畴县",value:"532623"},{label:"麻栗坡县",value:"532624"},{label:"马关县",value:"532625"},{label:"丘北县",value:"532626"},{label:"广南县",value:"532627"},{label:"富宁县",value:"532628"}],[{label:"景洪市",value:"532801"},{label:"勐海县",value:"532822"},{label:"勐腊县",value:"532823"}],[{label:"大理市",value:"532901"},{label:"漾濞彝族自治县",value:"532922"},{label:"祥云县",value:"532923"},{label:"宾川县",value:"532924"},{label:"弥渡县",value:"532925"},{label:"南涧彝族自治县",value:"532926"},{label:"巍山彝族回族自治县",value:"532927"},{label:"永平县",value:"532928"},{label:"云龙县",value:"532929"},{label:"洱源县",value:"532930"},{label:"剑川县",value:"532931"},{label:"鹤庆县",value:"532932"}],[{label:"瑞丽市",value:"533102"},{label:"芒市",value:"533103"},{label:"梁河县",value:"533122"},{label:"盈江县",value:"533123"},{label:"陇川县",value:"533124"}],[{label:"泸水市",value:"533301"},{label:"福贡县",value:"533323"},{label:"贡山独龙族怒族自治县",value:"533324"},{label:"兰坪白族普米族自治县",value:"533325"}],[{label:"香格里拉市",value:"533401"},{label:"德钦县",value:"533422"},{label:"维西傈僳族自治县",value:"533423"}]],[[{label:"城关区",value:"540102"},{label:"堆龙德庆区",value:"540103"},{label:"林周县",value:"540121"},{label:"当雄县",value:"540122"},{label:"尼木县",value:"540123"},{label:"曲水县",value:"540124"},{label:"达孜县",value:"540126"},{label:"墨竹工卡县",value:"540127"},{label:"格尔木藏青工业园区",value:"540171"},{label:"拉萨经济技术开发区",value:"540172"},{label:"西藏文化旅游创意园区",value:"540173"},{label:"达孜工业园区",value:"540174"}],[{label:"桑珠孜区",value:"540202"},{label:"南木林县",value:"540221"},{label:"江孜县",value:"540222"},{label:"定日县",value:"540223"},{label:"萨迦县",value:"540224"},{label:"拉孜县",value:"540225"},{label:"昂仁县",value:"540226"},{label:"谢通门县",value:"540227"},{label:"白朗县",value:"540228"},{label:"仁布县",value:"540229"},{label:"康马县",value:"540230"},{label:"定结县",value:"540231"},{label:"仲巴县",value:"540232"},{label:"亚东县",value:"540233"},{label:"吉隆县",value:"540234"},{label:"聂拉木县",value:"540235"},{label:"萨嘎县",value:"540236"},{label:"岗巴县",value:"540237"}],[{label:"卡若区",value:"540302"},{label:"江达县",value:"540321"},{label:"贡觉县",value:"540322"},{label:"类乌齐县",value:"540323"},{label:"丁青县",value:"540324"},{label:"察雅县",value:"540325"},{label:"八宿县",value:"540326"},{label:"左贡县",value:"540327"},{label:"芒康县",value:"540328"},{label:"洛隆县",value:"540329"},{label:"边坝县",value:"540330"}],[{label:"巴宜区",value:"540402"},{label:"工布江达县",value:"540421"},{label:"米林县",value:"540422"},{label:"墨脱县",value:"540423"},{label:"波密县",value:"540424"},{label:"察隅县",value:"540425"},{label:"朗县",value:"540426"}],[{label:"乃东区",value:"540502"},{label:"扎囊县",value:"540521"},{label:"贡嘎县",value:"540522"},{label:"桑日县",value:"540523"},{label:"琼结县",value:"540524"},{label:"曲松县",value:"540525"},{label:"措美县",value:"540526"},{label:"洛扎县",value:"540527"},{label:"加查县",value:"540528"},{label:"隆子县",value:"540529"},{label:"错那县",value:"540530"},{label:"浪卡子县",value:"540531"}],[{label:"那曲县",value:"542421"},{label:"嘉黎县",value:"542422"},{label:"比如县",value:"542423"},{label:"聂荣县",value:"542424"},{label:"安多县",value:"542425"},{label:"申扎县",value:"542426"},{label:"索县",value:"542427"},{label:"班戈县",value:"542428"},{label:"巴青县",value:"542429"},{label:"尼玛县",value:"542430"},{label:"双湖县",value:"542431"}],[{label:"普兰县",value:"542521"},{label:"札达县",value:"542522"},{label:"噶尔县",value:"542523"},{label:"日土县",value:"542524"},{label:"革吉县",value:"542525"},{label:"改则县",value:"542526"},{label:"措勤县",value:"542527"}]],[[{label:"新城区",value:"610102"},{label:"碑林区",value:"610103"},{label:"莲湖区",value:"610104"},{label:"灞桥区",value:"610111"},{label:"未央区",value:"610112"},{label:"雁塔区",value:"610113"},{label:"阎良区",value:"610114"},{label:"临潼区",value:"610115"},{label:"长安区",value:"610116"},{label:"高陵区",value:"610117"},{label:"鄠邑区",value:"610118"},{label:"蓝田县",value:"610122"},{label:"周至县",value:"610124"}],[{label:"王益区",value:"610202"},{label:"印台区",value:"610203"},{label:"耀州区",value:"610204"},{label:"宜君县",value:"610222"}],[{label:"渭滨区",value:"610302"},{label:"金台区",value:"610303"},{label:"陈仓区",value:"610304"},{label:"凤翔县",value:"610322"},{label:"岐山县",value:"610323"},{label:"扶风县",value:"610324"},{label:"眉县",value:"610326"},{label:"陇县",value:"610327"},{label:"千阳县",value:"610328"},{label:"麟游县",value:"610329"},{label:"凤县",value:"610330"},{label:"太白县",value:"610331"}],[{label:"秦都区",value:"610402"},{label:"杨陵区",value:"610403"},{label:"渭城区",value:"610404"},{label:"三原县",value:"610422"},{label:"泾阳县",value:"610423"},{label:"乾县",value:"610424"},{label:"礼泉县",value:"610425"},{label:"永寿县",value:"610426"},{label:"彬县",value:"610427"},{label:"长武县",value:"610428"},{label:"旬邑县",value:"610429"},{label:"淳化县",value:"610430"},{label:"武功县",value:"610431"},{label:"兴平市",value:"610481"}],[{label:"临渭区",value:"610502"},{label:"华州区",value:"610503"},{label:"潼关县",value:"610522"},{label:"大荔县",value:"610523"},{label:"合阳县",value:"610524"},{label:"澄城县",value:"610525"},{label:"蒲城县",value:"610526"},{label:"白水县",value:"610527"},{label:"富平县",value:"610528"},{label:"韩城市",value:"610581"},{label:"华阴市",value:"610582"}],[{label:"宝塔区",value:"610602"},{label:"安塞区",value:"610603"},{label:"延长县",value:"610621"},{label:"延川县",value:"610622"},{label:"子长县",value:"610623"},{label:"志丹县",value:"610625"},{label:"吴起县",value:"610626"},{label:"甘泉县",value:"610627"},{label:"富县",value:"610628"},{label:"洛川县",value:"610629"},{label:"宜川县",value:"610630"},{label:"黄龙县",value:"610631"},{label:"黄陵县",value:"610632"}],[{label:"汉台区",value:"610702"},{label:"南郑区",value:"610703"},{label:"城固县",value:"610722"},{label:"洋县",value:"610723"},{label:"西乡县",value:"610724"},{label:"勉县",value:"610725"},{label:"宁强县",value:"610726"},{label:"略阳县",value:"610727"},{label:"镇巴县",value:"610728"},{label:"留坝县",value:"610729"},{label:"佛坪县",value:"610730"}],[{label:"榆阳区",value:"610802"},{label:"横山区",value:"610803"},{label:"府谷县",value:"610822"},{label:"靖边县",value:"610824"},{label:"定边县",value:"610825"},{label:"绥德县",value:"610826"},{label:"米脂县",value:"610827"},{label:"佳县",value:"610828"},{label:"吴堡县",value:"610829"},{label:"清涧县",value:"610830"},{label:"子洲县",value:"610831"},{label:"神木市",value:"610881"}],[{label:"汉滨区",value:"610902"},{label:"汉阴县",value:"610921"},{label:"石泉县",value:"610922"},{label:"宁陕县",value:"610923"},{label:"紫阳县",value:"610924"},{label:"岚皋县",value:"610925"},{label:"平利县",value:"610926"},{label:"镇坪县",value:"610927"},{label:"旬阳县",value:"610928"},{label:"白河县",value:"610929"}],[{label:"商州区",value:"611002"},{label:"洛南县",value:"611021"},{label:"丹凤县",value:"611022"},{label:"商南县",value:"611023"},{label:"山阳县",value:"611024"},{label:"镇安县",value:"611025"},{label:"柞水县",value:"611026"}]],[[{label:"城关区",value:"620102"},{label:"七里河区",value:"620103"},{label:"西固区",value:"620104"},{label:"安宁区",value:"620105"},{label:"红古区",value:"620111"},{label:"永登县",value:"620121"},{label:"皋兰县",value:"620122"},{label:"榆中县",value:"620123"},{label:"兰州新区",value:"620171"}],[{label:"嘉峪关市",value:"620201"}],[{label:"金川区",value:"620302"},{label:"永昌县",value:"620321"}],[{label:"白银区",value:"620402"},{label:"平川区",value:"620403"},{label:"靖远县",value:"620421"},{label:"会宁县",value:"620422"},{label:"景泰县",value:"620423"}],[{label:"秦州区",value:"620502"},{label:"麦积区",value:"620503"},{label:"清水县",value:"620521"},{label:"秦安县",value:"620522"},{label:"甘谷县",value:"620523"},{label:"武山县",value:"620524"},{label:"张家川回族自治县",value:"620525"}],[{label:"凉州区",value:"620602"},{label:"民勤县",value:"620621"},{label:"古浪县",value:"620622"},{label:"天祝藏族自治县",value:"620623"}],[{label:"甘州区",value:"620702"},{label:"肃南裕固族自治县",value:"620721"},{label:"民乐县",value:"620722"},{label:"临泽县",value:"620723"},{label:"高台县",value:"620724"},{label:"山丹县",value:"620725"}],[{label:"崆峒区",value:"620802"},{label:"泾川县",value:"620821"},{label:"灵台县",value:"620822"},{label:"崇信县",value:"620823"},{label:"华亭县",value:"620824"},{label:"庄浪县",value:"620825"},{label:"静宁县",value:"620826"},{label:"平凉工业园区",value:"620871"}],[{label:"肃州区",value:"620902"},{label:"金塔县",value:"620921"},{label:"瓜州县",value:"620922"},{label:"肃北蒙古族自治县",value:"620923"},{label:"阿克塞哈萨克族自治县",value:"620924"},{label:"玉门市",value:"620981"},{label:"敦煌市",value:"620982"}],[{label:"西峰区",value:"621002"},{label:"庆城县",value:"621021"},{label:"环县",value:"621022"},{label:"华池县",value:"621023"},{label:"合水县",value:"621024"},{label:"正宁县",value:"621025"},{label:"宁县",value:"621026"},{label:"镇原县",value:"621027"}],[{label:"安定区",value:"621102"},{label:"通渭县",value:"621121"},{label:"陇西县",value:"621122"},{label:"渭源县",value:"621123"},{label:"临洮县",value:"621124"},{label:"漳县",value:"621125"},{label:"岷县",value:"621126"}],[{label:"武都区",value:"621202"},{label:"成县",value:"621221"},{label:"文县",value:"621222"},{label:"宕昌县",value:"621223"},{label:"康县",value:"621224"},{label:"西和县",value:"621225"},{label:"礼县",value:"621226"},{label:"徽县",value:"621227"},{label:"两当县",value:"621228"}],[{label:"临夏市",value:"622901"},{label:"临夏县",value:"622921"},{label:"康乐县",value:"622922"},{label:"永靖县",value:"622923"},{label:"广河县",value:"622924"},{label:"和政县",value:"622925"},{label:"东乡族自治县",value:"622926"},{label:"积石山保安族东乡族撒拉族自治县",value:"622927"}],[{label:"合作市",value:"623001"},{label:"临潭县",value:"623021"},{label:"卓尼县",value:"623022"},{label:"舟曲县",value:"623023"},{label:"迭部县",value:"623024"},{label:"玛曲县",value:"623025"},{label:"碌曲县",value:"623026"},{label:"夏河县",value:"623027"}]],[[{label:"城东区",value:"630102"},{label:"城中区",value:"630103"},{label:"城西区",value:"630104"},{label:"城北区",value:"630105"},{label:"大通回族土族自治县",value:"630121"},{label:"湟中县",value:"630122"},{label:"湟源县",value:"630123"}],[{label:"乐都区",value:"630202"},{label:"平安区",value:"630203"},{label:"民和回族土族自治县",value:"630222"},{label:"互助土族自治县",value:"630223"},{label:"化隆回族自治县",value:"630224"},{label:"循化撒拉族自治县",value:"630225"}],[{label:"门源回族自治县",value:"632221"},{label:"祁连县",value:"632222"},{label:"海晏县",value:"632223"},{label:"刚察县",value:"632224"}],[{label:"同仁县",value:"632321"},{label:"尖扎县",value:"632322"},{label:"泽库县",value:"632323"},{label:"河南蒙古族自治县",value:"632324"}],[{label:"共和县",value:"632521"},{label:"同德县",value:"632522"},{label:"贵德县",value:"632523"},{label:"兴海县",value:"632524"},{label:"贵南县",value:"632525"}],[{label:"玛沁县",value:"632621"},{label:"班玛县",value:"632622"},{label:"甘德县",value:"632623"},{label:"达日县",value:"632624"},{label:"久治县",value:"632625"},{label:"玛多县",value:"632626"}],[{label:"玉树市",value:"632701"},{label:"杂多县",value:"632722"},{label:"称多县",value:"632723"},{label:"治多县",value:"632724"},{label:"囊谦县",value:"632725"},{label:"曲麻莱县",value:"632726"}],[{label:"格尔木市",value:"632801"},{label:"德令哈市",value:"632802"},{label:"乌兰县",value:"632821"},{label:"都兰县",value:"632822"},{label:"天峻县",value:"632823"},{label:"大柴旦行政委员会",value:"632857"},{label:"冷湖行政委员会",value:"632858"},{label:"茫崖行政委员会",value:"632859"}]],[[{label:"兴庆区",value:"640104"},{label:"西夏区",value:"640105"},{label:"金凤区",value:"640106"},{label:"永宁县",value:"640121"},{label:"贺兰县",value:"640122"},{label:"灵武市",value:"640181"}],[{label:"大武口区",value:"640202"},{label:"惠农区",value:"640205"},{label:"平罗县",value:"640221"}],[{label:"利通区",value:"640302"},{label:"红寺堡区",value:"640303"},{label:"盐池县",value:"640323"},{label:"同心县",value:"640324"},{label:"青铜峡市",value:"640381"}],[{label:"原州区",value:"640402"},{label:"西吉县",value:"640422"},{label:"隆德县",value:"640423"},{label:"泾源县",value:"640424"},{label:"彭阳县",value:"640425"}],[{label:"沙坡头区",value:"640502"},{label:"中宁县",value:"640521"},{label:"海原县",value:"640522"}]],[[{label:"天山区",value:"650102"},{label:"沙依巴克区",value:"650103"},{label:"新市区",value:"650104"},{label:"水磨沟区",value:"650105"},{label:"头屯河区",value:"650106"},{label:"达坂城区",value:"650107"},{label:"米东区",value:"650109"},{label:"乌鲁木齐县",value:"650121"},{label:"乌鲁木齐经济技术开发区",value:"650171"},{label:"乌鲁木齐高新技术产业开发区",value:"650172"}],[{label:"独山子区",value:"650202"},{label:"克拉玛依区",value:"650203"},{label:"白碱滩区",value:"650204"},{label:"乌尔禾区",value:"650205"}],[{label:"高昌区",value:"650402"},{label:"鄯善县",value:"650421"},{label:"托克逊县",value:"650422"}],[{label:"伊州区",value:"650502"},{label:"巴里坤哈萨克自治县",value:"650521"},{label:"伊吾县",value:"650522"}],[{label:"昌吉市",value:"652301"},{label:"阜康市",value:"652302"},{label:"呼图壁县",value:"652323"},{label:"玛纳斯县",value:"652324"},{label:"奇台县",value:"652325"},{label:"吉木萨尔县",value:"652327"},{label:"木垒哈萨克自治县",value:"652328"}],[{label:"博乐市",value:"652701"},{label:"阿拉山口市",value:"652702"},{label:"精河县",value:"652722"},{label:"温泉县",value:"652723"}],[{label:"库尔勒市",value:"652801"},{label:"轮台县",value:"652822"},{label:"尉犁县",value:"652823"},{label:"若羌县",value:"652824"},{label:"且末县",value:"652825"},{label:"焉耆回族自治县",value:"652826"},{label:"和静县",value:"652827"},{label:"和硕县",value:"652828"},{label:"博湖县",value:"652829"},{label:"库尔勒经济技术开发区",value:"652871"}],[{label:"阿克苏市",value:"652901"},{label:"温宿县",value:"652922"},{label:"库车县",value:"652923"},{label:"沙雅县",value:"652924"},{label:"新和县",value:"652925"},{label:"拜城县",value:"652926"},{label:"乌什县",value:"652927"},{label:"阿瓦提县",value:"652928"},{label:"柯坪县",value:"652929"}],[{label:"阿图什市",value:"653001"},{label:"阿克陶县",value:"653022"},{label:"阿合奇县",value:"653023"},{label:"乌恰县",value:"653024"}],[{label:"喀什市",value:"653101"},{label:"疏附县",value:"653121"},{label:"疏勒县",value:"653122"},{label:"英吉沙县",value:"653123"},{label:"泽普县",value:"653124"},{label:"莎车县",value:"653125"},{label:"叶城县",value:"653126"},{label:"麦盖提县",value:"653127"},{label:"岳普湖县",value:"653128"},{label:"伽师县",value:"653129"},{label:"巴楚县",value:"653130"},{label:"塔什库尔干塔吉克自治县",value:"653131"}],[{label:"和田市",value:"653201"},{label:"和田县",value:"653221"},{label:"墨玉县",value:"653222"},{label:"皮山县",value:"653223"},{label:"洛浦县",value:"653224"},{label:"策勒县",value:"653225"},{label:"于田县",value:"653226"},{label:"民丰县",value:"653227"}],[{label:"伊宁市",value:"654002"},{label:"奎屯市",value:"654003"},{label:"霍尔果斯市",value:"654004"},{label:"伊宁县",value:"654021"},{label:"察布查尔锡伯自治县",value:"654022"},{label:"霍城县",value:"654023"},{label:"巩留县",value:"654024"},{label:"新源县",value:"654025"},{label:"昭苏县",value:"654026"},{label:"特克斯县",value:"654027"},{label:"尼勒克县",value:"654028"}],[{label:"塔城市",value:"654201"},{label:"乌苏市",value:"654202"},{label:"额敏县",value:"654221"},{label:"沙湾县",value:"654223"},{label:"托里县",value:"654224"},{label:"裕民县",value:"654225"},{label:"和布克赛尔蒙古自治县",value:"654226"}],[{label:"阿勒泰市",value:"654301"},{label:"布尔津县",value:"654321"},{label:"富蕴县",value:"654322"},{label:"福海县",value:"654323"},{label:"哈巴河县",value:"654324"},{label:"青河县",value:"654325"},{label:"吉木乃县",value:"654326"}],[{label:"石河子市",value:"659001"},{label:"阿拉尔市",value:"659002"},{label:"图木舒克市",value:"659003"},{label:"五家渠市",value:"659004"},{label:"铁门关市",value:"659006"}]],[[{label:"台北",value:"660101"}],[{label:"高雄",value:"660201"}],[{label:"基隆",value:"660301"}],[{label:"台中",value:"660401"}],[{label:"台南",value:"660501"}],[{label:"新竹",value:"660601"}],[{label:"嘉义",value:"660701"}],[{label:"宜兰",value:"660801"}],[{label:"桃园",value:"660901"}],[{label:"苗栗",value:"661001"}],[{label:"彰化",value:"661101"}],[{label:"南投",value:"661201"}],[{label:"云林",value:"661301"}],[{label:"屏东",value:"661401"}],[{label:"台东",value:"661501"}],[{label:"花莲",value:"661601"}],[{label:"澎湖",value:"661701"}]],[[{label:"香港岛",value:"670101"}],[{label:"九龙",value:"670201"}],[{label:"新界",value:"670301"}]],[[{label:"澳门半岛",value:"680101"}],[{label:"氹仔岛",value:"680201"}],[{label:"路环岛",value:"680301"}],[{label:"路氹城",value:"680401"}]],[[{label:"钓鱼岛全岛",value:"690101"}]]];l.default=t},"7ca3":function(e,l,a){var t=a("d551");e.exports=function(e,l,a){return l=t(l),l in e?Object.defineProperty(e,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[l]=a,e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"7d69":function(e,l,a){"use strict";var t=a("2fc6"),n=a.n(t);n.a},"7eb4":function(e,l,a){var t=a("9fc1")();e.exports=t},"81fa":function(e,l,a){},"828b":function(e,l,a){"use strict";function t(e,l,a,t,n,u,r,o,i,v){var s,b="function"===typeof e?e.options:e;if(i){b.components||(b.components={});var c=Object.prototype.hasOwnProperty;for(var f in i)c.call(i,f)&&!c.call(b.components,f)&&(b.components[f]=i[f])}if(v&&("function"===typeof v.beforeCreate&&(v.beforeCreate=[v.beforeCreate]),(v.beforeCreate||(v.beforeCreate=[])).unshift((function(){this[v.__module]=this})),(b.mixins||(b.mixins=[])).push(v)),l&&(b.render=l,b.staticRenderFns=a,b._compiled=!0),t&&(b.functional=!0),u&&(b._scopeId="data-v-"+u),r?(s=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),n&&n.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(r)},b._ssrRegister=s):n&&(s=o?function(){n.call(this,this.$root.$options.shadowRoot)}:n),s)if(b.functional){b._injectStyles=s;var d=b.render;b.render=function(e,l){return s.call(l),d(e,l)}}else{var p=b.beforeCreate;b.beforeCreate=p?[].concat(p,s):[s]}return{exports:e,options:b}}a.d(l,"a",(function(){return t}))},"85b7":function(e,l,a){"use strict";var t=a("c89f"),n=a.n(t);n.a},"871e":function(e,l,a){"use strict";a.r(l);var t=a("2ed8"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},"8b0d":function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var n=t(a("7eb4")),u=t(a("ee10")),r=t(a("7ca3")),o=a("ab49"),i=a("8f59"),v=a("d585"),s=a("9ec3");function b(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function c(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?b(Object(a),!0).forEach((function(l){(0,r.default)(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):b(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}var f={data:function(){return{showDisplay:!1,rocallTime:"",textTip:"",showConfirm:!1,orderDetailsData:{},timeout:!1,isOvertime:!1,orderId:null,activeRadio:0,time:null,isPayment:!1,times:null,phone:""}},computed:{orderListDataes:function(){return this.orderListData()},orderDataes:function(){var e=[];if(!1===this.showDisplay){if(this.orderListDataes.length>2)for(var l=0;l<2;l++)e.push(this.orderListDataes[l]);else e=this.orderListDataes;return e}return this.orderListDataes}},components:{Status:function(){Promise.all([a.e("common/vendor"),a.e("pages/details/components/status")]).then(function(){return resolve(a("4cbd"))}.bind(null,a)).catch(a.oe)},OrderDetail:function(){Promise.all([a.e("common/vendor"),a.e("pages/details/components/orderDetail")]).then(function(){return resolve(a("c371"))}.bind(null,a)).catch(a.oe)},DeliveryInfo:function(){Promise.all([a.e("common/vendor"),a.e("pages/details/components/deliveryInfo")]).then(function(){return resolve(a("3565"))}.bind(null,a)).catch(a.oe)},OrderInfo:function(){Promise.all([a.e("common/vendor"),a.e("pages/details/components/orderInfo")]).then(function(){return resolve(a("3e0a"))}.bind(null,a)).catch(a.oe)}},onLoad:function(e){this.getBaseData(e.orderId)},methods:c(c(c({},(0,i.mapMutations)(["setOrderData","initdishListMut"])),(0,i.mapState)(["shopInfo","orderListData","orderData"])),{},{getBaseData:function(e){var l=this;(0,o.getOrderDetail)(e).then((function(e){1===e.code&&(l.orderDetailsData=e.data,l.initdishListMut(l.orderDetailsData.orderDetailList),1===l.orderDetailsData.status&&l.runTimeBack(l.orderDetailsData.orderTime))}))},handleReminder:function(e){var l=this;(0,o.reminderOrder)(e.id).then((function(a){1===a.code&&(l.showConfirm=!0,l.textTip="您的催单信息已发出！",l.$refs.status.$refs.commonPopup.open(e.type),l.orderId=e.id)}))},cancel:function(e,l){var a=this;(0,o.cancelOrder)(l.id).then((function(t){1===t.code&&(a.isPayment=!0,a.showConfirm=!0,a.textTip="您的订单已取消！",a.$refs.status.$refs.commonPopup.open(e),a.orderId=l.id)}))},handleCancel:function(e){1===e.obj.status||2===e.obj.status?this.cancel(e.type,e.obj):(this.showConfirm=!1,this.$refs.status.$refs.commonPopup.open(e.type),this.textTip="请联系商家进行取消！")},oneMoreOrder:function(l){return(0,u.default)(n.default.mark((function a(){return n.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,(0,o.delShoppingCart)();case 2:(0,o.repetitionOrder)(l).then((function(l){1===l.code&&e.redirectTo({url:"/pages/index/index"})}));case 3:case"end":return a.stop()}}),a)})))()},statusWord:function(e){if(this.timeout&&1===e||6===this.orderDetailsData.status)return"订单已取消";switch(e){case 2:return"等待商户接单";case 3:return"商家已接单";case 4:return"订单派送中";case 5:return"订单已完成"}},runTimeBack:function(e){var l=Date.parse(e.replace(/-/g,"/")),a=Date.parse(new Date),t=9e5-(a-l);if(t<0)this.timeout=!0,clearTimeout(this.times),this.cancel("center",this.orderDetailsData);else{var n=parseInt(t/1e3/60%60),u=parseInt(t/1e3%60);n=n<10?"0"+n:n,u=u<10?"0"+u:u,this.rocallTime=n+":"+u;var r=this;if(n>=0&&u>=0){if(0===n&&0===u)return this.timeout=!0,clearTimeout(this.times),void this.cancel("center",this.orderDetailsData);this.times=setTimeout((function(){r.runTimeBack(e)}),1e3)}}},getNewImage:function(e){return"".concat(v.baseUrl,"/common/download?name=").concat(e)},goBack:function(){e.redirectTo({url:"/pages/historyOrder/historyOrder"})},openPopuos:function(e){this.$refs.status.$refs.commonPopup.open(e)},handleRefund:function(e){this.showConfirm=!1,this.openPopuos(e),this.textTip="请联系商家进行退款！"},handlePhone:function(e,l){this.$refs.phone.open(e),this.phone=l},closePopup:function(e){this.$refs.phone.close(e)},closePopupInfo:function(e){this.$refs.status.$refs.commonPopup.close(e),this.getBaseData(this.orderId)},handlePay:function(l){var a={orderNumber:this.orderDetailsData.number,orderAmount:this.orderDetailsData.amount,orderTime:this.orderDetailsData.orderTime};this.setOrderData(a),e.redirectTo({url:"/pages/pay/index?orderId="+l})},call:function(){(0,s.call)(this.phone)}})};l.default=f}).call(this,a("df3c")["default"])},"8b2f":function(e,l,a){"use strict";a.r(l);var t=a("6883"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},"8f59":function(e,l,a){"use strict";(function(l){var a="undefined"!==typeof window?window:"undefined"!==typeof l?l:{},t=a.__VUE_DEVTOOLS_GLOBAL_HOOK__;function n(e,l){if(void 0===l&&(l=[]),null===e||"object"!==typeof e)return e;var a=function(e,l){return e.filter(l)[0]}(l,(function(l){return l.original===e}));if(a)return a.copy;var t=Array.isArray(e)?[]:{};return l.push({original:e,copy:t}),Object.keys(e).forEach((function(a){t[a]=n(e[a],l)})),t}function u(e,l){Object.keys(e).forEach((function(a){return l(e[a],a)}))}function r(e){return null!==e&&"object"===typeof e}var o=function(e,l){this.runtime=l,this._children=Object.create(null),this._rawModule=e;var a=e.state;this.state=("function"===typeof a?a():a)||{}},i={namespaced:{configurable:!0}};i.namespaced.get=function(){return!!this._rawModule.namespaced},o.prototype.addChild=function(e,l){this._children[e]=l},o.prototype.removeChild=function(e){delete this._children[e]},o.prototype.getChild=function(e){return this._children[e]},o.prototype.hasChild=function(e){return e in this._children},o.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},o.prototype.forEachChild=function(e){u(this._children,e)},o.prototype.forEachGetter=function(e){this._rawModule.getters&&u(this._rawModule.getters,e)},o.prototype.forEachAction=function(e){this._rawModule.actions&&u(this._rawModule.actions,e)},o.prototype.forEachMutation=function(e){this._rawModule.mutations&&u(this._rawModule.mutations,e)},Object.defineProperties(o.prototype,i);var v=function(e){this.register([],e,!1)};v.prototype.get=function(e){return e.reduce((function(e,l){return e.getChild(l)}),this.root)},v.prototype.getNamespace=function(e){var l=this.root;return e.reduce((function(e,a){return l=l.getChild(a),e+(l.namespaced?a+"/":"")}),"")},v.prototype.update=function(e){(function e(l,a,t){0;if(a.update(t),t.modules)for(var n in t.modules){if(!a.getChild(n))return void 0;e(l.concat(n),a.getChild(n),t.modules[n])}})([],this.root,e)},v.prototype.register=function(e,l,a){var t=this;void 0===a&&(a=!0);var n=new o(l,a);if(0===e.length)this.root=n;else{var r=this.get(e.slice(0,-1));r.addChild(e[e.length-1],n)}l.modules&&u(l.modules,(function(l,n){t.register(e.concat(n),l,a)}))},v.prototype.unregister=function(e){var l=this.get(e.slice(0,-1)),a=e[e.length-1],t=l.getChild(a);t&&t.runtime&&l.removeChild(a)},v.prototype.isRegistered=function(e){var l=this.get(e.slice(0,-1)),a=e[e.length-1];return!!l&&l.hasChild(a)};var s;var b=function(e){var l=this;void 0===e&&(e={}),!s&&"undefined"!==typeof window&&window.Vue&&y(window.Vue);var a=e.plugins;void 0===a&&(a=[]);var n=e.strict;void 0===n&&(n=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new v(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new s,this._makeLocalGettersCache=Object.create(null);var u=this,r=this.dispatch,o=this.commit;this.dispatch=function(e,l){return r.call(u,e,l)},this.commit=function(e,l,a){return o.call(u,e,l,a)},this.strict=n;var i=this._modules.root.state;h(this,i,[],this._modules.root),p(this,i),a.forEach((function(e){return e(l)}));var b=void 0!==e.devtools?e.devtools:s.config.devtools;b&&function(e){t&&(e._devtoolHook=t,t.emit("vuex:init",e),t.on("vuex:travel-to-state",(function(l){e.replaceState(l)})),e.subscribe((function(e,l){t.emit("vuex:mutation",e,l)}),{prepend:!0}),e.subscribeAction((function(e,l){t.emit("vuex:action",e,l)}),{prepend:!0}))}(this)},c={state:{configurable:!0}};function f(e,l,a){return l.indexOf(e)<0&&(a&&a.prepend?l.unshift(e):l.push(e)),function(){var a=l.indexOf(e);a>-1&&l.splice(a,1)}}function d(e,l){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var a=e.state;h(e,a,[],e._modules.root,!0),p(e,a,l)}function p(e,l,a){var t=e._vm;e.getters={},e._makeLocalGettersCache=Object.create(null);var n=e._wrappedGetters,r={};u(n,(function(l,a){r[a]=function(e,l){return function(){return e(l)}}(l,e),Object.defineProperty(e.getters,a,{get:function(){return e._vm[a]},enumerable:!0})}));var o=s.config.silent;s.config.silent=!0,e._vm=new s({data:{$$state:l},computed:r}),s.config.silent=o,e.strict&&function(e){e._vm.$watch((function(){return this._data.$$state}),(function(){0}),{deep:!0,sync:!0})}(e),t&&(a&&e._withCommit((function(){t._data.$$state=null})),s.nextTick((function(){return t.$destroy()})))}function h(e,l,a,t,n){var u=!a.length,r=e._modules.getNamespace(a);if(t.namespaced&&(e._modulesNamespaceMap[r],e._modulesNamespaceMap[r]=t),!u&&!n){var o=m(l,a.slice(0,-1)),i=a[a.length-1];e._withCommit((function(){s.set(o,i,t.state)}))}var v=t.context=function(e,l,a){var t=""===l,n={dispatch:t?e.dispatch:function(a,t,n){var u=g(a,t,n),r=u.payload,o=u.options,i=u.type;return o&&o.root||(i=l+i),e.dispatch(i,r)},commit:t?e.commit:function(a,t,n){var u=g(a,t,n),r=u.payload,o=u.options,i=u.type;o&&o.root||(i=l+i),e.commit(i,r,o)}};return Object.defineProperties(n,{getters:{get:t?function(){return e.getters}:function(){return function(e,l){if(!e._makeLocalGettersCache[l]){var a={},t=l.length;Object.keys(e.getters).forEach((function(n){if(n.slice(0,t)===l){var u=n.slice(t);Object.defineProperty(a,u,{get:function(){return e.getters[n]},enumerable:!0})}})),e._makeLocalGettersCache[l]=a}return e._makeLocalGettersCache[l]}(e,l)}},state:{get:function(){return m(e.state,a)}}}),n}(e,r,a);t.forEachMutation((function(l,a){var t=r+a;(function(e,l,a,t){var n=e._mutations[l]||(e._mutations[l]=[]);n.push((function(l){a.call(e,t.state,l)}))})(e,t,l,v)})),t.forEachAction((function(l,a){var t=l.root?a:r+a,n=l.handler||l;(function(e,l,a,t){var n=e._actions[l]||(e._actions[l]=[]);n.push((function(l){var n=a.call(e,{dispatch:t.dispatch,commit:t.commit,getters:t.getters,state:t.state,rootGetters:e.getters,rootState:e.state},l);return function(e){return e&&"function"===typeof e.then}(n)||(n=Promise.resolve(n)),e._devtoolHook?n.catch((function(l){throw e._devtoolHook.emit("vuex:error",l),l})):n}))})(e,t,n,v)})),t.forEachGetter((function(l,a){var t=r+a;(function(e,l,a,t){if(e._wrappedGetters[l])return void 0;e._wrappedGetters[l]=function(e){return a(t.state,t.getters,e.state,e.getters)}})(e,t,l,v)})),t.forEachChild((function(t,u){h(e,l,a.concat(u),t,n)}))}function m(e,l){return l.reduce((function(e,l){return e[l]}),e)}function g(e,l,a){return r(e)&&e.type&&(a=l,l=e,e=e.type),{type:e,payload:l,options:a}}function y(e){s&&e===s||(s=e,
/*!
 * vuex v3.6.2
 * (c) 2021 Evan You
 * @license MIT
 */
function(e){var l=Number(e.version.split(".")[0]);if(l>=2)e.mixin({beforeCreate:t});else{var a=e.prototype._init;e.prototype._init=function(e){void 0===e&&(e={}),e.init=e.init?[t].concat(e.init):t,a.call(this,e)}}function t(){var e=this.$options;e.store?this.$store="function"===typeof e.store?e.store():e.store:e.parent&&e.parent.$store&&(this.$store=e.parent.$store)}}(s))}c.state.get=function(){return this._vm._data.$$state},c.state.set=function(e){0},b.prototype.commit=function(e,l,a){var t=this,n=g(e,l,a),u=n.type,r=n.payload,o=(n.options,{type:u,payload:r}),i=this._mutations[u];i&&(this._withCommit((function(){i.forEach((function(e){e(r)}))})),this._subscribers.slice().forEach((function(e){return e(o,t.state)})))},b.prototype.dispatch=function(e,l){var a=this,t=g(e,l),n=t.type,u=t.payload,r={type:n,payload:u},o=this._actions[n];if(o){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(r,a.state)}))}catch(v){0}var i=o.length>1?Promise.all(o.map((function(e){return e(u)}))):o[0](u);return new Promise((function(e,l){i.then((function(l){try{a._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(r,a.state)}))}catch(v){0}e(l)}),(function(e){try{a._actionSubscribers.filter((function(e){return e.error})).forEach((function(l){return l.error(r,a.state,e)}))}catch(v){0}l(e)}))}))}},b.prototype.subscribe=function(e,l){return f(e,this._subscribers,l)},b.prototype.subscribeAction=function(e,l){var a="function"===typeof e?{before:e}:e;return f(a,this._actionSubscribers,l)},b.prototype.watch=function(e,l,a){var t=this;return this._watcherVM.$watch((function(){return e(t.state,t.getters)}),l,a)},b.prototype.replaceState=function(e){var l=this;this._withCommit((function(){l._vm._data.$$state=e}))},b.prototype.registerModule=function(e,l,a){void 0===a&&(a={}),"string"===typeof e&&(e=[e]),this._modules.register(e,l),h(this,this.state,e,this._modules.get(e),a.preserveState),p(this,this.state)},b.prototype.unregisterModule=function(e){var l=this;"string"===typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){var a=m(l.state,e.slice(0,-1));s.delete(a,e[e.length-1])})),d(this)},b.prototype.hasModule=function(e){return"string"===typeof e&&(e=[e]),this._modules.isRegistered(e)},b.prototype[[104,111,116,85,112,100,97,116,101].map((function(e){return String.fromCharCode(e)})).join("")]=function(e){this._modules.update(e),d(this,!0)},b.prototype._withCommit=function(e){var l=this._committing;this._committing=!0,e(),this._committing=l},Object.defineProperties(b.prototype,c);var _=D((function(e,l){var a={};return $(l).forEach((function(l){var t=l.key,n=l.val;a[t]=function(){var l=this.$store.state,a=this.$store.getters;if(e){var t=k(this.$store,"mapState",e);if(!t)return;l=t.context.state,a=t.context.getters}return"function"===typeof n?n.call(this,l,a):l[n]},a[t].vuex=!0})),a})),w=D((function(e,l){var a={};return $(l).forEach((function(l){var t=l.key,n=l.val;a[t]=function(){var l=[],a=arguments.length;while(a--)l[a]=arguments[a];var t=this.$store.commit;if(e){var u=k(this.$store,"mapMutations",e);if(!u)return;t=u.context.commit}return"function"===typeof n?n.apply(this,[t].concat(l)):t.apply(this.$store,[n].concat(l))}})),a})),O=D((function(e,l){var a={};return $(l).forEach((function(l){var t=l.key,n=l.val;n=e+n,a[t]=function(){if(!e||k(this.$store,"mapGetters",e))return this.$store.getters[n]},a[t].vuex=!0})),a})),x=D((function(e,l){var a={};return $(l).forEach((function(l){var t=l.key,n=l.val;a[t]=function(){var l=[],a=arguments.length;while(a--)l[a]=arguments[a];var t=this.$store.dispatch;if(e){var u=k(this.$store,"mapActions",e);if(!u)return;t=u.context.dispatch}return"function"===typeof n?n.apply(this,[t].concat(l)):t.apply(this.$store,[n].concat(l))}})),a}));function $(e){return function(e){return Array.isArray(e)||r(e)}(e)?Array.isArray(e)?e.map((function(e){return{key:e,val:e}})):Object.keys(e).map((function(l){return{key:l,val:e[l]}})):[]}function D(e){return function(l,a){return"string"!==typeof l?(a=l,l=""):"/"!==l.charAt(l.length-1)&&(l+="/"),e(l,a)}}function k(e,l,a){var t=e._modulesNamespaceMap[a];return t}function S(e,l,a){var t=a?e.groupCollapsed:e.group;try{t.call(e,l)}catch(n){e.log(l)}}function A(e){try{e.groupEnd()}catch(l){e.log("—— log end ——")}}function P(){var e=new Date;return" @ "+j(e.getHours(),2)+":"+j(e.getMinutes(),2)+":"+j(e.getSeconds(),2)+"."+j(e.getMilliseconds(),3)}function j(e,l){return function(e,l){return new Array(l+1).join(e)}("0",l-e.toString().length)+e}var I={Store:b,install:y,version:"3.6.2",mapState:_,mapMutations:w,mapGetters:O,mapActions:x,createNamespacedHelpers:function(e){return{mapState:_.bind(null,e),mapGetters:O.bind(null,e),mapMutations:w.bind(null,e),mapActions:x.bind(null,e)}},createLogger:function(e){void 0===e&&(e={});var l=e.collapsed;void 0===l&&(l=!0);var a=e.filter;void 0===a&&(a=function(e,l,a){return!0});var t=e.transformer;void 0===t&&(t=function(e){return e});var u=e.mutationTransformer;void 0===u&&(u=function(e){return e});var r=e.actionFilter;void 0===r&&(r=function(e,l){return!0});var o=e.actionTransformer;void 0===o&&(o=function(e){return e});var i=e.logMutations;void 0===i&&(i=!0);var v=e.logActions;void 0===v&&(v=!0);var s=e.logger;return void 0===s&&(s=console),function(e){var b=n(e.state);"undefined"!==typeof s&&(i&&e.subscribe((function(e,r){var o=n(r);if(a(e,b,o)){var i=P(),v=u(e),c="mutation "+e.type+i;S(s,c,l),s.log("%c prev state","color: #9E9E9E; font-weight: bold",t(b)),s.log("%c mutation","color: #03A9F4; font-weight: bold",v),s.log("%c next state","color: #4CAF50; font-weight: bold",t(o)),A(s)}b=o})),v&&e.subscribeAction((function(e,a){if(r(e,a)){var t=P(),n=o(e),u="action "+e.type+t;S(s,u,l),s.log("%c action","color: #03A9F4; font-weight: bold",n),A(s)}})))}}};e.exports=I}).call(this,a("0ee4"))},9008:function(e,l){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},9111:function(e,l,a){"use strict";var t=a("38b9"),n=a.n(t);n.a},9175:function(e,l,a){},"931d":function(e,l,a){var t=a("7647"),n=a("011a");e.exports=function(e,l,a){if(n())return Reflect.construct.apply(null,arguments);var u=[null];u.push.apply(u,l);var r=new(e.bind.apply(e,u));return a&&t(r,a.prototype),r},e.exports.__esModule=!0,e.exports["default"]=e.exports},"93f6":function(e,l,a){},9565:function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.request=function(l){var a=l.url,t=void 0===a?"":a,r=l.params,o=void 0===r?{}:r,i=l.method,v=void 0===i?"GET":i;e.getStorage({key:""});var s=n.default.state,b={Accept:"application/json","Access-Control-Allow-Origin":"*","Content-Type":"application/json",authentication:s.token},c=new Promise((function(l,a){n.default.commit("setLodding",!1),e.request({url:u.baseUrl+t,data:o,header:b,method:v,success:function(e){var t=e.data;200==t.code||1===t.code?l(e.data):a(e.data)},fail:function(e){var l={data:{msg:e.data}};a(l)}})}));return c};var n=t(a("77f6")),u=a("d585")}).call(this,a("df3c")["default"])},"96b1":function(e,l,a){"use strict";var t=a("64ea"),n=a.n(t);n.a},9908:function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var n=t(a("a567")),u={data:function(){return{productInfo:{id:1,name:"精选美味商品1",price:29.9,image:"/static/logo_ruiji.png",description:"这是一款精心制作的美味商品，采用优质食材，口感丰富，营养均衡。"}}},onLoad:function(e){console.log("商品1页面加载"),e&&e.mid&&n.default.setMid(e.mid)},methods:{goBack:function(){e.navigateBack({delta:1})},goToOrder:function(){n.default.setMid("4"),n.default.navigateToWithRefresh("/pages/index/index")}}};l.default=u}).call(this,a("df3c")["default"])},"9c86":function(e,l){},"9ec3":function(e,l,a){"use strict";(function(e){Object.defineProperty(l,"__esModule",{value:!0}),l.statusWord=l.splitMobile=l.runTimeBack=l.presentFormat=l.getWeekDate=l.getOvertime=l.getLableVal=l.dateFormat=l.call=void 0;l.call=function(l){e.makePhoneCall({phoneNumber:l,success:function(e){},fail:function(e){}})};l.splitMobile=function(e){return String(e).replace(/(?=(\d{4})+$)/g,"-")};l.getLableVal=function(e){switch(e){case"1":return"公司";case"2":return"家";case"3":return"学校";default:return"其他"}};l.statusWord=function(e,l){if(l){if(1===e&&l>0)return"待付款";if(6===e||l<0&&1===e)return"已完成"}switch(e){case 1:return"待付款";case 2:return"等待商家接单";case 3:return"等待骑手接单";case 4:return"派送中";case 5:return"派送中";case 6:return"已完成";case 7:return"已取消"}};l.runTimeBack=function(){};l.getOvertime=function(e){var l=Date.parse(new Date(e.replace(/-/g,"/"))),a=Date.parse(new Date),t=9e5-(a-l);return t};l.getWeekDate=function(e){var l=new Date(e),a=l.getDay(),t=["周日","周一","周二","周三","周四","周五","周六"][a];return t};function a(e){return e<10?"0"+e:e}l.presentFormat=function(){var e=new Date;e.setTime(e.getTime()+36e5);var l=e.getFullYear(),t=e.getMonth()+1,n=e.getDate(),u=e.getHours(),r=e.getMinutes(),o=e.getSeconds(),i=+l+"-"+a(t)+"-"+a(n)+" "+a(u)+":"+a(r)+":"+a(o);return i};l.dateFormat=function(e,l){var a,t=new Date,n=null;n=e?new Date(t.setDate(t.getDate()+1)):t;var u=n.getFullYear().toString(),r=(n.getMonth()+1).toString(),o=n.getDate().toString();return r<10&&(r="0"+r),o<10&&(o="0"+o),a=u+"-"+r+"-"+o+" "+l+":00",console.log(a,44),a}}).call(this,a("df3c")["default"])},"9fc1":function(e,l,a){var t=a("3b2d")["default"];function n(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=n=function(){return a},e.exports.__esModule=!0,e.exports["default"]=e.exports;var l,a={},u=Object.prototype,r=u.hasOwnProperty,o=Object.defineProperty||function(e,l,a){e[l]=a.value},i="function"==typeof Symbol?Symbol:{},v=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",b=i.toStringTag||"@@toStringTag";function c(e,l,a){return Object.defineProperty(e,l,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[l]}try{c({},"")}catch(l){c=function(e,l,a){return e[l]=a}}function f(e,l,a,t){var n=l&&l.prototype instanceof y?l:y,u=Object.create(n.prototype),r=new T(t||[]);return o(u,"_invoke",{value:A(e,a,r)}),u}function d(e,l,a){try{return{type:"normal",arg:e.call(l,a)}}catch(e){return{type:"throw",arg:e}}}a.wrap=f;var p="suspendedStart",h="executing",m="completed",g={};function y(){}function _(){}function w(){}var O={};c(O,v,(function(){return this}));var x=Object.getPrototypeOf,$=x&&x(x(E([])));$&&$!==u&&r.call($,v)&&(O=$);var D=w.prototype=y.prototype=Object.create(O);function k(e){["next","throw","return"].forEach((function(l){c(e,l,(function(e){return this._invoke(l,e)}))}))}function S(e,l){function a(n,u,o,i){var v=d(e[n],e,u);if("throw"!==v.type){var s=v.arg,b=s.value;return b&&"object"==t(b)&&r.call(b,"__await")?l.resolve(b.__await).then((function(e){a("next",e,o,i)}),(function(e){a("throw",e,o,i)})):l.resolve(b).then((function(e){s.value=e,o(s)}),(function(e){return a("throw",e,o,i)}))}i(v.arg)}var n;o(this,"_invoke",{value:function(e,t){function u(){return new l((function(l,n){a(e,t,l,n)}))}return n=n?n.then(u,u):u()}})}function A(e,a,t){var n=p;return function(u,r){if(n===h)throw Error("Generator is already running");if(n===m){if("throw"===u)throw r;return{value:l,done:!0}}for(t.method=u,t.arg=r;;){var o=t.delegate;if(o){var i=P(o,t);if(i){if(i===g)continue;return i}}if("next"===t.method)t.sent=t._sent=t.arg;else if("throw"===t.method){if(n===p)throw n=m,t.arg;t.dispatchException(t.arg)}else"return"===t.method&&t.abrupt("return",t.arg);n=h;var v=d(e,a,t);if("normal"===v.type){if(n=t.done?m:"suspendedYield",v.arg===g)continue;return{value:v.arg,done:t.done}}"throw"===v.type&&(n=m,t.method="throw",t.arg=v.arg)}}}function P(e,a){var t=a.method,n=e.iterator[t];if(n===l)return a.delegate=null,"throw"===t&&e.iterator["return"]&&(a.method="return",a.arg=l,P(e,a),"throw"===a.method)||"return"!==t&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+t+"' method")),g;var u=d(n,e.iterator,a.arg);if("throw"===u.type)return a.method="throw",a.arg=u.arg,a.delegate=null,g;var r=u.arg;return r?r.done?(a[e.resultName]=r.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=l),a.delegate=null,g):r:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function j(e){var l={tryLoc:e[0]};1 in e&&(l.catchLoc=e[1]),2 in e&&(l.finallyLoc=e[2],l.afterLoc=e[3]),this.tryEntries.push(l)}function I(e){var l=e.completion||{};l.type="normal",delete l.arg,e.completion=l}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function E(e){if(e||""===e){var a=e[v];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,u=function a(){for(;++n<e.length;)if(r.call(e,n))return a.value=e[n],a.done=!1,a;return a.value=l,a.done=!0,a};return u.next=u}}throw new TypeError(t(e)+" is not iterable")}return _.prototype=w,o(D,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:_,configurable:!0}),_.displayName=c(w,b,"GeneratorFunction"),a.isGeneratorFunction=function(e){var l="function"==typeof e&&e.constructor;return!!l&&(l===_||"GeneratorFunction"===(l.displayName||l.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,b,"GeneratorFunction")),e.prototype=Object.create(D),e},a.awrap=function(e){return{__await:e}},k(S.prototype),c(S.prototype,s,(function(){return this})),a.AsyncIterator=S,a.async=function(e,l,t,n,u){void 0===u&&(u=Promise);var r=new S(f(e,l,t,n),u);return a.isGeneratorFunction(l)?r:r.next().then((function(e){return e.done?e.value:r.next()}))},k(D),c(D,b,"Generator"),c(D,v,(function(){return this})),c(D,"toString",(function(){return"[object Generator]"})),a.keys=function(e){var l=Object(e),a=[];for(var t in l)a.push(t);return a.reverse(),function e(){for(;a.length;){var t=a.pop();if(t in l)return e.value=t,e.done=!1,e}return e.done=!0,e}},a.values=E,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=l,this.done=!1,this.delegate=null,this.method="next",this.arg=l,this.tryEntries.forEach(I),!e)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=l)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function t(t,n){return o.type="throw",o.arg=e,a.next=t,n&&(a.method="next",a.arg=l),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var u=this.tryEntries[n],o=u.completion;if("root"===u.tryLoc)return t("end");if(u.tryLoc<=this.prev){var i=r.call(u,"catchLoc"),v=r.call(u,"finallyLoc");if(i&&v){if(this.prev<u.catchLoc)return t(u.catchLoc,!0);if(this.prev<u.finallyLoc)return t(u.finallyLoc)}else if(i){if(this.prev<u.catchLoc)return t(u.catchLoc,!0)}else{if(!v)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return t(u.finallyLoc)}}}},abrupt:function(e,l){for(var a=this.tryEntries.length-1;a>=0;--a){var t=this.tryEntries[a];if(t.tryLoc<=this.prev&&r.call(t,"finallyLoc")&&this.prev<t.finallyLoc){var n=t;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=l&&l<=n.finallyLoc&&(n=null);var u=n?n.completion:{};return u.type=e,u.arg=l,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(u)},complete:function(e,l){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&l&&(this.next=l),g},finish:function(e){for(var l=this.tryEntries.length-1;l>=0;--l){var a=this.tryEntries[l];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),I(a),g}},catch:function(e){for(var l=this.tryEntries.length-1;l>=0;--l){var a=this.tryEntries[l];if(a.tryLoc===e){var t=a.completion;if("throw"===t.type){var n=t.arg;I(a)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,a,t){return this.delegate={iterator:E(e),resultName:a,nextLoc:t},"next"===this.method&&(this.arg=l),g}},a}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},a1d4:function(e,l,a){"use strict";a.r(l);var t=a("5268"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},a507:function(e,l,a){"use strict";a.r(l);var t=a("565b"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},a567:function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.midManager=l.default=void 0;var n=t(a("7ca3"));function u(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function r(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?u(Object(a),!0).forEach((function(l){(0,n.default)(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}var o={setMid:function(l){if(l)try{e.setStorageSync("persistentMid",l.toString()),console.log("Mid参数已保存:",l)}catch(a){console.error("保存Mid参数失败:",a)}},getMid:function(){try{var l=e.getStorageSync("persistentMid");return l||null}catch(a){return console.error("获取Mid参数失败:",a),null}},clearMid:function(){try{e.removeStorageSync("persistentMid"),console.log("Mid参数已清除")}catch(l){console.error("清除Mid参数失败:",l)}},hasMid:function(){return!!this.getMid()},addMidToUrl:function(e){var l=this.getMid();if(!l)return e;var a=e.includes("?")?"&":"?";return"".concat(e).concat(a,"mid=").concat(l)},navigateTo:function(l){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=this.addMidToUrl(l);e.navigateTo(r({url:t},a))},navigateToWithRefresh:function(l){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=this.addMidToUrl(l);e.reLaunch(r({url:t},a))},redirectTo:function(l){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=this.addMidToUrl(l);e.redirectTo(r({url:t},a))}};l.midManager=o;var i=o;l.default=i}).call(this,a("df3c")["default"])},a708:function(e,l,a){var t=a("6454");e.exports=function(e){if(Array.isArray(e))return t(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},a9c4:function(e,l,a){"use strict";a.r(l);var t=a("f209"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},ab49:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.userLogin=l.submitOrderSubmit=l.repetitionOrder=l.reminderOrder=l.querySetmealDishById=l.querySetmeaList=l.queryOrdersCheckStatus=l.queryOrderUserPage=l.queryAddressBookList=l.queryAddressBookById=l.putAddressBookDefault=l.paymentOrder=l.payOrder=l.openTable=l.oneOrderAgain=l.newShoppingCartSub=l.newAddShoppingCartAdd=l.getTableState=l.getTableOrderDishList=l.getShoppingCartList=l.getShopStatus=l.getOrderPage=l.getOrderDetail=l.getMoreNorm=l.getList=l.getEstimatedDeliveryTime=l.getDishList=l.getDishDetail=l.getCategoryList=l.getAddressBookDefault=l.editHoppingCart=l.editAddressBook=l.dishListByCategoryId=l.delShoppingCart=l.delDish=l.delAddressBook=l.commonDownload=l.clearOrder=l.cancelOrder=l.addShoppingCart=l.addDish=l.addAddressBook=void 0;var t=a("9565");l.openTable=function(e){return(0,t.request)({url:"/user/table/open/".concat(e.tableId,"/").concat(e.seatNumber),method:"GET",params:e})};l.getTableState=function(e){return(0,t.request)({url:"/user/table/tableStatus/".concat(e.shopId,"/").concat(e.storeId,"/").concat(e.tableId),method:"GET",params:e})};l.getTableOrderDishList=function(e){return(0,t.request)({url:"/user/order/shopCart//".concat(e.tableId),method:"GET",params:e})};l.getMoreNorm=function(e){return(0,t.request)({url:"/user/dish/flavor/".concat(e.dishId),method:"GET",params:e})};l.getList=function(e){return(0,t.request)({url:"/user/dish/category",method:"GET",params:e})};l.getDishDetail=function(e){return(0,t.request)({url:"/user/dish/setmealDishList/".concat(e.setmealId),method:"GET",params:e})};l.getDishList=function(e){return(0,t.request)({url:"/user/dish/dishPageList/".concat(e.categoryId,"/").concat(e.type,"/").concat(e.page,"/").concat(e.pageSize),method:"GET",params:e})};l.addDish=function(e){return(0,t.request)({url:"/user/order/addDish",method:"POST",params:e})};l.delDish=function(e){return(0,t.request)({url:"/user/order/decreaseDish/".concat(e.tableId,"/").concat(e.dishId),method:"GET",params:e})};l.clearOrder=function(e){return(0,t.request)({url:"/user/order/cleanShopCart/".concat(e.tableId),method:"GET",params:e})};l.payOrder=function(e){return(0,t.request)({url:"/user/order/pay/".concat(e.tableId,"/").concat(e.jsCode),method:"GET",params:e})};l.userLogin=function(e){return(0,t.request)({url:"/user/user/login",method:"POST",params:e})};l.getCategoryList=function(e){return(0,t.request)({url:"/user/category/list",method:"GET",params:e})};l.dishListByCategoryId=function(e){return(0,t.request)({url:"/user/dish/list",method:"GET",params:e})};l.commonDownload=function(e){return(0,t.request)({url:"/user/common/download",method:"GET",params:e})};l.addShoppingCart=function(e){return(0,t.request)({url:"/user/shoppingCart",method:"POST",params:e})};l.querySetmeaList=function(e){return(0,t.request)({url:"/user/setmeal/list",method:"GET",params:e})};l.getShoppingCartList=function(e){return(0,t.request)({url:"/user/shoppingCart/list",method:"GET",params:e})};l.editHoppingCart=function(e){return(0,t.request)({url:"/user/shoppingCart",method:"PUT",params:e})};l.newAddShoppingCartAdd=function(e){return(0,t.request)({url:"/user/shoppingCart/add",method:"POST",params:e})};l.newShoppingCartSub=function(e){return(0,t.request)({url:"/user/shoppingCart/sub",method:"POST",params:e})};l.delShoppingCart=function(e){return(0,t.request)({url:"/user/shoppingCart/clean",method:"DELETE",params:e})};l.queryOrderUserPage=function(e){return(0,t.request)({url:"/user/order/userPage",method:"GET",params:e})};l.submitOrderSubmit=function(e){return(0,t.request)({url:"/user/order/submit",method:"POST",params:e})};l.queryAddressBookList=function(e){return(0,t.request)({url:"/user/addressBook/list",method:"GET",params:e})};l.putAddressBookDefault=function(e){return(0,t.request)({url:"/user/addressBook/default",method:"PUT",params:e})};l.addAddressBook=function(e){return(0,t.request)({url:"/user/addressBook",method:"POST",params:e})};l.editAddressBook=function(e){return(0,t.request)({url:"/user/addressBook",method:"PUT",params:e})};l.delAddressBook=function(e){return(0,t.request)({url:"/user/addressBook/?id=".concat(e),method:"DELETE",params:{id:e}})};l.queryAddressBookById=function(e){return(0,t.request)({url:"/user/addressBook/".concat(e.id),method:"GET",params:e})};l.oneOrderAgain=function(e){return(0,t.request)({url:"/user/order/again",method:"POST",params:e})};l.getAddressBookDefault=function(){return(0,t.request)({url:"/user/addressBook/default",method:"GET"})};l.querySetmealDishById=function(e){return(0,t.request)({url:"/user/setmeal/dish/".concat(e.id),method:"GET"})};l.getShopStatus=function(e){return(0,t.request)({url:"/user/shop/status",method:"GET"})};l.getOrderPage=function(e){return(0,t.request)({url:"/user/order/historyOrders",method:"GET",params:e})};l.getOrderDetail=function(e){return(0,t.request)({url:"/user/order/orderDetail/".concat(e),method:"GET"})};l.cancelOrder=function(e){return(0,t.request)({url:"/user/order/cancel/".concat(e),method:"PUT"})};l.reminderOrder=function(e){return(0,t.request)({url:"/user/order/reminder/".concat(e),method:"GET"})};l.paymentOrder=function(e){return(0,t.request)({url:"/user/order/payment",method:"PUT",params:e})};l.repetitionOrder=function(e){return(0,t.request)({url:"/user/order/repetition/".concat(e),method:"POST",params:e})};l.getEstimatedDeliveryTime=function(e){return(0,t.request)({url:"/user/order/getEstimatedDeliveryTime",method:"get",params:e})};l.queryOrdersCheckStatus=function(e){return(0,t.request)({url:"/user/order/queryOrdersCheckStatus",method:"get",params:e})}},ab82:function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.createAnimation=function(e,l){if(!l)return;return clearTimeout(l.timer),new v(e,l)};var n=t(a("7ca3")),u=t(a("67ad")),r=t(a("0bdb"));function o(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function i(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?o(Object(a),!0).forEach((function(l){(0,n.default)(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}var v=function(){function l(a,t){(0,u.default)(this,l),this.options=a,this.animation=e.createAnimation(i({},a)),this.currentStepAnimates={},this.next=0,this.$=t}return(0,r.default)(l,[{key:"_nvuePushAnimates",value:function(e,l){var a=this.currentStepAnimates[this.next],t={};if(t=a||{styles:{},config:{}},s.includes(e)){t.styles.transform||(t.styles.transform="");var n="";"rotate"===e&&(n="deg"),t.styles.transform+="".concat(e,"(").concat(l+n,") ")}else t.styles[e]="".concat(l);this.currentStepAnimates[this.next]=t}},{key:"_animateRun",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=this.$.$refs["ani"].ref;if(a)return new Promise((function(t,n){nvueAnimation.transition(a,i({styles:e},l),(function(e){t()}))}))}},{key:"_nvueNextAnimate",value:function(e){var l=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2?arguments[2]:void 0,n=e[a];if(n){var u=n.styles,r=n.config;this._animateRun(u,r).then((function(){a+=1,l._nvueNextAnimate(e,a,t)}))}else this.currentStepAnimates={},"function"===typeof t&&t(),this.isEnd=!0}},{key:"step",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.animation.step(e),this}},{key:"run",value:function(e){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((function(){"function"===typeof e&&e()}),this.$.durationTime)}}]),l}(),s=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];s.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((function(e){v.prototype[e]=function(){var l;return(l=this.animation)[e].apply(l,arguments),this}}))}).call(this,a("df3c")["default"])},ada0:function(e,l,a){"use strict";a.r(l);var t=a("229e"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},af34:function(e,l,a){var t=a("a708"),n=a("b893"),u=a("6382"),r=a("9008");e.exports=function(e){return t(e)||n(e)||u(e)||r()},e.exports.__esModule=!0,e.exports["default"]=e.exports},b0d3:function(e,l,a){},b50d:function(e,l,a){"use strict";var t=a("6dc9"),n=a.n(t);n.a},b893:function(e,l){e.exports=function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},b9a2:function(e,l,a){"use strict";var t=a("514e"),n=a.n(t);n.a},bb42:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;l.default={contact:"",person:"",personadd:"","contact-filled":"","person-filled":"","personadd-filled":"",phone:"",email:"",chatbubble:"",chatboxes:"","phone-filled":"","email-filled":"","chatbubble-filled":"","chatboxes-filled":"",weibo:"",weixin:"",pengyouquan:"",chat:"",qq:"",videocam:"",camera:"",mic:"",location:"","mic-filled":"",speech:"","location-filled":"",micoff:"",image:"",map:"",compose:"",trash:"",upload:"",download:"",close:"",redo:"",undo:"",refresh:"",star:"",plus:"",minus:"",circle:"",checkbox:"","close-filled":"",clear:"","refresh-filled":"","star-filled":"","plus-filled":"","minus-filled":"","circle-filled":"","checkbox-filled":"",closeempty:"",refreshempty:"",reload:"",starhalf:"",spinner:"","spinner-cycle":"",search:"",plusempty:"",forward:"",back:"","left-nav":"",checkmarkempty:"",home:"",navigate:"",gear:"",paperplane:"",info:"",help:"",locked:"",more:"",flag:"","home-filled":"","gear-filled":"","info-filled":"","help-filled":"","more-filled":"",settings:"",list:"",bars:"",loop:"",paperclip:"",eye:"",arrowup:"",arrowdown:"",arrowleft:"",arrowright:"",arrowthinup:"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",pulldown:"",closefill:"",sound:"",scan:""}},be66:function(e,l,a){},c0f2:function(e,l,a){},c3dc:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=a("7281"),n=(0,t.createProductPageConfig)(8,"精选美味商品8",36.9);l.default=n},c761:function(e,l,a){"use strict";var t=a("752b"),n=a.n(t);n.a},c82b:function(e,l,a){"use strict";var t=a("9175"),n=a.n(t);n.a},c89f:function(e,l,a){},d3b4:function(e,l,a){"use strict";(function(e,t){var n=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.LOCALE_ZH_HANT=l.LOCALE_ZH_HANS=l.LOCALE_FR=l.LOCALE_ES=l.LOCALE_EN=l.I18n=l.Formatter=void 0,l.compileI18nJsonStr=function(e,l){var a=l.locale,t=l.locales,n=l.delimiters;if(!D(e,n))return e;x||(x=new b);var u=[];Object.keys(t).forEach((function(e){e!==a&&u.push({locale:e,values:t[e]})})),u.unshift({locale:a,values:t[a]});try{return JSON.stringify(S(JSON.parse(e),u,n),null,2)}catch(r){}return e},l.hasI18nJson=function e(l,a){x||(x=new b);return A(l,(function(l,t){var n=l[t];return $(n)?!!D(n,a)||void 0:e(n,a)}))},l.initVueI18n=function(e){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0,t=arguments.length>3?arguments[3]:void 0;if("string"!==typeof e){var n=[l,e];e=n[0],l=n[1]}"string"!==typeof e&&(e=O());"string"!==typeof a&&(a="undefined"!==typeof __uniConfig&&__uniConfig.fallbackLocale||"en");var u=new _({locale:e,fallbackLocale:a,messages:l,watcher:t}),r=function(e,l){if("function"!==typeof getApp)r=function(e,l){return u.t(e,l)};else{var a=!1;r=function(e,l){var t=getApp().$vm;return t&&(t.$locale,a||(a=!0,w(t,u))),u.t(e,l)}}return r(e,l)};return{i18n:u,f:function(e,l,a){return u.f(e,l,a)},t:function(e,l){return r(e,l)},add:function(e,l){var a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return u.add(e,l,a)},watch:function(e){return u.watchLocale(e)},getLocale:function(){return u.getLocale()},setLocale:function(e){return u.setLocale(e)}}},l.isI18nStr=D,l.isString=void 0,l.normalizeLocale=y,l.parseI18nJson=function e(l,a,t){x||(x=new b);return A(l,(function(l,n){var u=l[n];$(u)?D(u,t)&&(l[n]=k(u,a,t)):e(u,a,t)})),l},l.resolveLocale=function(e){return function(l){return l?(l=y(l)||l,function(e){var l=[],a=e.split("-");while(a.length)l.push(a.join("-")),a.pop();return l}(l).find((function(l){return e.indexOf(l)>-1}))):l}};var u=n(a("34cf")),r=n(a("67ad")),o=n(a("0bdb")),i=n(a("3b2d")),v=function(e){return null!==e&&"object"===(0,i.default)(e)},s=["{","}"],b=function(){function e(){(0,r.default)(this,e),this._caches=Object.create(null)}return(0,o.default)(e,[{key:"interpolate",value:function(e,l){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(!l)return[e];var t=this._caches[e];return t||(t=d(e,a),this._caches[e]=t),p(t,l)}}]),e}();l.Formatter=b;var c=/^(?:\d)+/,f=/^(?:\w)+/;function d(e,l){var a=(0,u.default)(l,2),t=a[0],n=a[1],r=[],o=0,i="";while(o<e.length){var v=e[o++];if(v===t){i&&r.push({type:"text",value:i}),i="";var s="";v=e[o++];while(void 0!==v&&v!==n)s+=v,v=e[o++];var b=v===n,d=c.test(s)?"list":b&&f.test(s)?"named":"unknown";r.push({value:s,type:d})}else i+=v}return i&&r.push({type:"text",value:i}),r}function p(e,l){var a=[],t=0,n=Array.isArray(l)?"list":v(l)?"named":"unknown";if("unknown"===n)return a;while(t<e.length){var u=e[t];switch(u.type){case"text":a.push(u.value);break;case"list":a.push(l[parseInt(u.value,10)]);break;case"named":"named"===n&&a.push(l[u.value]);break;case"unknown":0;break}t++}return a}l.LOCALE_ZH_HANS="zh-Hans";l.LOCALE_ZH_HANT="zh-Hant";l.LOCALE_EN="en";l.LOCALE_FR="fr";l.LOCALE_ES="es";var h=Object.prototype.hasOwnProperty,m=function(e,l){return h.call(e,l)},g=new b;function y(e,l){if(e){if(e=e.trim().replace(/_/g,"-"),l&&l[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,l){return!!l.find((function(l){return-1!==e.indexOf(l)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var a=["en","fr","es"];l&&Object.keys(l).length>0&&(a=Object.keys(l));var t=function(e,l){return l.find((function(l){return 0===e.indexOf(l)}))}(e,a);return t||void 0}}var _=function(){function e(l){var a=l.locale,t=l.fallbackLocale,n=l.messages,u=l.watcher,o=l.formater;(0,r.default)(this,e),this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=o||g,this.messages=n||{},this.setLocale(a||"en"),u&&this.watchLocale(u)}return(0,o.default)(e,[{key:"setLocale",value:function(e){var l=this,a=this.locale;this.locale=y(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],a!==this.locale&&this.watchers.forEach((function(e){e(l.locale,a)}))}},{key:"getLocale",value:function(){return this.locale}},{key:"watchLocale",value:function(e){var l=this,a=this.watchers.push(e)-1;return function(){l.watchers.splice(a,1)}}},{key:"add",value:function(e,l){var a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],t=this.messages[e];t?a?Object.assign(t,l):Object.keys(l).forEach((function(e){m(t,e)||(t[e]=l[e])})):this.messages[e]=l}},{key:"f",value:function(e,l,a){return this.formater.interpolate(e,l,a).join("")}},{key:"t",value:function(e,l,a){var t=this.message;return"string"===typeof l?(l=y(l,this.messages),l&&(t=this.messages[l])):a=l,m(t,e)?this.formater.interpolate(t[e],a).join(""):(console.warn("Cannot translate the value of keypath ".concat(e,". Use the value of keypath as default.")),e)}}]),e}();function w(e,l){e.$watchLocale?e.$watchLocale((function(e){l.setLocale(e)})):e.$watch((function(){return e.$locale}),(function(e){l.setLocale(e)}))}function O(){return"undefined"!==typeof e&&e.getLocale?e.getLocale():"undefined"!==typeof t&&t.getLocale?t.getLocale():"en"}l.I18n=_;var x,$=function(e){return"string"===typeof e};function D(e,l){return e.indexOf(l[0])>-1}function k(e,l,a){return x.interpolate(e,l,a).join("")}function S(e,l,a){return A(e,(function(e,t){(function(e,l,a,t){var n=e[l];if($(n)){if(D(n,t)&&(e[l]=k(n,a[0].values,t),a.length>1)){var u=e[l+"Locales"]={};a.forEach((function(e){u[e.locale]=k(n,e.values,t)}))}}else S(n,a,t)})(e,t,l,a)})),e}function A(e,l){if(Array.isArray(e)){for(var a=0;a<e.length;a++)if(l(e,a))return!0}else if(v(e))for(var t in e)if(l(e,t))return!0;return!1}l.isString=$}).call(this,a("df3c")["default"],a("0ee4"))},d448:function(e,l,a){"use strict";(function(e){var t=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var n=t(a("a567")),u={data:function(){return{productInfo:{id:1,name:"精选美味商品1",price:29.9,image:"/static/logo_ruiji.png",description:"这是一款精心制作的美味商品，采用优质食材，口感丰富，营养均衡。"}}},onLoad:function(e){console.log("商品1页面加载"),e&&e.mid&&n.default.setMid(e.mid)},methods:{goBack:function(){e.navigateBack({delta:1})},goToOrder:function(){n.default.setMid("3"),n.default.navigateToWithRefresh("/pages/index/index")}}};l.default=u}).call(this,a("df3c")["default"])},d551:function(e,l,a){var t=a("3b2d")["default"],n=a("e6db");e.exports=function(e){var l=n(e,"string");return"symbol"==t(l)?l:l+""},e.exports.__esModule=!0,e.exports["default"]=e.exports},d585:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.baseUrl=void 0;l.baseUrl="https://xiaopaotuibackground.naishuo.top"},d73a:function(e,l,a){"use strict";var t=a("b0d3"),n=a.n(t);n.a},d968:function(e,l,a){"use strict";var t=a("6e29"),n=a.n(t);n.a},dd3e:function(e,l){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},df3c:function(e,l,a){"use strict";(function(e,t){var n=a("47a9");Object.defineProperty(l,"__esModule",{value:!0}),l.createApp=Ll,l.createComponent=ql,l.createPage=Fl,l.createPlugin=zl,l.createSubpackageApp=Gl,l.default=void 0;var u,r=n(a("34cf")),o=n(a("7ca3")),i=n(a("931d")),v=n(a("af34")),s=n(a("3b2d")),b=a("d3b4"),c=n(a("3240"));function f(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function d(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?f(Object(a),!0).forEach((function(l){(0,o.default)(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):f(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}var p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",h=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function m(){var l,a=e.getStorageSync("uni_id_token")||"",t=a.split(".");if(!a||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{l=JSON.parse(function(e){return decodeURIComponent(u(e).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))}(t[1]))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return l.tokenExpired=1e3*l.exp,delete l.exp,delete l.iat,l}u="function"!==typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!h.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var l;e+="==".slice(2-(3&e.length));for(var a,t,n="",u=0;u<e.length;)l=p.indexOf(e.charAt(u++))<<18|p.indexOf(e.charAt(u++))<<12|(a=p.indexOf(e.charAt(u++)))<<6|(t=p.indexOf(e.charAt(u++))),n+=64===a?String.fromCharCode(l>>16&255):64===t?String.fromCharCode(l>>16&255,l>>8&255):String.fromCharCode(l>>16&255,l>>8&255,255&l);return n}:atob;var g=Object.prototype.toString,y=Object.prototype.hasOwnProperty;function _(e){return"function"===typeof e}function w(e){return"string"===typeof e}function O(e){return"[object Object]"===g.call(e)}function x(e,l){return y.call(e,l)}function $(){}function D(e){var l=Object.create(null);return function(a){var t=l[a];return t||(l[a]=e(a))}}var k=/-(\w)/g,S=D((function(e){return e.replace(k,(function(e,l){return l?l.toUpperCase():""}))}));function A(e){var l={};return O(e)&&Object.keys(e).sort().forEach((function(a){l[a]=e[a]})),Object.keys(l)?l:e}var P=["invoke","success","fail","complete","returnValue"],j={},I={};function T(e,l){Object.keys(l).forEach((function(a){-1!==P.indexOf(a)&&_(l[a])&&(e[a]=function(e,l){var a=l?e?e.concat(l):Array.isArray(l)?l:[l]:e;return a?function(e){for(var l=[],a=0;a<e.length;a++)-1===l.indexOf(e[a])&&l.push(e[a]);return l}(a):a}(e[a],l[a]))}))}function E(e,l){e&&l&&Object.keys(l).forEach((function(a){-1!==P.indexOf(a)&&_(l[a])&&function(e,l){var a=e.indexOf(l);-1!==a&&e.splice(a,1)}(e[a],l[a])}))}function L(e,l){return function(a){return e(a,l)||a}}function C(e){return!!e&&("object"===(0,s.default)(e)||"function"===typeof e)&&"function"===typeof e.then}function M(e,l,a){for(var t=!1,n=0;n<e.length;n++){var u=e[n];if(t)t=Promise.resolve(L(u,a));else{var r=u(l,a);if(C(r)&&(t=Promise.resolve(r)),!1===r)return{then:function(){}}}}return t||{then:function(e){return e(l)}}}function N(e){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return["success","fail","complete"].forEach((function(a){if(Array.isArray(e[a])){var t=l[a];l[a]=function(n){M(e[a],n,l).then((function(e){return _(t)&&t(e)||e}))}}})),l}function B(e,l){var a=[];Array.isArray(j.returnValue)&&a.push.apply(a,(0,v.default)(j.returnValue));var t=I[e];return t&&Array.isArray(t.returnValue)&&a.push.apply(a,(0,v.default)(t.returnValue)),a.forEach((function(e){l=e(l)||l})),l}function U(e){var l=Object.create(null);Object.keys(j).forEach((function(e){"returnValue"!==e&&(l[e]=j[e].slice())}));var a=I[e];return a&&Object.keys(a).forEach((function(e){"returnValue"!==e&&(l[e]=(l[e]||[]).concat(a[e]))})),l}function V(e,l,a){for(var t=arguments.length,n=new Array(t>3?t-3:0),u=3;u<t;u++)n[u-3]=arguments[u];var r=U(e);if(r&&Object.keys(r).length){if(Array.isArray(r.invoke)){var o=M(r.invoke,a);return o.then((function(a){return l.apply(void 0,[N(U(e),a)].concat(n))}))}return l.apply(void 0,[N(r,a)].concat(n))}return l.apply(void 0,[a].concat(n))}var R={returnValue:function(e){return C(e)?new Promise((function(l,a){e.then((function(e){e?e[0]?a(e[0]):l(e[1]):l(e)}))})):e}},H=/^\$|__f__|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/,F=/^create|Manager$/,q=["createBLEConnection"],G=["createBLEConnection","createPushMessage"],z=/^on|^off/;function W(e){return F.test(e)&&-1===q.indexOf(e)}function J(e){return H.test(e)&&-1===G.indexOf(e)}function Y(e){return e.then((function(e){return[null,e]})).catch((function(e){return[e]}))}function K(e){return!(W(e)||J(e)||function(e){return z.test(e)&&"onPush"!==e}(e))}function Z(e,l){return K(e)&&_(l)?function(){for(var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length,n=new Array(t>1?t-1:0),u=1;u<t;u++)n[u-1]=arguments[u];return _(a.success)||_(a.fail)||_(a.complete)?B(e,V.apply(void 0,[e,l,Object.assign({},a)].concat(n))):B(e,Y(new Promise((function(t,u){V.apply(void 0,[e,l,Object.assign({},a,{success:t,fail:u})].concat(n))}))))}:l}Promise.prototype.finally||(Promise.prototype.finally=function(e){var l=this.constructor;return this.then((function(a){return l.resolve(e()).then((function(){return a}))}),(function(a){return l.resolve(e()).then((function(){throw a}))}))});var X=!1,Q=0,ee=0;function le(l,a){if(0===Q&&function(){var l,a,t,n="function"===typeof e.getWindowInfo&&e.getWindowInfo()?e.getWindowInfo():e.getSystemInfoSync(),u="function"===typeof e.getDeviceInfo&&e.getDeviceInfo()?e.getDeviceInfo():e.getSystemInfoSync();l=n.windowWidth,a=n.pixelRatio,t=u.platform,Q=l,ee=a,X="ios"===t}(),l=Number(l),0===l)return 0;var t=l/750*(a||Q);return t<0&&(t=-t),t=Math.floor(t+1e-4),0===t&&(t=1!==ee&&X?.5:1),l<0?-t:t}var ae,te={};function ne(){var l,a="function"===typeof e.getAppBaseInfo&&e.getAppBaseInfo()?e.getAppBaseInfo():e.getSystemInfoSync(),t=a&&a.language?a.language:"en";return l=oe(t)||"en",l}ae=ne(),function(){if(function(){return"undefined"!==typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length}()){var e=Object.keys(__uniConfig.locales);e.length&&e.forEach((function(e){var l=te[e],a=__uniConfig.locales[e];l?Object.assign(l,a):te[e]=a}))}}();var ue=(0,b.initVueI18n)(ae,{}),re=ue.t;ue.mixin={beforeCreate:function(){var e=this,l=ue.i18n.watchLocale((function(){e.$forceUpdate()}));this.$once("hook:beforeDestroy",(function(){l()}))},methods:{$$t:function(e,l){return re(e,l)}}},ue.setLocale,ue.getLocale;function oe(e,l){if(e){if(e=e.trim().replace(/_/g,"-"),l&&l[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,l){return!!l.find((function(l){return-1!==e.indexOf(l)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var a=function(e,l){return l.find((function(l){return 0===e.indexOf(l)}))}(e,["en","fr","es"]);return a||void 0}}function ie(){if(_(getApp)){var e=getApp({allowDefault:!0});if(e&&e.$vm)return e.$vm.$locale}return ne()}var ve=[];"undefined"!==typeof t&&(t.getLocale=ie);var se={promiseInterceptor:R},be=Object.freeze({__proto__:null,upx2px:le,rpx2px:le,getLocale:ie,setLocale:function(e){var l=!!_(getApp)&&getApp();if(!l)return!1;var a=l.$vm.$locale;return a!==e&&(l.$vm.$locale=e,ve.forEach((function(l){return l({locale:e})})),!0)},onLocaleChange:function(e){-1===ve.indexOf(e)&&ve.push(e)},addInterceptor:function(e,l){"string"===typeof e&&O(l)?T(I[e]||(I[e]={}),l):O(e)&&T(j,e)},removeInterceptor:function(e,l){"string"===typeof e?O(l)?E(I[e],l):delete I[e]:O(e)&&E(j,e)},interceptors:se});var ce,fe={name:function(e){return"back"===e.exists&&e.delta?"navigateBack":"redirectTo"},args:function(e){if("back"===e.exists&&e.url){var l=function(e){var l=getCurrentPages(),a=l.length;while(a--){var t=l[a];if(t.$page&&t.$page.fullPath===e)return a}return-1}(e.url);if(-1!==l){var a=getCurrentPages().length-1-l;a>0&&(e.delta=a)}}}},de={args:function(e){var l=parseInt(e.current);if(!isNaN(l)){var a=e.urls;if(Array.isArray(a)){var t=a.length;if(t)return l<0?l=0:l>=t&&(l=t-1),l>0?(e.current=a[l],e.urls=a.filter((function(e,t){return!(t<l)||e!==a[l]}))):e.current=a[0],{indicator:!1,loop:!1}}}}};function pe(l){ce=ce||e.getStorageSync("__DC_STAT_UUID"),ce||(ce=Date.now()+""+Math.floor(1e7*Math.random()),e.setStorage({key:"__DC_STAT_UUID",data:ce})),l.deviceId=ce}function he(e){if(e.safeArea){var l=e.safeArea;e.safeAreaInsets={top:l.top,left:l.left,right:e.windowWidth-l.right,bottom:e.screenHeight-l.bottom}}}function me(e,l){var a="",t="";switch(a=e.split(" ")[0]||l,t=e.split(" ")[1]||"",a=a.toLocaleLowerCase(),a){case"harmony":case"ohos":case"openharmony":a="harmonyos";break;case"iphone os":a="ios";break;case"mac":case"darwin":a="macos";break;case"windows_nt":a="windows";break}return{osName:a,osVersion:t}}function ge(e,l){for(var a=e.deviceType||"phone",t={ipad:"pad",windows:"pc",mac:"pc"},n=Object.keys(t),u=l.toLocaleLowerCase(),r=0;r<n.length;r++){var o=n[r];if(-1!==u.indexOf(o)){a=t[o];break}}return a}function ye(e){var l=e;return l&&(l=e.toLocaleLowerCase()),l}function _e(e){return ie?ie():e}function we(e){var l=e.hostName||"WeChat";return e.environment?l=e.environment:e.host&&e.host.env&&(l=e.host.env),l}var Oe={returnValue:function(e){pe(e),he(e),function(e){var l=e.brand,a=void 0===l?"":l,t=e.model,n=void 0===t?"":t,u=e.system,r=void 0===u?"":u,o=e.language,i=void 0===o?"":o,v=e.theme,s=e.version,b=e.platform,c=e.fontSizeSetting,f=e.SDKVersion,d=e.pixelRatio,p=e.deviceOrientation,h=me(r,b),m=h.osName,g=h.osVersion,y=s,_=ge(e,n),w=ye(a),O=we(e),x=p,$=d,D=f,k=(i||"").replace(/_/g,"-"),S={appId:"__UNI__6D069CB",appName:"xiaopaotui",appVersion:"1.0.0",appVersionCode:"100",appLanguage:_e(k),uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75",uniPlatform:"mp-weixin",deviceBrand:w,deviceModel:n,deviceType:_,devicePixelRatio:$,deviceOrientation:x,osName:m.toLocaleLowerCase(),osVersion:g,hostTheme:v,hostVersion:y,hostLanguage:k,hostName:O,hostSDKVersion:D,hostFontSizeSetting:c,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};Object.assign(e,S,{})}(e)}},xe={args:function(e){"object"===(0,s.default)(e)&&(e.alertText=e.title)}},$e={returnValue:function(e){var l=e,a=l.version,t=l.language,n=l.SDKVersion,u=l.theme,r=we(e),o=(t||"").replace("_","-");e=A(Object.assign(e,{appId:"__UNI__6D069CB",appName:"xiaopaotui",appVersion:"1.0.0",appVersionCode:"100",appLanguage:_e(o),hostVersion:a,hostLanguage:o,hostName:r,hostSDKVersion:n,hostTheme:u,isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75"}))}},De={returnValue:function(e){var l=e,a=l.brand,t=l.model,n=l.system,u=void 0===n?"":n,r=l.platform,o=void 0===r?"":r,i=ge(e,t),v=ye(a);pe(e);var s=me(u,o),b=s.osName,c=s.osVersion;e=A(Object.assign(e,{deviceType:i,deviceBrand:v,deviceModel:t,osName:b,osVersion:c}))}},ke={returnValue:function(e){he(e),e=A(Object.assign(e,{windowTop:0,windowBottom:0}))}},Se={redirectTo:fe,previewImage:de,getSystemInfo:Oe,getSystemInfoSync:Oe,showActionSheet:xe,getAppBaseInfo:$e,getDeviceInfo:De,getWindowInfo:ke,getAppAuthorizeSetting:{returnValue:function(e){var l=e.locationReducedAccuracy;e.locationAccuracy="unsupported",!0===l?e.locationAccuracy="reduced":!1===l&&(e.locationAccuracy="full")}},compressImage:{args:function(e){e.compressedHeight&&!e.compressHeight&&(e.compressHeight=e.compressedHeight),e.compressedWidth&&!e.compressWidth&&(e.compressWidth=e.compressedWidth)}}},Ae=["success","fail","cancel","complete"];function Pe(e,l,a){return function(t){return l(Ie(e,t,a))}}function je(e,l){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(O(l)){var u=!0===n?l:{};for(var r in _(a)&&(a=a(l,u)||{}),l)if(x(a,r)){var o=a[r];_(o)&&(o=o(l[r],l,u)),o?w(o)?u[o]=l[r]:O(o)&&(u[o.name?o.name:r]=o.value):console.warn("The '".concat(e,"' method of platform '微信小程序' does not support option '").concat(r,"'"))}else-1!==Ae.indexOf(r)?_(l[r])&&(u[r]=Pe(e,l[r],t)):n||(u[r]=l[r]);return u}return _(l)&&(l=Pe(e,l,t)),l}function Ie(e,l,a){var t=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return _(Se.returnValue)&&(l=Se.returnValue(e,l)),je(e,l,a,{},t)}function Te(l,a){if(x(Se,l)){var t=Se[l];return t?function(a,n){var u=t;_(t)&&(u=t(a)),a=je(l,a,u.args,u.returnValue);var r=[a];"undefined"!==typeof n&&r.push(n),_(u.name)?l=u.name(a):w(u.name)&&(l=u.name);var o=e[l].apply(e,r);return J(l)?Ie(l,o,u.returnValue,W(l)):o}:function(){console.error("Platform '微信小程序' does not support '".concat(l,"'."))}}return a}var Ee=Object.create(null);["onTabBarMidButtonTap","subscribePush","unsubscribePush","onPush","offPush","share"].forEach((function(e){Ee[e]=function(e){return function(l){var a=l.fail,t=l.complete,n={errMsg:"".concat(e,":fail method '").concat(e,"' not supported")};_(a)&&a(n),_(t)&&t(n)}}(e)}));var Le={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]};var Ce=Object.freeze({__proto__:null,getProvider:function(e){var l=e.service,a=e.success,t=e.fail,n=e.complete,u=!1;Le[l]?(u={errMsg:"getProvider:ok",service:l,provider:Le[l]},_(a)&&a(u)):(u={errMsg:"getProvider:fail service not found"},_(t)&&t(u)),_(n)&&n(u)}}),Me=function(){var e;return function(){return e||(e=new c.default),e}}();function Ne(e,l,a){return e[l].apply(e,a)}var Be,Ue,Ve,Re=Object.freeze({__proto__:null,$on:function(){return Ne(Me(),"$on",Array.prototype.slice.call(arguments))},$off:function(){return Ne(Me(),"$off",Array.prototype.slice.call(arguments))},$once:function(){return Ne(Me(),"$once",Array.prototype.slice.call(arguments))},$emit:function(){return Ne(Me(),"$emit",Array.prototype.slice.call(arguments))}});function He(e){return function(){try{return e.apply(e,arguments)}catch(l){console.error(l)}}}function Fe(e){try{return JSON.parse(e)}catch(l){}return e}var qe=[];function Ge(e,l){qe.forEach((function(a){a(e,l)})),qe.length=0}var ze=[];var We=e.getAppBaseInfo&&e.getAppBaseInfo();We||(We=e.getSystemInfoSync());var Je=We?We.host:null,Ye=Je&&"SAAASDK"===Je.env?e.miniapp.shareVideoMessage:e.shareVideoMessage,Ke=Object.freeze({__proto__:null,shareVideoMessage:Ye,getPushClientId:function(e){O(e)||(e={});var l=function(e){var l={};for(var a in e){var t=e[a];_(t)&&(l[a]=He(t),delete e[a])}return l}(e),a=l.success,t=l.fail,n=l.complete,u=_(a),r=_(t),o=_(n);Promise.resolve().then((function(){"undefined"===typeof Ve&&(Ve=!1,Be="",Ue="uniPush is not enabled"),qe.push((function(e,l){var i;e?(i={errMsg:"getPushClientId:ok",cid:e},u&&a(i)):(i={errMsg:"getPushClientId:fail"+(l?" "+l:"")},r&&t(i)),o&&n(i)})),"undefined"!==typeof Be&&Ge(Be,Ue)}))},onPushMessage:function(e){-1===ze.indexOf(e)&&ze.push(e)},offPushMessage:function(e){if(e){var l=ze.indexOf(e);l>-1&&ze.splice(l,1)}else ze.length=0},invokePushCallback:function(e){if("enabled"===e.type)Ve=!0;else if("clientId"===e.type)Be=e.cid,Ue=e.errMsg,Ge(Be,e.errMsg);else if("pushMsg"===e.type)for(var l={type:"receive",data:Fe(e.message)},a=0;a<ze.length;a++){var t=ze[a];if(t(l),l.stopped)break}else"click"===e.type&&ze.forEach((function(l){l({type:"click",data:Fe(e.message)})}))},__f__:function(e){for(var l=arguments.length,a=new Array(l>1?l-1:0),t=1;t<l;t++)a[t-1]=arguments[t];console[e].apply(console,a)}}),Ze=["__route__","__wxExparserNodeId__","__wxWebviewId__"];function Xe(e){return Behavior(e)}function Qe(){return!!this.route}function el(e){this.triggerEvent("__l",e)}function ll(e){var l=e.$scope,a={};Object.defineProperty(e,"$refs",{get:function(){var e={};(function e(l,a,t){var n=l.selectAllComponents(a)||[];n.forEach((function(l){var n=l.dataset.ref;t[n]=l.$vm||nl(l),"scoped"===l.dataset.vueGeneric&&l.selectAllComponents(".scoped-ref").forEach((function(l){e(l,a,t)}))}))})(l,".vue-ref",e);var t=l.selectAllComponents(".vue-ref-in-for")||[];return t.forEach((function(l){var a=l.dataset.ref;e[a]||(e[a]=[]),e[a].push(l.$vm||nl(l))})),function(e,l){var a=(0,i.default)(Set,(0,v.default)(Object.keys(e))),t=Object.keys(l);return t.forEach((function(t){var n=e[t],u=l[t];Array.isArray(n)&&Array.isArray(u)&&n.length===u.length&&u.every((function(e){return n.includes(e)}))||(e[t]=u,a.delete(t))})),a.forEach((function(l){delete e[l]})),e}(a,e)}})}function al(e){var l,a=e.detail||e.value,t=a.vuePid,n=a.vueOptions;t&&(l=function e(l,a){for(var t,n=l.$children,u=n.length-1;u>=0;u--){var r=n[u];if(r.$scope._$vueId===a)return r}for(var o=n.length-1;o>=0;o--)if(t=e(n[o],a),t)return t}(this.$vm,t)),l||(l=this.$vm),n.parent=l}function tl(e){return Object.defineProperty(e,"__v_isMPComponent",{configurable:!0,enumerable:!1,value:!0}),e}function nl(e){return function(e){return null!==e&&"object"===(0,s.default)(e)}(e)&&Object.isExtensible(e)&&Object.defineProperty(e,"__ob__",{configurable:!0,enumerable:!1,value:(0,o.default)({},"__v_skip",!0)}),e}var ul=/_(.*)_worklet_factory_/;var rl=Page,ol=Component,il=/:/g,vl=D((function(e){return S(e.replace(il,"-"))}));function sl(e){var l=e.triggerEvent,a=function(e){for(var a=arguments.length,t=new Array(a>1?a-1:0),n=1;n<a;n++)t[n-1]=arguments[n];if(this.$vm||this.dataset&&this.dataset.comType)e=vl(e);else{var u=vl(e);u!==e&&l.apply(this,[u].concat(t))}return l.apply(this,[e].concat(t))};try{e.triggerEvent=a}catch(t){e._triggerEvent=a}}function bl(e,l,a){var t=l[e];l[e]=function(){if(tl(this),sl(this),t){for(var e=arguments.length,l=new Array(e),a=0;a<e;a++)l[a]=arguments[a];return t.apply(this,l)}}}rl.__$wrappered||(rl.__$wrappered=!0,Page=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return bl("onLoad",e),rl(e)},Page.after=rl.after,Component=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return bl("created",e),ol(e)});function cl(e,l,a){l.forEach((function(l){(function e(l,a){if(!a)return!0;if(c.default.options&&Array.isArray(c.default.options[l]))return!0;if(a=a.default||a,_(a))return!!_(a.extendOptions[l])||!!(a.super&&a.super.options&&Array.isArray(a.super.options[l]));if(_(a[l])||Array.isArray(a[l]))return!0;var t=a.mixins;return Array.isArray(t)?!!t.find((function(a){return e(l,a)})):void 0})(l,a)&&(e[l]=function(e){return this.$vm&&this.$vm.__call_hook(l,e)})}))}function fl(e,l){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];dl(l).forEach((function(l){return pl(e,l,a)}))}function dl(e){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&Object.keys(e).forEach((function(a){0===a.indexOf("on")&&_(e[a])&&l.push(a)})),l}function pl(e,l,a){-1!==a.indexOf(l)||x(e,l)||(e[l]=function(e){return this.$vm&&this.$vm.__call_hook(l,e)})}function hl(e,l){var a;return l=l.default||l,a=_(l)?l:e.extend(l),l=a.options,[a,l]}function ml(e,l){if(Array.isArray(l)&&l.length){var a=Object.create(null);l.forEach((function(e){a[e]=!0})),e.$scopedSlots=e.$slots=a}}function gl(e,l){e=(e||"").split(",");var a=e.length;1===a?l._$vueId=e[0]:2===a&&(l._$vueId=e[0],l._$vuePid=e[1])}function yl(e,l){var a=e.data||{},t=e.methods||{};if("function"===typeof a)try{a=a.call(l)}catch(n){Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"xiaopaotui",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.warn("根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。",a)}else try{a=JSON.parse(JSON.stringify(a))}catch(n){}return O(a)||(a={}),Object.keys(t).forEach((function(e){-1!==l.__lifecycle_hooks__.indexOf(e)||x(a,e)||(a[e]=t[e])})),a}var _l=[String,Number,Boolean,Object,Array,null];function wl(e){return function(l,a){this.$vm&&(this.$vm[e]=l)}}function Ol(e,l){var a=e.behaviors,t=e.extends,n=e.mixins,u=e.props;u||(e.props=u=[]);var r=[];return Array.isArray(a)&&a.forEach((function(e){r.push(e.replace("uni://","wx".concat("://"))),"uni://form-field"===e&&(Array.isArray(u)?(u.push("name"),u.push("value")):(u.name={type:String,default:""},u.value={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),O(t)&&t.props&&r.push(l({properties:$l(t.props,!0)})),Array.isArray(n)&&n.forEach((function(e){O(e)&&e.props&&r.push(l({properties:$l(e.props,!0)}))})),r}function xl(e,l,a,t){return Array.isArray(l)&&1===l.length?l[0]:l}function $l(e){var l=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>3?arguments[3]:void 0,t={};return l||(t.vueId={type:String,value:""},a.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""}),t.scopedSlotsCompiler={type:String,value:""},t.vueSlots={type:null,value:[],observer:function(e,l){var a=Object.create(null);e.forEach((function(e){a[e]=!0})),this.setData({$slots:a})}}),Array.isArray(e)?e.forEach((function(e){t[e]={type:null,observer:wl(e)}})):O(e)&&Object.keys(e).forEach((function(l){var a=e[l];if(O(a)){var n=a.default;_(n)&&(n=n()),a.type=xl(0,a.type),t[l]={type:-1!==_l.indexOf(a.type)?a.type:null,value:n,observer:wl(l)}}else{var u=xl(0,a);t[l]={type:-1!==_l.indexOf(u)?u:null,observer:wl(l)}}})),t}function Dl(e,l,a,t){var n={};return Array.isArray(l)&&l.length&&l.forEach((function(l,u){"string"===typeof l?l?"$event"===l?n["$"+u]=a:"arguments"===l?n["$"+u]=a.detail&&a.detail.__args__||t:0===l.indexOf("$event.")?n["$"+u]=e.__get_value(l.replace("$event.",""),a):n["$"+u]=e.__get_value(l):n["$"+u]=e:n["$"+u]=function(e,l){var a=e;return l.forEach((function(l){var t=l[0],n=l[2];if(t||"undefined"!==typeof n){var u,r=l[1],o=l[3];Number.isInteger(t)?u=t:t?"string"===typeof t&&t&&(u=0===t.indexOf("#s#")?t.substr(3):e.__get_value(t,a)):u=a,Number.isInteger(u)?a=n:r?Array.isArray(u)?a=u.find((function(l){return e.__get_value(r,l)===n})):O(u)?a=Object.keys(u).find((function(l){return e.__get_value(r,u[l])===n})):console.error("v-for 暂不支持循环数据：",u):a=u[n],o&&(a=e.__get_value(o,a))}})),a}(e,l)})),n}function kl(e){for(var l={},a=1;a<e.length;a++){var t=e[a];l[t[0]]=t[1]}return l}function Sl(e,l){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],n=arguments.length>4?arguments[4]:void 0,u=arguments.length>5?arguments[5]:void 0,r=!1,o=O(l.detail)&&l.detail.__args__||[l.detail];if(n&&(r=l.currentTarget&&l.currentTarget.dataset&&"wx"===l.currentTarget.dataset.comType,!a.length))return r?[l]:o;var i=Dl(e,t,l,o),v=[];return a.forEach((function(e){"$event"===e?"__set_model"!==u||n?n&&!r?v.push(o[0]):v.push(l):v.push(l.target.value):Array.isArray(e)&&"o"===e[0]?v.push(kl(e)):"string"===typeof e&&x(i,e)?v.push(i[e]):v.push(e)})),v}function Al(e){var l=this;e=function(e){try{e.mp=JSON.parse(JSON.stringify(e))}catch(l){}return e.stopPropagation=$,e.preventDefault=$,e.target=e.target||{},x(e,"detail")||(e.detail={}),x(e,"markerId")&&(e.detail="object"===(0,s.default)(e.detail)?e.detail:{},e.detail.markerId=e.markerId),O(e.detail)&&(e.target=Object.assign({},e.target,e.detail)),e}(e);var a=(e.currentTarget||e.target).dataset;if(!a)return console.warn("事件信息不存在");var t=a.eventOpts||a["event-opts"];if(!t)return console.warn("事件信息不存在");var n=e.type,u=[];return t.forEach((function(a){var t=a[0],r=a[1],o="^"===t.charAt(0);t=o?t.slice(1):t;var i="~"===t.charAt(0);t=i?t.slice(1):t,r&&function(e,l){return e===l||"regionchange"===l&&("begin"===e||"end"===e)}(n,t)&&r.forEach((function(a){var t=a[0];if(t){var n=l.$vm;if(n.$options.generic&&(n=function(e){var l=e.$parent;while(l&&l.$parent&&(l.$options.generic||l.$parent.$options.generic||l.$scope._$vuePid))l=l.$parent;return l&&l.$parent}(n)||n),"$emit"===t)return void n.$emit.apply(n,Sl(l.$vm,e,a[1],a[2],o,t));var r=n[t];if(!_(r)){var v="page"===l.$vm.mpType?"Page":"Component",s=l.route||l.is;throw new Error("".concat(v,' "').concat(s,'" does not have a method "').concat(t,'"'))}if(i){if(r.once)return;r.once=!0}var b=Sl(l.$vm,e,a[1],a[2],o,t);b=Array.isArray(b)?b:[],/=\s*\S+\.eventParams\s*\|\|\s*\S+\[['"]event-params['"]\]/.test(r.toString())&&(b=b.concat([,,,,,,,,,,e])),u.push(r.apply(n,b))}}))})),"input"===n&&1===u.length&&"undefined"!==typeof u[0]?u[0]:void 0}var Pl={};var jl=["onShow","onHide","onError","onPageNotFound","onThemeChange","onUnhandledRejection"];function Il(){c.default.prototype.getOpenerEventChannel=function(){return this.$scope.getOpenerEventChannel()};var e=c.default.prototype.__call_hook;c.default.prototype.__call_hook=function(l,a){return"onLoad"===l&&a&&a.__id__&&(this.__eventChannel__=function(e){var l=Pl[e];return delete Pl[e],l}(a.__id__),delete a.__id__),e.call(this,l,a)}}function Tl(l,a){var t=a.mocks,n=a.initRefs;Il(),function(){var e={},l={};function a(e){var l=this.$options.propsData.vueId;if(l){var a=l.split(",")[0];e(a)}}c.default.prototype.$hasSSP=function(a){var t=e[a];return t||(l[a]=this,this.$on("hook:destroyed",(function(){delete l[a]}))),t},c.default.prototype.$getSSP=function(l,a,t){var n=e[l];if(n){var u=n[a]||[];return t?u:u[0]}},c.default.prototype.$setSSP=function(l,t){var n=0;return a.call(this,(function(a){var u=e[a],r=u[l]=u[l]||[];r.push(t),n=r.length-1})),n},c.default.prototype.$initSSP=function(){a.call(this,(function(l){e[l]={}}))},c.default.prototype.$callSSP=function(){a.call(this,(function(e){l[e]&&l[e].$forceUpdate()}))},c.default.mixin({destroyed:function(){var a=this.$options.propsData,t=a&&a.vueId;t&&(delete e[t],delete l[t])}})}(),l.$options.store&&(c.default.prototype.$store=l.$options.store),function(e){e.prototype.uniIDHasRole=function(e){var l=m(),a=l.role;return a.indexOf(e)>-1},e.prototype.uniIDHasPermission=function(e){var l=m(),a=l.permission;return this.uniIDHasRole("admin")||a.indexOf(e)>-1},e.prototype.uniIDTokenValid=function(){var e=m(),l=e.tokenExpired;return l>Date.now()}}(c.default),c.default.prototype.mpHost="mp-weixin",c.default.mixin({beforeCreate:function(){if(this.$options.mpType){if(this.mpType=this.$options.mpType,this.$mp=(0,o.default)({data:{}},this.mpType,this.$options.mpInstance),this.$scope=this.$options.mpInstance,delete this.$options.mpType,delete this.$options.mpInstance,"page"===this.mpType&&"function"===typeof getApp){var e=getApp();e.$vm&&e.$vm.$i18n&&(this._i18n=e.$vm.$i18n)}"app"!==this.mpType&&(n(this),function(e,l){var a=e.$mp[e.mpType];l.forEach((function(l){x(a,l)&&(e[l]=a[l])}))}(this,t))}}});var u={onLaunch:function(a){this.$vm||(e.canIUse&&!e.canIUse("nextTick")&&console.error("当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上"),this.$vm=l,this.$vm.$mp={app:this},this.$vm.$scope=this,this.$vm.globalData=this.globalData,this.$vm._isMounted=!0,this.$vm.__call_hook("mounted",a),this.$vm.__call_hook("onLaunch",a))}};u.globalData=l.$options.globalData||{};var r=l.$options.methods;return r&&Object.keys(r).forEach((function(e){u[e]=r[e]})),function(e,l,a){var t=e.observable({locale:a||ue.getLocale()}),n=[];l.$watchLocale=function(e){n.push(e)},Object.defineProperty(l,"$locale",{get:function(){return t.locale},set:function(e){t.locale=e,n.forEach((function(l){return l(e)}))}})}(c.default,l,function(){var l,a=e.getAppBaseInfo(),t=a&&a.language?a.language:"en";return l=oe(t)||"en",l}()),cl(u,jl),fl(u,l.$options),u}function El(e){return Tl(e,{mocks:Ze,initRefs:ll})}function Ll(e){return App(El(e)),e}var Cl=/[!'()*]/g,Ml=function(e){return"%"+e.charCodeAt(0).toString(16)},Nl=/%2C/g,Bl=function(e){return encodeURIComponent(e).replace(Cl,Ml).replace(Nl,",")};function Ul(e){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Bl,a=e?Object.keys(e).map((function(a){var t=e[a];if(void 0===t)return"";if(null===t)return l(a);if(Array.isArray(t)){var n=[];return t.forEach((function(e){void 0!==e&&(null===e?n.push(l(a)):n.push(l(a)+"="+l(e)))})),n.join("&")}return l(a)+"="+l(t)})).filter((function(e){return e.length>0})).join("&"):null;return a?"?".concat(a):""}function Vl(e,l){return function(e){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=l.isPage,t=l.initRelation,n=arguments.length>2?arguments[2]:void 0,u=hl(c.default,e),o=(0,r.default)(u,2),i=o[0],v=o[1],s=d({multipleSlots:!0,addGlobalClass:!0},v.options||{});v["mp-weixin"]&&v["mp-weixin"].options&&Object.assign(s,v["mp-weixin"].options);var b={options:s,data:yl(v,c.default.prototype),behaviors:Ol(v,Xe),properties:$l(v.props,!1,v.__file,s),lifetimes:{attached:function(){var e=this.properties,l={mpType:a.call(this)?"page":"component",mpInstance:this,propsData:e};gl(e.vueId,this),t.call(this,{vuePid:this._$vuePid,vueOptions:l}),this.$vm=new i(l),ml(this.$vm,e.vueSlots),this.$vm.$mount()},ready:function(){this.$vm&&(this.$vm._isMounted=!0,this.$vm.__call_hook("mounted"),this.$vm.__call_hook("onReady"))},detached:function(){this.$vm&&this.$vm.$destroy()}},pageLifetimes:{show:function(e){this.$vm&&this.$vm.__call_hook("onPageShow",e)},hide:function(){this.$vm&&this.$vm.__call_hook("onPageHide")},resize:function(e){this.$vm&&this.$vm.__call_hook("onPageResize",e)}},methods:{__l:al,__e:Al}};return v.externalClasses&&(b.externalClasses=v.externalClasses),Array.isArray(v.wxsCallMethods)&&v.wxsCallMethods.forEach((function(e){b.methods[e]=function(l){return this.$vm[e](l)}})),n?[b,v,i]:a?b:[b,i]}(e,{isPage:Qe,initRelation:el},l)}var Rl=["onShow","onHide","onUnload"];function Hl(e){var l=Vl(e,!0),a=(0,r.default)(l,2),t=a[0],n=a[1];return cl(t.methods,Rl,n),t.methods.onLoad=function(e){this.options=e;var l=Object.assign({},e);delete l.__id__,this.$page={fullPath:"/"+(this.route||this.is)+Ul(l)},this.$vm.$mp.query=e,this.$vm.__call_hook("onLoad",e)},fl(t.methods,e,["onReady"]),function(e,l){l&&Object.keys(l).forEach((function(a){var t=a.match(ul);if(t){var n=t[1];e[a]=l[a],e[n]=l[n]}}))}(t.methods,n.methods),t}function Fl(e){return Component(function(e){return Hl(e)}(e))}function ql(e){return Component(Vl(e))}function Gl(l){var a=El(l),t=getApp({allowDefault:!0});l.$scope=t;var n=t.globalData;if(n&&Object.keys(a.globalData).forEach((function(e){x(n,e)||(n[e]=a.globalData[e])})),Object.keys(a).forEach((function(e){x(t,e)||(t[e]=a[e])})),_(a.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,a=new Array(e),t=0;t<e;t++)a[t]=arguments[t];l.__call_hook("onShow",a)})),_(a.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,a=new Array(e),t=0;t<e;t++)a[t]=arguments[t];l.__call_hook("onHide",a)})),_(a.onLaunch)){var u=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();l.__call_hook("onLaunch",u)}return l}function zl(l){var a=El(l);if(_(a.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,a=new Array(e),t=0;t<e;t++)a[t]=arguments[t];l.__call_hook("onShow",a)})),_(a.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,a=new Array(e),t=0;t<e;t++)a[t]=arguments[t];l.__call_hook("onHide",a)})),_(a.onLaunch)){var t=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();l.__call_hook("onLaunch",t)}return l}Rl.push.apply(Rl,["onPullDownRefresh","onReachBottom","onAddToFavorites","onShareTimeline","onShareAppMessage","onPageScroll","onResize","onTabItemTap"]),["vibrate","preloadPage","unPreloadPage","loadSubPackage"].forEach((function(e){Se[e]=!1})),[].forEach((function(l){var a=Se[l]&&Se[l].name?Se[l].name:l;e.canIUse(a)||(Se[l]=!1)}));var Wl={};"undefined"!==typeof Proxy?Wl=new Proxy({},{get:function(l,a){return x(l,a)?l[a]:be[a]?be[a]:Ke[a]?Z(a,Ke[a]):Ce[a]?Z(a,Ce[a]):Ee[a]?Z(a,Ee[a]):Re[a]?Re[a]:Z(a,Te(a,e[a]))},set:function(e,l,a){return e[l]=a,!0}}):(Object.keys(be).forEach((function(e){Wl[e]=be[e]})),Object.keys(Ee).forEach((function(e){Wl[e]=Z(e,Ee[e])})),Object.keys(Ce).forEach((function(e){Wl[e]=Z(e,Ce[e])})),Object.keys(Re).forEach((function(e){Wl[e]=Re[e]})),Object.keys(Ke).forEach((function(e){Wl[e]=Z(e,Ke[e])})),Object.keys(e).forEach((function(l){(x(e,l)||x(Se,l))&&(Wl[l]=Z(l,Te(l,e[l])))}))),e.createApp=Ll,e.createPage=Fl,e.createComponent=ql,e.createSubpackageApp=Gl,e.createPlugin=zl;var Jl=Wl,Yl=Jl;l.default=Yl}).call(this,a("3223")["default"],a("0ee4"))},e6db:function(e,l,a){var t=a("3b2d")["default"];e.exports=function(e,l){if("object"!=t(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,l||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},e79c:function(e,l,a){"use strict";a.r(l);var t=a("d448"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},ed45:function(e,l){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},ee10:function(e,l){function a(e,l,a,t,n,u,r){try{var o=e[u](r),i=o.value}catch(v){return void a(v)}o.done?l(i):Promise.resolve(i).then(t,n)}e.exports=function(e){return function(){var l=this,t=arguments;return new Promise((function(n,u){var r=e.apply(l,t);function o(e){a(r,n,u,o,i,"next",e)}function i(e){a(r,n,u,o,i,"throw",e)}o(void 0)}))}},e.exports.__esModule=!0,e.exports["default"]=e.exports},f209:function(e,l,a){"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var t=a("7281"),n=(0,t.createProductPageConfig)(4,"精选美味商品4",38.9);l.default=n},f47a:function(e,l,a){"use strict";a.r(l);var t=a("9908"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},f6d2:function(e,l,a){"use strict";var t=a("4e9e"),n=a.n(t);n.a},f80e:function(e,l,a){},f82b:function(e,l,a){"use strict";a.r(l);var t=a("c3dc"),n=a.n(t);for(var u in t)["default"].indexOf(u)<0&&function(e){a.d(l,e,(function(){return t[e]}))}(u);l["default"]=n.a},f91b:function(e,l,a){"use strict";var t=a("54e9"),n=a.n(t);n.a}}]);