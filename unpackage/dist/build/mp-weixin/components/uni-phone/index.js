(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-phone/index"],{"25ed":function(n,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){return u}));var u={uniPopup:function(){return t.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(t.bind(null,"d84b"))}},o=function(){var n=this.$createElement;this._self._c},i=[]},"586a":function(n,e,t){"use strict";t.r(e);var u=t("25ed"),o=t("8e53");for(var i in o)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(i);t("f6d2");var c=t("828b"),r=Object(c["a"])(o["default"],u["b"],u["c"],!1,null,"26c6f860",null,!1,u["a"],void 0);e["default"]=r.exports},"8e53":function(n,e,t){"use strict";t.r(e);var u=t("f5e9"),o=t.n(u);for(var i in u)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(i);e["default"]=o.a},f5e9:function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var u=t("9ec3"),o={props:{phoneData:{type:String,default:""}},methods:{call:function(){(0,u.call)(this.phoneData)},closePopup:function(){this.$emit("closePopup")}}};e.default=o}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-phone/index-create-component',
    {
        'components/uni-phone/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("586a"))
        })
    },
    [['components/uni-phone/index-create-component']]
]);
