<view class="{{['uni-easyinput',(msg)?'uni-easyinput-error':'']}}" style="{{(boxStyle)}}"><view class="{{['uni-easyinput__content',inputContentClass]}}" style="{{(inputContentStyle)}}"><block wx:if="{{prefixIcon}}"><uni-icons class="content-clear-icon" vue-id="d0425c22-1" type="{{prefixIcon}}" color="#c0c4cc" size="22" data-event-opts="{{[['^click',[['onClickIcon',['prefix']]]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block><block wx:if="{{type==='textarea'}}"><textarea class="{{['uni-easyinput__content-textarea',(inputBorder)?'input-padding':'']}}" name="{{name}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled}}" placeholder-class="uni-easyinput__placeholder-class" maxlength="{{inputMaxlength}}" focus="{{focused}}" autoHeight="{{autoHeight}}" cursor-spacing="{{cursorSpacing}}" data-event-opts="{{[['input',[['onInput',['$event']]]],['blur',[['_Blur',['$event']]]],['focus',[['_Focus',['$event']]]],['confirm',[['onConfirm',['$event']]]],['keyboardheightchange',[['onkeyboardheightchange',['$event']]]]]}}" value="{{val}}" bindinput="__e" bindblur="__e" bindfocus="__e" bindconfirm="__e" bindkeyboardheightchange="__e"></textarea></block><block wx:else><input class="uni-easyinput__content-input" style="{{(inputStyle)}}" type="{{type==='password'?'text':type}}" name="{{name}}" password="{{!showPassword&&type==='password'}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" placeholder-class="uni-easyinput__placeholder-class" disabled="{{disabled}}" maxlength="{{inputMaxlength}}" focus="{{focused}}" confirmType="{{confirmType}}" cursor-spacing="{{cursorSpacing}}" data-event-opts="{{[['focus',[['_Focus',['$event']]]],['blur',[['_Blur',['$event']]]],['input',[['onInput',['$event']]]],['confirm',[['onConfirm',['$event']]]],['keyboardheightchange',[['onkeyboardheightchange',['$event']]]]]}}" value="{{val}}" bindfocus="__e" bindblur="__e" bindinput="__e" bindconfirm="__e" bindkeyboardheightchange="__e"/></block><block wx:if="{{type==='password'&&passwordIcon}}"><block wx:if="{{isVal}}"><uni-icons class="{{['content-clear-icon',(type==='textarea')?'is-textarea-icon':'']}}" vue-id="d0425c22-2" type="{{showPassword?'eye-slash-filled':'eye-filled'}}" size="{{22}}" color="{{focusShow?primaryColor:'#c0c4cc'}}" data-event-opts="{{[['^click',[['onEyes']]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block></block><block wx:else><block wx:if="{{suffixIcon}}"><block wx:if="{{suffixIcon}}"><uni-icons class="content-clear-icon" vue-id="d0425c22-3" type="{{suffixIcon}}" color="#c0c4cc" size="22" data-event-opts="{{[['^click',[['onClickIcon',['suffix']]]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block></block><block wx:else><block wx:if="{{clearable&&isVal&&!disabled&&type!=='textarea'}}"><uni-icons class="{{['content-clear-icon',(type==='textarea')?'is-textarea-icon':'']}}" vue-id="d0425c22-4" type="clear" size="{{clearSize}}" color="{{msg?'#dd524d':focusShow?primaryColor:'#c0c4cc'}}" data-event-opts="{{[['^click',[['onClear']]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block></block></block><slot name="right"></slot></view></view>