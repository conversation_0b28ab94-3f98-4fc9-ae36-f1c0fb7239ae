<view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="cart_pop data-v-b2da6f24" catchtap="__e"><view class="top_title data-v-b2da6f24"><view class="tit data-v-b2da6f24">购物车</view><view data-event-opts="{{[['tap',[['clearCardOrder']]]]}}" class="clear data-v-b2da6f24" catchtap="__e"><image class="clear_icon data-v-b2da6f24" src="../../../static/clear.png" mode></image><text class="clear-des data-v-b2da6f24">清空</text></view></view><scroll-view class="card_order_list data-v-b2da6f24" scroll-y="true" scroll-top="40rpx"><block wx:for="{{orderAndUserInfo}}" wx:for-item="item" wx:for-index="ind" wx:key="ind"><view class="type_item_cont data-v-b2da6f24"><block wx:for="{{item.dishList}}" wx:for-item="obj" wx:for-index="index" wx:key="index"><view class="type_item data-v-b2da6f24"><view class="dish_img data-v-b2da6f24"><image class="dish_img_url data-v-b2da6f24" mode="aspectFill" src="{{obj.image}}"></image></view><view class="dish_info data-v-b2da6f24"><view class="dish_name data-v-b2da6f24">{{obj.name}}</view><block wx:if="{{obj.dishFlavor}}"><view class="dish_dishFlavor data-v-b2da6f24">{{obj.dishFlavor}}</view></block><view class="dish_price data-v-b2da6f24"><text class="ico data-v-b2da6f24">￥</text>{{''+obj.amount+''}}</view><view class="dish_active data-v-b2da6f24"><block wx:if="{{obj.number&&obj.number>0}}"><image class="dish_red data-v-b2da6f24" src="../../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','购物车'],[[['orderAndUserInfo','',ind],['dishList','',index]]]]]]]}}" catchtap="__e"></image></block><block wx:if="{{obj.number&&obj.number>0}}"><text class="dish_number data-v-b2da6f24">{{obj.number}}</text></block><image class="dish_add data-v-b2da6f24" src="../../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','购物车'],[[['orderAndUserInfo','',ind],['dishList','',index]]]]]]]}}" catchtap="__e"></image></view></view></view></block></view></block><view class="seize_seat data-v-b2da6f24"></view></scroll-view></view>