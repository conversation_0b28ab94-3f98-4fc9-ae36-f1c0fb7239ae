@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.address.data-v-db675620 {
  width: 750rpx;
}
.address .address_content.data-v-db675620 {
  margin: 0 20rpx;
  padding-bottom: 20rpx;
  height: 100%;
  overflow-y: auto;
}
.address .address_content .address_liests.data-v-db675620 {
  width: 100%;
  height: 256rpx;
  opacity: 1;
  background: #ffffff;
  border-radius: 12rpx;
  display: flex;
  display: flex;
  flex-direction: column;
  margin-top: 20rpx;
  padding: 0 28rpx 0 12rpx;
  box-sizing: border-box;
}
.address .address_content .address_liests .list_item_top.data-v-db675620 {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
}
.address .address_content .address_liests .list_item_top .item_left.data-v-db675620 {
  flex: 1;
  overflow: hidden;
  margin-left: 12rpx;
}
.address .address_content .address_liests .list_item_top .item_left .details.data-v-db675620 {
  margin-top: 42rpx;
  display: flex;
  height: 40rpx;
  line-height: 40rpx;
}
.address .address_content .address_liests .list_item_top .item_left .details .address_word.data-v-db675620 {
  flex: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.address .address_content .address_liests .list_item_top .item_left .details .active.data-v-db675620 {
  background: #fef8e7;
}
.address .address_content .address_liests .list_item_top .item_left .sale.data-v-db675620 {
  margin-top: 20rpx;
}
.address .address_content .address_liests .list_item_top .item_left .sale .name.data-v-db675620,
.address .address_content .address_liests .list_item_top .item_left .sale .num.data-v-db675620 {
  height: 40rpx;
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #999999;
  line-height: 40rpx;
  letter-spacing: 0px;
}
.address .address_content .address_liests .list_item_top .item_left .sale .num.data-v-db675620 {
  margin-left: 20rpx;
}
.address .address_content .address_liests .list_item_top .item_right.data-v-db675620 {
  width: 100rpx;
  height: 100rpx;
  line-height: 1;
  text-align: right;
  padding-right: 18rpx;
}
.address .address_content .address_liests .list_item_top .item_right .edit.data-v-db675620 {
  width: 32rpx;
  height: 32rpx;
  padding: 24rpx;
  margin-top: 50rpx;
  margin-left: 20rpx;
}
.address .address_content .address_liests .list_item_bottom.data-v-db675620 {
  height: 80rpx;
  line-height: 80rpx;
  border-top: 1px solid #efefef;
}
.address .address_content .address_liests .list_item_bottom .radio.data-v-db675620 {
  margin-left: 8rpx;
  opacity: 1;
  font-size: 26rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
}
.address .address_content .address_liests .list_item_bottom .radio .item_radio.data-v-db675620 {
  -webkit-transform: scale(0.7);
          transform: scale(0.7);
}
.address .no_address.data-v-db675620 {
  margin: 0 auto;
  height: 50rpx;
}
.address .no_address .no_word.data-v-db675620 {
  display: block;
  text-align: center;
  font-size: 32rpx;
}
.address .add_address.data-v-db675620 {
  position: fixed;
  bottom: 40rpx;
  left: 20rpx;
  right: 20rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.address .add_address .add_btn.data-v-db675620 {
  width: 100%;
  height: 86rpx;
  line-height: 86rpx;
  border-radius: 8rpx;
  background: #ffc200;
  border: 1px solid #ffc200;
  opacity: 1;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 600;
  text-align: center;
  color: #333333;
  letter-spacing: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.address .add_address .add_btn .add-icon.data-v-db675620 {
  font-size: 32rpx;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}
.address .add_address .add_btn .img_btn.data-v-db675620 {
  width: 44rpx;
  height: 44rpx;
  vertical-align: middle;
  margin-bottom: 8rpx;
}
.customer-box.data-v-db675620 {
  height: 100vh;
}

