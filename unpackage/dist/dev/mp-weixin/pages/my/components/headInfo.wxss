@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my_info.data-v-7f94e65d {
  height: 172rpx;
  width: 750rpx;
  background-color: #ffc200;
  display: flex;
}
.my_info .head.data-v-7f94e65d {
  width: 172rpx;
  height: 172rpx;
  margin: auto;
  text-align: center;
}
.my_info .head .head_image.data-v-7f94e65d {
  width: 116rpx;
  height: 116rpx;
  line-height: 172rpx;
  vertical-align: top;
  margin: 20rpx auto;
  border-radius: 50%;
  background-color: #fff;
}
.my_info .phone_name.data-v-7f94e65d {
  flex: 1;
  margin: auto;
}
.my_info .phone_name .name .name_text.data-v-7f94e65d {
  font-size: 32rpx;
  opacity: 1;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 550;
  text-align: left;
  color: #333333;
  height: 44rpx;
  line-height: 44rpx;
  margin-right: 12rpx;
}
.my_info .phone_name .name .name_type.data-v-7f94e65d {
  width: 32rpx;
  height: 32rpx;
  vertical-align: middle;
  margin-bottom: 6rpx;
}
.my_info .phone_name .phone .phone_text.data-v-7f94e65d {
  height: 40rpx;
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
  line-height: 40rpx;
}

