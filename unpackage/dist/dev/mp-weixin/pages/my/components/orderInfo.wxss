@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.address_order.data-v-664dcc7d {
  width: 710rpx;
  height: 200rpx;
  margin: 20rpx auto;
  margin-top: 0;
}
.address_order .address.data-v-664dcc7d {
  line-height: 100rpx;
  position: relative;
}
.address_order .address .location.data-v-664dcc7d {
  width: 34rpx;
  height: 36rpx;
  margin-right: 8rpx;
  vertical-align: middle;
  margin-bottom: 4rpx;
  padding-left: 30rpx;
}
.address_order .address .address_word.data-v-664dcc7d {
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: center;
  color: #333333;
  line-height: 40rpx;
}
.address_order .address .to_right.data-v-664dcc7d {
  width: 30rpx;
  height: 30rpx;
  vertical-align: middle;
  margin-bottom: 10rpx;
  position: absolute;
  top: 50%;
  right: 20rpx;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.address_order .order.data-v-664dcc7d {
  line-height: 100rpx;
  position: relative;
  border-top: 1px dashed #ebebeb;
  margin-left: 30rpx;
  margin-right: 20rpx;
}
.address_order .order .location.data-v-664dcc7d {
  width: 34rpx;
  height: 36rpx;
  margin-right: 8rpx;
  vertical-align: middle;
  margin-bottom: 4rpx;
}
.address_order .order .order_word.data-v-664dcc7d {
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: center;
  color: #333333;
  line-height: 40rpx;
}
.address_order .order .to_right.data-v-664dcc7d {
  width: 30rpx;
  height: 30rpx;
  vertical-align: middle;
  color: #fff;
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.recent.data-v-664dcc7d {
  height: 60rpx;
  padding: 0 16rpx 0 22rpx;
}
.recent .order_line.data-v-664dcc7d {
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 550;
  text-align: left;
  color: #333333;
  line-height: 60rpx;
  letter-spacing: 0px;
  display: block;
  width: 100%;
  padding-left: 6rpx;
}
.quit.data-v-664dcc7d {
  width: 710rpx;
  height: 100rpx;
  opacity: 1;
  background: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  color: #333333;
  line-height: 100rpx;
  position: fixed;
  bottom: 50rpx;
  left: 20rpx;
}

