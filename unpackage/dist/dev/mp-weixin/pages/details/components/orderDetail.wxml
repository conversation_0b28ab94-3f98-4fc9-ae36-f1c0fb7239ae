<block wx:if="{{$root.g0}}"><view class="box order_list_cont"><view class="order_list"><view class="word_text"><text class="word_style">{{orderDetailsData.shopName}}</text></view><view class="order-type"><block wx:for="{{$root.l0}}" wx:for-item="obj" wx:for-index="index" wx:key="index"><view class="type_item"><view class="dish_img"><image class="dish_img_url" mode="aspectFill" src="{{obj.$orig.image}}"></image></view><view class="dish_info"><view class="dish_name">{{obj.$orig.name}}</view><view class="dish_dishFlavor">{{obj.$orig.dishFlavor?obj.$orig.dishFlavor:""}}</view><view class="dish_price">×<block wx:if="{{obj.$orig.number&&obj.$orig.number>0}}"><text class="dish_number">{{obj.$orig.number}}</text></block></view><view class="dish_active"><text>￥</text>{{''+obj.g1+''}}</view></view></view></block><view class="iconUp"><block wx:if="{{$root.g2>2}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e"><text>{{!showDisplay?"展开更多":"点击收起"}}</text><image class="{{['icon_img',showDisplay?'icon_imgDown':'']}}" src="../../../static/toRight.png" mode></image></view></block></view><view class="orderList"><view class="orderInfo"><text class="text">打包费</text><text class="may">￥</text>{{''+orderDetailsData.packAmount+''}}</view><view class="orderInfo"><text class="text">配送费</text><text class="may">￥</text>{{''+orderDetailsData.deliveryFee+''}}</view><view class="totalMoney">合计<text class="text"><text>￥</text>{{''+orderDetailsData.amount+''}}</text></view></view></view></view></view></block>