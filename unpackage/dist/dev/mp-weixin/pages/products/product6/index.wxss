@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.product-container.data-v-46720de8 {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}
.order-button-container.data-v-46720de8 {
  width: 100%;
  height: 33.33vh;
  /* 占据屏幕高度的三分之一 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}
.order-button-container .order-button.data-v-46720de8 {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: #fff;
  font-size: 72rpx;
  font-weight: bold;
  border-radius: 40rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 60rpx rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
  letter-spacing: 8rpx;
}
.order-button-container .order-button.data-v-46720de8:active {
  -webkit-transform: translateY(6rpx) scale(0.98);
          transform: translateY(6rpx) scale(0.98);
  box-shadow: 0 15rpx 45rpx rgba(255, 107, 53, 0.6);
}
.order-button-container .order-button.data-v-46720de8:hover {
  -webkit-transform: translateY(-3rpx);
          transform: translateY(-3rpx);
  box-shadow: 0 25rpx 70rpx rgba(255, 107, 53, 0.5);
}

