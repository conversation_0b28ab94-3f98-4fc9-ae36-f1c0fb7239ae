@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.product-container.data-v-ed97a3f0 {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}
.product-showcase.data-v-ed97a3f0 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 60rpx;
  text-align: center;
}
.product-showcase .product-title.data-v-ed97a3f0 {
  font-size: 64rpx;
  font-weight: 300;
  color: #1e293b;
  margin-bottom: 24rpx;
  letter-spacing: 2rpx;
}
.product-showcase .product-subtitle.data-v-ed97a3f0 {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 400;
  line-height: 1.6;
}
.order-section.data-v-ed97a3f0 {
  padding: 60rpx;
  background: #fff;
  border-top: 1rpx solid #e2e8f0;
}
.order-section .order-button.data-v-ed97a3f0 {
  width: 100%;
  height: 120rpx;
  background: #0f172a;
  color: #fff;
  font-size: 36rpx;
  font-weight: 600;
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  letter-spacing: 1rpx;
}
.order-section .order-button.data-v-ed97a3f0:active {
  background: #334155;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}

