{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?a4e4", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?52c5", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?480e", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?72ba", "uni-app:///uni_modules/uni-transition/components/uni-transition/uni-transition.vue"], "names": ["name", "emits", "props", "show", "type", "default", "modeClass", "duration", "styles", "customClass", "onceRender", "data", "isShow", "transform", "opacity", "animationData", "durationTime", "config", "watch", "handler", "immediate", "computed", "stylesObject", "transformStyles", "created", "timingFunction", "transform<PERSON><PERSON>in", "delay", "methods", "init", "onClick", "detail", "step", "console", "run", "open", "clearTimeout", "close", "styleInit", "buildStyle", "tranfromInit", "aniNum", "buildTranfrom", "animationType", "fade", "animationMode", "toLine"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;;;AAG7D;AACsN;AACtN,gBAAgB,iNAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA02B,CAAgB,4yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACU93B;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,gBAgBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAf;MACAgB;QACA;UACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA,6CACA;QACA;MAAA,EACA;MACA;MACA;QACA;QACAT;MACA;MACA;IACA;IACA;IACAU;MACA;IACA;EACA;EACAC;IACA;IACA;MACAjB;MACAkB;MACAC;MACAC;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;UACA;YAAA;YACA;UACA;YACA;UACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACAC;MACA;MACA;MACA;QAAAtB;QAAAD;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;YACAkB;UACA;QACA;MACA;IACA;IACA;IACAM;MAAA;MACA;MACA,wBACAL,OACAE;QACA;QACA;QACA;QACA;UAAApB;UAAAD;QACA;QACA;QACA;UACAkB;QACA;MACA;IACA;IACA;IACAO;MAAA;MACA;QACAzB;MACA;MACA;QACA;UACAL;QACA;UACAA;QACA;MACA;MACA;QACA+B;MACA;QACA;UACAA;QACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACAC;QACA;UACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;QACA;MACA;MACA;QACAC;MACA;QACA;UACAA;QACA;MACA;MAEA;IACA;IACAC;MACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAD;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAE;MACA;IACA;EACA;AACA;AAAA,4B", "file": "uni_modules/uni-transition/components/uni-transition/uni-transition.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-transition.vue?vue&type=template&id=6369f8c4&\"\nvar renderjs\nimport script from \"./uni-transition.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-transition.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-transition/components/uni-transition/uni-transition.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=template&id=6369f8c4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=script&lang=js&\"", "<template>\n  <!-- #ifndef APP-NVUE -->\n  <view v-show=\"isShow\" ref=\"ani\" :animation=\"animationData\" :class=\"customClass\" :style=\"transformStyles\" @click=\"onClick\"><slot></slot></view>\n  <!-- #endif -->\n  <!-- #ifdef APP-NVUE -->\n  <view v-if=\"isShow\" ref=\"ani\" :animation=\"animationData\" :class=\"customClass\" :style=\"transformStyles\" @click=\"onClick\"><slot></slot></view>\n  <!-- #endif -->\n</template>\n\n<script>\nimport { createAnimation } from './createAnimation'\n\n/**\n * Transition 过渡动画\n * @description 简单过渡动画组件\n * @tutorial https://ext.dcloud.net.cn/plugin?id=985\n * @property {Boolean} show = [false|true] 控制组件显示或隐藏\n * @property {Array|String} modeClass = [fade|slide-top|slide-right|slide-bottom|slide-left|zoom-in|zoom-out] 过渡动画类型\n *  @value fade 渐隐渐出过渡\n *  @value slide-top 由上至下过渡\n *  @value slide-right 由右至左过渡\n *  @value slide-bottom 由下至上过渡\n *  @value slide-left 由左至右过渡\n *  @value zoom-in 由小到大过渡\n *  @value zoom-out 由大到小过渡\n * @property {Number} duration 过渡动画持续时间\n * @property {Object} styles 组件样式，同 css 样式，注意带’-‘连接符的属性需要使用小驼峰写法如：`backgroundColor:red`\n */\nexport default {\n\tname: 'uniTransition',\n\temits:['click','change'],\n\tprops: {\n\t\tshow: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tmodeClass: {\n\t\t\ttype: [Array, String],\n\t\t\tdefault() {\n\t\t\t\treturn 'fade'\n\t\t\t}\n\t\t},\n\t\tduration: {\n\t\t\ttype: Number,\n\t\t\tdefault: 300\n\t\t},\n\t\tstyles: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {}\n\t\t\t}\n\t\t},\n\t\tcustomClass:{\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tonceRender:{\n\t\t\ttype:Boolean,\n\t\t\tdefault:false\n\t\t},\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tisShow: false,\n\t\t\ttransform: '',\n\t\t\topacity: 1,\n\t\t\tanimationData: {},\n\t\t\tdurationTime: 300,\n\t\t\tconfig: {}\n\t\t}\n\t},\n\twatch: {\n\t\tshow: {\n\t\t\thandler(newVal) {\n\t\t\t\tif (newVal) {\n\t\t\t\t\tthis.open()\n\t\t\t\t} else {\n\t\t\t\t\t// 避免上来就执行 close,导致动画错乱\n\t\t\t\t\tif (this.isShow) {\n\t\t\t\t\t\tthis.close()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 生成样式数据\n\t\tstylesObject() {\n\t\t\tlet styles = {\n\t\t\t\t...this.styles,\n\t\t\t\t'transition-duration': this.duration / 1000 + 's'\n\t\t\t}\n\t\t\tlet transform = ''\n\t\t\tfor (let i in styles) {\n\t\t\t\tlet line = this.toLine(i)\n\t\t\t\ttransform += line + ':' + styles[i] + ';'\n\t\t\t}\n\t\t\treturn transform\n\t\t},\n\t\t// 初始化动画条件\n\t\ttransformStyles() {\n\t\t\treturn 'transform:' + this.transform + ';' + 'opacity:' + this.opacity + ';' + this.stylesObject\n\t\t}\n\t},\n\tcreated() {\n\t\t// 动画默认配置\n\t\tthis.config = {\n\t\t\tduration: this.duration,\n\t\t\ttimingFunction: 'ease',\n\t\t\ttransformOrigin: '50% 50%',\n\t\t\tdelay: 0\n\t\t}\n\t\tthis.durationTime = this.duration\n\t},\n\tmethods: {\n\t\t/**\n\t\t *  ref 触发 初始化动画\n\t\t */\n\t\tinit(obj = {}) {\n\t\t\tif (obj.duration) {\n\t\t\t\tthis.durationTime = obj.duration\n\t\t\t}\n\t\t\tthis.animation = createAnimation(Object.assign(this.config, obj),this)\n\t\t},\n\t\t/**\n\t\t * 点击组件触发回调\n\t\t */\n\t\tonClick() {\n\t\t\tthis.$emit('click', {\n\t\t\t\tdetail: this.isShow\n\t\t\t})\n\t\t},\n\t\t/**\n\t\t * ref 触发 动画分组\n\t\t * @param {Object} obj\n\t\t */\n\t\tstep(obj, config = {}) {\n\t\t\tif (!this.animation) return\n\t\t\tfor (let i in obj) {\n\t\t\t\ttry {\n\t\t\t\t\tif(typeof obj[i] === 'object'){\n\t\t\t\t\t\tthis.animation[i](...obj[i])\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.animation[i](obj[i])\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(`方法 ${i} 不存在`)\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.animation.step(config)\n\t\t\treturn this\n\t\t},\n\t\t/**\n\t\t *  ref 触发 执行动画\n\t\t */\n\t\trun(fn) {\n\t\t\tif (!this.animation) return\n\t\t\tthis.animation.run(fn)\n\t\t},\n\t\t// 开始过度动画\n\t\topen() {\n\t\t\tclearTimeout(this.timer)\n\t\t\tthis.transform = ''\n\t\t\tthis.isShow = true\n\t\t\tlet { opacity, transform } = this.styleInit(false)\n\t\t\tif (typeof opacity !== 'undefined') {\n\t\t\t\tthis.opacity = opacity\n\t\t\t}\n\t\t\tthis.transform = transform\n\t\t\t// 确保动态样式已经生效后，执行动画，如果不加 nextTick ，会导致 wx 动画执行异常\n\t\t\tthis.$nextTick(() => {\n\t\t\t\t// TODO 定时器保证动画完全执行，目前有些问题，后面会取消定时器\n\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\tthis.animation = createAnimation(this.config, this)\n\t\t\t\t\tthis.tranfromInit(false).step()\n\t\t\t\t\tthis.animation.run()\n\t\t\t\t\tthis.$emit('change', {\n\t\t\t\t\t\tdetail: this.isShow\n\t\t\t\t\t})\n\t\t\t\t}, 20)\n\t\t\t})\n\t\t},\n\t\t// 关闭过度动画\n\t\tclose(type) {\n\t\t\tif (!this.animation) return\n\t\t\tthis.tranfromInit(true)\n\t\t\t\t.step()\n\t\t\t\t.run(() => {\n\t\t\t\t\tthis.isShow = false\n\t\t\t\t\tthis.animationData = null\n\t\t\t\t\tthis.animation = null\n\t\t\t\t\tlet { opacity, transform } = this.styleInit(false)\n\t\t\t\t\tthis.opacity = opacity || 1\n\t\t\t\t\tthis.transform = transform\n\t\t\t\t\tthis.$emit('change', {\n\t\t\t\t\t\tdetail: this.isShow\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t},\n\t\t// 处理动画开始前的默认样式\n\t\tstyleInit(type) {\n\t\t\tlet styles = {\n\t\t\t\ttransform: ''\n\t\t\t}\n\t\t\tlet buildStyle = (type, mode) => {\n\t\t\t\tif (mode === 'fade') {\n\t\t\t\t\tstyles.opacity = this.animationType(type)[mode]\n\t\t\t\t} else {\n\t\t\t\t\tstyles.transform += this.animationType(type)[mode] + ' '\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (typeof this.modeClass === 'string') {\n\t\t\t\tbuildStyle(type, this.modeClass)\n\t\t\t} else {\n\t\t\t\tthis.modeClass.forEach(mode => {\n\t\t\t\t\tbuildStyle(type, mode)\n\t\t\t\t})\n\t\t\t}\n\t\t\treturn styles\n\t\t},\n\t\t// 处理内置组合动画\n\t\ttranfromInit(type) {\n\t\t\tlet buildTranfrom = (type, mode) => {\n\t\t\t\tlet aniNum = null\n\t\t\t\tif (mode === 'fade') {\n\t\t\t\t\taniNum = type ? 0 : 1\n\t\t\t\t} else {\n\t\t\t\t\taniNum = type ? '-100%' : '0'\n\t\t\t\t\tif (mode === 'zoom-in') {\n\t\t\t\t\t\taniNum = type ? 0.8 : 1\n\t\t\t\t\t}\n\t\t\t\t\tif (mode === 'zoom-out') {\n\t\t\t\t\t\taniNum = type ? 1.2 : 1\n\t\t\t\t\t}\n\t\t\t\t\tif (mode === 'slide-right') {\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\n\t\t\t\t\t}\n\t\t\t\t\tif (mode === 'slide-bottom') {\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.animation[this.animationMode()[mode]](aniNum)\n\t\t\t}\n\t\t\tif (typeof this.modeClass === 'string') {\n\t\t\t\tbuildTranfrom(type, this.modeClass)\n\t\t\t} else {\n\t\t\t\tthis.modeClass.forEach(mode => {\n\t\t\t\t\tbuildTranfrom(type, mode)\n\t\t\t\t})\n\t\t\t}\n\n\t\t\treturn this.animation\n\t\t},\n\t\tanimationType(type) {\n\t\t\treturn {\n\t\t\t\tfade: type ? 1 : 0,\n\t\t\t\t'slide-top': `translateY(${type ? '0' : '-100%'})`,\n\t\t\t\t'slide-right': `translateX(${type ? '0' : '100%'})`,\n\t\t\t\t'slide-bottom': `translateY(${type ? '0' : '100%'})`,\n\t\t\t\t'slide-left': `translateX(${type ? '0' : '-100%'})`,\n\t\t\t\t'zoom-in': `scaleX(${type ? 1 : 0.8}) scaleY(${type ? 1 : 0.8})`,\n\t\t\t\t'zoom-out': `scaleX(${type ? 1 : 1.2}) scaleY(${type ? 1 : 1.2})`\n\t\t\t}\n\t\t},\n\t\t// 内置动画类型与实际动画对应字典\n\t\tanimationMode() {\n\t\t\treturn {\n\t\t\t\tfade: 'opacity',\n\t\t\t\t'slide-top': 'translateY',\n\t\t\t\t'slide-right': 'translateX',\n\t\t\t\t'slide-bottom': 'translateY',\n\t\t\t\t'slide-left': 'translateX',\n\t\t\t\t'zoom-in': 'scale',\n\t\t\t\t'zoom-out': 'scale'\n\t\t\t}\n\t\t},\n\t\t// 驼峰转中横线\n\t\ttoLine(name) {\n\t\t\treturn name.replace(/([A-Z])/g, '-$1').toLowerCase()\n\t\t}\n\t}\n}\n</script>\n\n<style></style>\n"], "sourceRoot": ""}