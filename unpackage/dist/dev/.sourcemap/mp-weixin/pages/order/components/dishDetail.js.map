{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/dishDetail.vue?9d89", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/dishDetail.vue?9d5d", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/dishDetail.vue?c691", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/dishDetail.vue?1772", "uni-app:///pages/order/components/dishDetail.vue"], "names": ["props", "orderDataes", "type", "default", "showDisplay", "orderListDataes", "orderDishNumber", "orderDishPrice", "computed", "methods", "deliveryFee"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAAu1B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACgD32B;AAAA;AAAA;AAAA,gBACA;EACA;EACAA;IACA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QAAA;MAAA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK,4BACA,uCACA;EACAC;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B", "file": "pages/order/components/dishDetail.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dishDetail.vue?vue&type=template&id=2983afba&scoped=true&\"\nvar renderjs\nimport script from \"./dishDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./dishDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./../style.scss?vue&type=style&index=0&id=2983afba&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2983afba\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/components/dishDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=template&id=2983afba&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.orderDataes, function (obj, index) {\n    var $orig = _vm.__get_orig(obj)\n    var g0 = obj.amount.toFixed(2)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var g1 = _vm.orderListDataes.length\n  var m0 = _vm.deliveryFee()\n  var g2 = _vm.orderDishPrice.toFixed(2)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showDisplay = !_vm.showDisplay\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        m0: m0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=script&lang=js&\"", "<!--菜品信息-->\n<template>\n  <view class=\"box order_list\">\n    <view class=\"word_text\">\n      <text class=\"word_style\">{{ shopInfo.shopName }}</text>\n    </view>\n    <view class=\"order-type\">\n      <view class=\"type_item\" v-for=\"(obj, index) in orderDataes\" :key=\"index\">\n        <view class=\"dish_img\">\n          <image mode=\"aspectFill\" :src=\"obj.image\" class=\"dish_img_url\"></image>\n        </view>\n        <view class=\"dish_info\">\n          <view class=\"dish_name\"> {{ obj.name }} </view>\n          <view class=\"dish_dishFlavor\" v-if=\"obj.dishFlavor\">\n            {{ obj.dishFlavor }}\n          </view>\n          <view class=\"dish_price\">×\n            <text v-if=\"obj.number && obj.number > 0\" class=\"dish_number\">{{\n              obj.number\n            }}</text>\n          </view>\n          <view class=\"dish_active\">\n            <text>￥</text> {{ obj.amount.toFixed(2) }}\n          </view>\n        </view>\n      </view>\n      <view class=\"iconUp\">\n        <view @click=\"showDisplay = !showDisplay\" v-if=\"orderListDataes.length > 3\">\n          <text>{{ !showDisplay ? \"展开更多\" : \"点击收起\" }}</text>\n          <image class=\"icon_img\" :class=\"showDisplay ? 'icon_imgDown' : ''\" src=\"../../../static/toRight.png\" mode=\"\">\n          </image>\n        </view>\n      </view>\n      <view class=\"orderList\">\n        <view class=\"orderInfo\">\n          <text class=\"text\">打包费</text><text class=\"may\">￥</text>{{ orderDishNumber }}\n        </view>\n        <view class=\"orderInfo\">\n          <text class=\"text\">配送费</text><text class=\"may\">￥</text>{{ deliveryFee() }}\n        </view>\n        <view class=\"totalMoney\">\n          合计<text class=\"text\"><text>￥</text>{{ orderDishPrice.toFixed(2) }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nimport { mapState } from \"vuex\";\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 菜品数据\n    orderDataes: {\n      type: Array,\n      default: () => [],\n    },\n    // 是否显示更多收起\n    showDisplay: {\n      type: Boolean,\n      default: false,\n    },\n    // 菜品数据\n    orderListDataes: {\n      type: Array,\n      default: () => [],\n    },\n    // 打包费\n    orderDishNumber: {\n      type: Number,\n      default: 0,\n    },\n    // 合计总钱数\n    orderDishPrice: {\n      type: Number,\n      default: 0,\n    },\n  },\n  computed: {\n    ...mapState([\"0\", \"shopInfo\"]),\n  },\n  methods: {\n    // 安全获取配送费，确保返回有效数字\n    deliveryFee() {\n      const fee = this.$store.state.deliveryFee\n      if (fee === null || fee === undefined || isNaN(fee)) {\n        return 0\n      }\n      return Number(fee)\n    },\n  },\n};\n</script>\n<style src=\"./../style.scss\" lang=\"scss\" scoped></style>"], "sourceRoot": ""}