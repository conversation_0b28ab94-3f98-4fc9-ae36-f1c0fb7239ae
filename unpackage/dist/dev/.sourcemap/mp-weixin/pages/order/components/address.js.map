{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/address.vue?f7c6", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/address.vue?d86a", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/address.vue?5a9f", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/order/components/address.vue?8467", "uni-app:///pages/order/components/address.vue"], "names": ["props", "tagLabel", "type", "default", "addressLabel", "address", "nick<PERSON><PERSON>", "gender", "phoneNumber", "arrivalTime", "tabIndex", "selectValue", "popleft", "weeks", "newDateData", "methods", "go<PERSON>dd<PERSON>", "openTimePopuo", "change", "dateChange", "timeClick", "val", "i", "onsuer", "computed", "cryptoName"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACwC;;;AAG9F;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAo1B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCgGx2B;EACA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;QAAA;MAAA;IACA;IACA;IACAU;MACAX;MACAC;QAAA;MAAA;IACA;IACA;IACAW;MACAZ;MACAC;QAAA;MAAA;IACA;EACA;EACAY;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAAC;QAAAC;MAAA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B", "file": "pages/order/components/address.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./address.vue?vue&type=template&id=04873fd5&scoped=true&\"\nvar renderjs\nimport script from \"./address.vue?vue&type=script&lang=js&\"\nexport * from \"./address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./../style.scss?vue&type=style&index=0&id=04873fd5&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"04873fd5\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/components/address.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=template&id=04873fd5&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=script&lang=js&\"", "<!--地址-->\n<template>\n  <view class=\"container new_address\">\n    <!-- 地址 -->\n    <view class=\"top\" @click=\"goAddress\">\n      <!-- 无地址 -->\n      <view v-if=\"!address\" class=\"address_name_disabled\">\n        请选择收货地址\n      </view>\n      <!-- end -->\n      <!-- 有地址 -->\n      <view v-else class=\"address_name\">\n        <view class=\"address\">\n          <text class=\"tag\" :class=\"'tag' + tagLabel\">{{ addressLabel }}</text>\n          <text class=\"word\">{{ address }}</text>\n        </view>\n        <view class=\"name\">\n          <text class=\"name_1\">{{ cryptoName }}</text>\n          <text class=\"name_2\">{{ phoneNumber }}</text>\n        </view>\n        <view v-if=\"address\" class=\"infoTip\"\n          >为减少接触，降低风险，推荐使用无接触配送</view\n        >\n      </view>\n      <view class=\"address_image\">\n        <view class=\"to_right\"></view>\n      </view>\n      <!-- end -->\n    </view>\n    <!-- 送达时间 -->\n    <view class=\"bottom\">\n      <div class=\"bottomTime\" @click=\"openTimePopuo('bottom')\">\n        <text class=\"time_name_disabled\">立即送出</text>\n        <view class=\"address_image\">\n          <text class=\"\">{{ arrivalTime }}送达</text>\n          <view class=\"to_right\"></view>\n        </view>\n      </div>\n\n      <view v-if=\"address\" class=\"infoTip\"\n        >因配送订单较多，送达时间可能波动</view\n      >\n    </view>\n    <!-- end -->\n    <!-- 时间弹层 -->\n    <uni-popup ref=\"timePopup\" @change=\"change\" class=\"popupBox\">\n      <view class=\"popup-content\">\n        <view class=\"pickerCon\">\n          <view class=\"dayBox\">\n            <scroll-view\n              scroll-x=\"true\"\n              :scroll-into-view=\"scrollinto\"\n              :scroll-with-animation=\"true\"\n            >\n              <view\n                v-for=\"(item, index) in popleft\"\n                :key=\"index\"\n                :id=\"'tab' + index\"\n                class=\"scroll-row-item\"\n                @click=\"dateChange(index)\"\n              >\n                <view v-for=\"(val, i) in weeks\" :key=\"i\">\n                  <view\n                    :class=\"tabIndex == index ? 'scroll-row-day' : ''\"\n                    v-if=\"index === i\"\n                    ><text class=\"line\"></text>{{ item\n                    }}<text class=\"week\">({{ val }})</text></view\n                  >\n                </view>\n              </view>\n            </scroll-view>\n          </view>\n          <view class=\"timeBox\">\n            <scroll-view\n              class=\"card_order_list\"\n              scroll-y=\"true\"\n              scroll-top=\"40rpx\"\n            >\n              <view\n                v-for=\"(val, i) in newDateData\"\n                :key=\"i\"\n                class=\"item\"\n                :class=\"selectValue === i ? 'city-column_select' : ''\"\n                @click=\"timeClick(val, i)\"\n                >{{ val }}</view\n              >\n            </scroll-view>\n          </view>\n        </view>\n        <view class=\"btns\" @click=\"onsuer\">取消</view>\n      </view>\n    </uni-popup>\n    <!-- end -->\n  </view>\n</template>\n<script>\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 是公司还是家里样式\n    tagLabel: {\n      type: String,\n      default: \"\",\n    },\n    // 是公司还是家里\n    addressLabel: {\n      type: String,\n      default: \"\",\n    },\n    // 地址\n    address: {\n      type: String,\n      default: \"\",\n    },\n    // 名称\n    nickName: {\n      type: String,\n      default: \"\",\n    },\n    gender: {\n      type: Number,\n      default: -1,\n    },\n    // 电话\n    phoneNumber: {\n      type: String,\n      default: \"\",\n    },\n    // 送达时间\n    arrivalTime: {\n      type: String,\n      default: \"\",\n    },\n    // 当前选中\n    tabIndex: {\n      type: Number,\n      default: 0,\n    },\n    // 当前选中的时间样式\n    selectValue: {\n      type: Number,\n      default: 0,\n    },\n    // 时间选中的左侧数据（今天、明天）\n    popleft: {\n      type: Array,\n      default: () => [],\n    },\n    // 周几\n    weeks: {\n      type: Array,\n      default: () => [],\n    },\n    // 时间段\n    newDateData: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  methods: {\n    // 地址选择\n    goAddress() {\n      this.$emit(\"goAddress\");\n    },\n    // 送达时间弹层\n    openTimePopuo(type) {\n      this.$refs.timePopup.open(type);\n    },\n    //\n    change() {\n      this.$emit(\"change\");\n    },\n    // 星期几选择\n    dateChange(index) {\n      this.$emit(\"dateChange\", index);\n    },\n    // 选中时间段\n    timeClick(val, i) {\n      this.$emit(\"timeClick\", { val: val, i: i });\n      this.onsuer();\n    },\n    // 取消时间选择\n    onsuer(type) {\n      this.$refs.timePopup.close(type);\n    },\n  },\n  computed: {\n    // 万先生\n    cryptoName() {\n      if (this.$store.state.gender === 0) {\n        // 男\n        return this.nickName.charAt(0) + \"先生\";\n      } else {\n        // 女\n        return this.nickName.charAt(0) + \"女士\";\n      }\n    },\n  },\n};\n</script>\n<style src=\"./../style.scss\" lang=\"scss\" scoped></style>"], "sourceRoot": ""}