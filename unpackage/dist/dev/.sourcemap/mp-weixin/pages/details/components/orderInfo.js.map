{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderInfo.vue?9502", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderInfo.vue?224e", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderInfo.vue?c8bc", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderInfo.vue?a99b", "uni-app:///pages/details/components/orderInfo.vue"], "names": ["props", "orderDetailsData", "type", "default"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACqB;;;AAG7E;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCkC12B;EACA;EACAA;IACA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;EACA;AACA;AAAA,4B", "file": "pages/details/components/orderInfo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./orderInfo.vue?vue&type=template&id=5a38d3ee&\"\nvar renderjs\nimport script from \"./orderInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./orderInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"../../order/style.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/details/components/orderInfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderInfo.vue?vue&type=template&id=5a38d3ee&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderInfo.vue?vue&type=script&lang=js&\"", "<!--订单信息-->\n<template>\n  <view class=\"box\">\n    <view class=\"orderBaseInfo\">\n      <view>\n        <view>订单号码</view>\n        <view>{{ orderDetailsData.number }}</view>\n      </view>\n      <view>\n        <view>订单时间</view>\n        <view>{{ orderDetailsData.orderTime }}</view>\n      </view>\n      <view>\n        <view>支付方式</view>\n        <view>{{ orderDetailsData.payMethod === 1 ? \"微信\" : \"支付宝\" }}</view>\n      </view>\n      <view>\n        <view>订单备注</view>\n        <view class=\"orderinfo-remak\">{{\n          orderDetailsData.remark ? orderDetailsData.remark : \"暂无信息\"\n        }}</view>\n      </view>\n      <view>\n        <view>餐具数量</view>\n        <view>{{\n          orderDetailsData.tablewareStatus === 1\n            ? orderDetailsData.tablewareNumber\n            : orderDetailsData.tablewareNumber\n        }}</view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 订单详情\n    orderDetailsData: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n};\n</script>\n<style src=\"../../order/style.scss\" lang=\"scss\"></style>"], "sourceRoot": ""}