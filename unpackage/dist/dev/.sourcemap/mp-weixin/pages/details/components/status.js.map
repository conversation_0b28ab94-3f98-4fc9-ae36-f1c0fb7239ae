{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/status.vue?bd44", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/status.vue?d3de", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/status.vue?e047", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/status.vue?bded", "uni-app:///pages/details/components/status.vue"], "names": ["props", "orderDetailsData", "type", "default", "timeout", "rocallTime", "methods", "statusWord", "handleCancel", "obj", "handlePay", "handleReminder", "id", "handleRefund", "oneMoreOrder"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACwB;;;AAG7E;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAm1B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2Dv2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACA;EACAA;IACA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QAAAN;QAAAO;MAAA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAAT;QAAAU;MAAA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B", "file": "pages/details/components/status.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./status.vue?vue&type=template&id=ce7f4d56&\"\nvar renderjs\nimport script from \"./status.vue?vue&type=script&lang=js&\"\nexport * from \"./status.vue?vue&type=script&lang=js&\"\nimport style0 from \"../../order/style.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/details/components/status.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./status.vue?vue&type=template&id=ce7f4d56&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.statusWord(_vm.orderDetailsData.status)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./status.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./status.vue?vue&type=script&lang=js&\"", "<!-- 订单状态 -->\n<template>\n  <view>\n    <view class=\"box\">\n      <view class=\"orderInfoTip\">\n        <view class=\"tit\">{{ statusWord(orderDetailsData.status) }} <text class=\"smw\"\n            v-if=\"timeout && orderDetailsData.status === 1\"> ( 已经超时)</text></view>\n        <view class=\"rejectionReason\" v-if=\"orderDetailsData.status === 7\">\n          <text v-if=\"orderDetailsData.payStatus === 1 || orderDetailsData.payStatus === 2\">退款成功</text>\n          <text v-else-if=\"orderDetailsData.cancelReason\">{{ orderDetailsData.cancelReason }}</text>\n          <text v-else-if=\"orderDetailsData.rejectionReason\">{{ orderDetailsData.rejectionReason }}</text>\n        </view>\n        <view v-if=\"!timeout && orderDetailsData.status === 1\">\n          <view class=\"time\">\n            <view class=\"timeIcon\"></view>\n            等待支付：\n            <text>{{ rocallTime }}</text>\n            <text>{{ paymentTime }}</text>\n          </view>\n        </view>\n        <view class=\"againBtn\">\n          <button class=\"new_btn\" type=\"default\" @click=\"handleCancel('center', orderDetailsData)\" v-if=\"(!timeout && orderDetailsData.status === 1) ||\n            orderDetailsData.status === 2 ||\n            orderDetailsData.status === 3 ||\n            orderDetailsData.status === 4\n            \">\n            取消订单\n          </button>\n          <button class=\"new_btn btn\" type=\"default\" @click=\"handlePay(orderDetailsData.id)\"\n            v-if=\"!timeout && orderDetailsData.status === 1\">\n            立即支付\n          </button>\n          <button class=\"new_btn btn\" type=\"default\" @click=\"handleReminder('center', orderDetailsData.id)\"\n            v-if=\"orderDetailsData.status === 2\">\n            催单\n          </button>\n          <button class=\"new_btn\" type=\"default\" @click=\"handleRefund('center')\" v-if=\"orderDetailsData.status == 5\">\n            申请退款\n          </button>\n          <button class=\"new_btn\" type=\"default\" @click=\"oneMoreOrder(orderDetailsData.id)\"\n            v-if=\"orderDetailsData.status !== 7\">\n            再来一单\n          </button>\n        </view>\n      </view>\n    </view>\n    <view class=\"box timeTip\" v-if=\"!timeout && orderDetailsData.status === 1\">\n      <view class=\"icon newIcon\"></view>\n      请在15分钟内完成支付，超时将自动取消。\n    </view>\n    <view class=\"box timeTip\" v-if=\"orderDetailsData.status === 6 && orderDetailsData.payStatus === 2\">\n      <view class=\"icon moneyIcon\"></view>\n      您的订单已\n      <text>退款成功</text>\n      。\n    </view>\n  </view>\n</template>\n<script>\nimport { statusWord } from \"@/utils/index\";\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 订单详情\n    orderDetailsData: {\n      type: Object,\n      default: () => ({}),\n    },\n    // 倒计时间\n    timeout: {\n      type: Boolean,\n      default: false,\n    },\n    // 支付时间\n    rocallTime: {\n      type: String,\n      default: \"\",\n    },\n  },\n  methods: {\n    // 地址选择\n    statusWord(status) {\n      this.$emit(\"statusWord\", status);\n      // return errr;\n      return statusWord(status);\n    },\n    //取消订单\n    handleCancel(type, obj) {\n      this.$emit(\"handleCancel\", { type: type, obj: obj });\n    },\n    // 立即支付\n    handlePay(id) {\n      this.$emit(\"handlePay\", id);\n    },\n    // 催单\n    handleReminder(type, id) {\n      this.$emit(\"handleReminder\", { type: type, id: id });\n    },\n    // 申请退款\n    handleRefund(type) {\n      this.$emit(\"handleRefund\", type);\n    },\n    // 再来一单\n    oneMoreOrder(id) {\n      this.$emit(\"oneMoreOrder\", id);\n    },\n  },\n};\n</script>\n<style src=\"../../order/style.scss\" lang=\"scss\"></style>"], "sourceRoot": ""}