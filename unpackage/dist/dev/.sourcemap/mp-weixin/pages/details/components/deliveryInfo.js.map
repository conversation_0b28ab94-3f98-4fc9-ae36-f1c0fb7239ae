{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/deliveryInfo.vue?6938", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/deliveryInfo.vue?53a1", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/deliveryInfo.vue?6de7", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/deliveryInfo.vue?d826", "uni-app:///pages/details/components/deliveryInfo.vue"], "names": ["props", "orderDetailsData", "type", "default", "computed", "cryptoName"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACkB;;;AAG7E;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0B72B;EACA;EACAA;IACA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B", "file": "pages/details/components/deliveryInfo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./deliveryInfo.vue?vue&type=template&id=53219036&\"\nvar renderjs\nimport script from \"./deliveryInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./deliveryInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"../../order/style.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/details/components/deliveryInfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deliveryInfo.vue?vue&type=template&id=53219036&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deliveryInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deliveryInfo.vue?vue&type=script&lang=js&\"", "<!--配送信息-->\n<template>\n  <view class=\"box\">\n    <view class=\"orderBaseInfo\">\n      <view>\n        <view>期望时间</view>\n        <view>{{\n          orderDetailsData.deliveryStatus === 1\n          ? \"立即送出\"\n          : orderDetailsData.estimatedDeliveryTime\n        }}</view>\n      </view>\n      <view>\n        <view>配送地址</view>\n        <view>\n          <view class=\"nameInfo\">\n            <text>{{ cryptoName }}</text>\n            {{ orderDetailsData.phone }}\n          </view>\n          <view>{{ orderDetailsData.address }}</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 订单详情\n    orderDetailsData: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  computed: {\n    // 处理姓名 如x先生\n    cryptoName() {\n      if (!this.orderDetailsData.consignee) return \"\";\n      if (this.orderDetailsData.sex == 0) {\n        // 男\n        return this.orderDetailsData.consignee.charAt(0) + \"先生\";\n      } else {\n        // 女\n        return this.orderDetailsData.consignee.charAt(0) + \"女士\";\n      }\n    },\n  },\n};\n</script>\n<style src=\"../../order/style.scss\" lang=\"scss\"></style>"], "sourceRoot": ""}