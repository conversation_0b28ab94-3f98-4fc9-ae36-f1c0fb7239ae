{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderDetail.vue?73d8", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderDetail.vue?6118", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderDetail.vue?b996", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/details/components/orderDetail.vue?8d4e", "uni-app:///pages/details/components/orderDetail.vue"], "names": ["props", "orderDataes", "type", "default", "orderDetailsData", "showDisplay"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACmB;;;AAG7E;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAAw1B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0D52B;EACA;EACAA;IACA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACA;IACAC;MACAF;MACAC;QAAA;MAAA;IACA;IACA;IACAE;MACAH;MACAC;IACA;EACA;AACA;AAAA,4B", "file": "pages/details/components/orderDetail.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./orderDetail.vue?vue&type=template&id=4b8d4928&\"\nvar renderjs\nimport script from \"./orderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"../../order/style.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/details/components/orderDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=template&id=4b8d4928&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = Object.keys(_vm.orderDetailsData).length\n  var l0 = g0\n    ? _vm.__map(_vm.orderDataes, function (obj, index) {\n        var $orig = _vm.__get_orig(obj)\n        var g1 = obj.amount.toFixed(2)\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  var g2 = g0 ? _vm.orderDetailsData.orderDetailList.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showDisplay = !_vm.showDisplay\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=script&lang=js&\"", "<!-- 菜品列表 -->\n<template>\n  <view class=\"box order_list_cont\" v-if=\"Object.keys(orderDetailsData).length\">\n    <view class=\"order_list\">\n      <view class=\"word_text\"><text class=\"word_style\">{{ orderDetailsData.shopName }}</text></view>\n      <view class=\"order-type\">\n        <view class=\"type_item\" v-for=\"(obj, index) in orderDataes\" :key=\"index\">\n          <view class=\"dish_img\">\n            <image mode=\"aspectFill\" :src=\"obj.image\" class=\"dish_img_url\"></image>\n          </view>\n          <view class=\"dish_info\">\n            <view class=\"dish_name\">{{ obj.name }}</view>\n            <view class=\"dish_dishFlavor\">{{\n              obj.dishFlavor ? obj.dishFlavor : \"\"\n            }}</view>\n            <view class=\"dish_price\">\n              ×\n              <text v-if=\"obj.number && obj.number > 0\" class=\"dish_number\">{{\n                obj.number\n              }}</text>\n            </view>\n            <view class=\"dish_active\">\n              <text>￥</text>\n              {{ obj.amount.toFixed(2) }}\n            </view>\n          </view>\n        </view>\n        <view class=\"iconUp\">\n          <view @click=\"showDisplay = !showDisplay\" v-if=\"orderDetailsData.orderDetailList.length > 2\">\n            <text>{{ !showDisplay ? \"展开更多\" : \"点击收起\" }}</text>\n            <image class=\"icon_img\" :class=\"showDisplay ? 'icon_imgDown' : ''\" src=\"../../../static/toRight.png\" mode=\"\">\n            </image>\n          </view>\n        </view>\n        <view class=\"orderList\">\n          <view class=\"orderInfo\">\n            <text class=\"text\">打包费</text>\n            <text class=\"may\">￥</text>\n            {{ orderDetailsData.packAmount }}\n          </view>\n          <view class=\"orderInfo\">\n            <text class=\"text\">配送费</text>\n            <text class=\"may\">￥</text>\n            {{ orderDetailsData.deliveryFee }}\n          </view>\n          <view class=\"totalMoney\">\n            合计\n            <text class=\"text\">\n              <text>￥</text>\n              {{ orderDetailsData.amount }}\n            </text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 菜品数据\n    orderDataes: {\n      type: Array,\n      default: () => [],\n    },\n    // 订单信息\n    orderDetailsData: {\n      type: Object,\n      default: () => ({}),\n    },\n    // 是否显示展开收起\n    showDisplay: {\n      type: Boolean,\n      default: false,\n    },\n  },\n};\n</script>\n<style src=\"../../order/style.scss\" lang=\"scss\"></style>"], "sourceRoot": ""}