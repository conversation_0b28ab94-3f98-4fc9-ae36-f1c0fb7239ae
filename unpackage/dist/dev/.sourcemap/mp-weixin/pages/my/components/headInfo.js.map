{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/my/components/headInfo.vue?38bc", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/my/components/headInfo.vue?7861", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/my/components/headInfo.vue?8479", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/my/components/headInfo.vue?3ecd", "uni-app:///pages/my/components/headInfo.vue", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/my/components/headInfo.vue?081f", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/my/components/headInfo.vue?5299"], "names": ["props", "psersonUrl", "type", "default", "nick<PERSON><PERSON>", "gender", "phoneNumber", "getPhoneNum"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+Bz2B;EACA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAwlD,CAAgB,67CAAG,EAAC,C;;;;;;;;;;;ACA5mD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/components/headInfo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./headInfo.vue?vue&type=template&id=7f94e65d&scoped=true&\"\nvar renderjs\nimport script from \"./headInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./headInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./headInfo.vue?vue&type=style&index=0&id=7f94e65d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7f94e65d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/components/headInfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./headInfo.vue?vue&type=template&id=7f94e65d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var f0 = _vm._f(\"getPhoneNum\")(_vm.phoneNumber)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        f0: f0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./headInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./headInfo.vue?vue&type=script&lang=js&\"", "<!--我的头部-->\n<template>\n    <view class=\"my_info\">\n        <!-- 头像部分 -->\n        <view class=\"head\">\n          <image class=\"head_image\" :src=\"psersonUrl\"></image>\n        </view>\n        <!-- 姓名及手机号 -->\n        <view class=\"phone_name\">\n          <!-- 姓名 -->\n          <view class=\"name\">\n            <text class=\"name_text\">{{ nickName }}</text>\n            <image\n              v-if=\"gender === 2\"\n              class=\"name_type\"\n              src=\"../../../static/girl.png\"\n            ></image>\n            <image\n              v-if=\"gender === 1\"\n              class=\"name_type\"\n              src=\"../../../static/boy.png\"\n            ></image>\n          </view>\n          <!-- 电话号 -->\n          <view class=\"phone\">\n            <text class=\"phone_text\">{{ phoneNumber | getPhoneNum }}</text>\n          </view>\n        </view>\n      </view>\n</template>\n<script>\nexport default {\n  // 获取父级传的数据\n  props: {\n    // 头像\n    psersonUrl: {\n      type: String,\n      default: '',\n    },\n    // 姓名\n    nickName: {\n      type: String,\n      default: '',\n    },\n    // 性别\n    gender: {\n      type: String,\n      default: '',\n    },\n    // 电话\n    phoneNumber: {\n      type: String,\n      default: '',\n    },\n    // 电话\n    getPhoneNum: {\n      type: String,\n      default: '',\n    }\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.my_info {\n    height: 172rpx;\n    width: 750rpx;\n    background-color: #ffc200;\n    display: flex;\n    // 头像\n    .head {\n      width: 172rpx;\n      height: 172rpx;\n      margin: auto;\n      text-align: center;\n      .head_image {\n        width: 116rpx;\n        height: 116rpx;\n        line-height: 172rpx;\n        vertical-align: top;\n        margin: 20rpx auto;\n        border-radius: 50%;\n        background-color: #fff;\n      }\n    }\n    // 姓名电话号\n    .phone_name {\n      flex: 1;\n      margin: auto;\n      .name {\n        .name_text {\n          font-size: 32rpx;\n          opacity: 1;\n          font-family: PingFangSC, PingFangSC-Medium;\n          font-weight: 550;\n          text-align: left;\n          color: #333333;\n          height: 44rpx;\n          line-height: 44rpx;\n          margin-right: 12rpx;\n        }\n\n        .name_type {\n          width: 32rpx;\n          height: 32rpx;\n          vertical-align: middle;\n          margin-bottom: 6rpx;\n        }\n      }\n      .phone {\n        .phone_text {\n          height: 40rpx;\n          opacity: 1;\n          font-size: 28rpx;\n          font-family: PingFangSC, PingFangSC-Regular;\n          font-weight: 400;\n          text-align: left;\n          color: #333333;\n          line-height: 40rpx;\n        }\n      }\n    }\n  }\n</style>", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./headInfo.vue?vue&type=style&index=0&id=7f94e65d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./headInfo.vue?vue&type=style&index=0&id=7f94e65d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753498768939\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}