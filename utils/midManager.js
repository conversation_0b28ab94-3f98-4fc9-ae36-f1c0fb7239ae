/**
 * Mid参数管理工具
 * 用于在整个应用中持久化保存和获取mid参数
 */

const MID_STORAGE_KEY = 'persistentMid'

export const midManager = {
  /**
   * 设置mid参数
   * @param {string|number} mid - mid参数值
   */
  setMid(mid) {
    if (mid) {
      try {
        uni.setStorageSync(MID_STORAGE_KEY, mid.toString())
        console.log('Mid参数已保存:', mid)
      } catch (error) {
        console.error('保存Mid参数失败:', error)
      }
    }
  },

  /**
   * 获取mid参数
   * @returns {string|null} - 返回保存的mid参数，如果没有则返回null
   */
  getMid() {
    try {
      const mid = uni.getStorageSync(MID_STORAGE_KEY)
      return mid || null
    } catch (error) {
      console.error('获取Mid参数失败:', error)
      return null
    }
  },

  /**
   * 清除mid参数
   */
  clearMid() {
    try {
      uni.removeStorageSync(MID_STORAGE_KEY)
      console.log('Mid参数已清除')
    } catch (error) {
      console.error('清除Mid参数失败:', error)
    }
  },

  /**
   * 检查是否存在mid参数
   * @returns {boolean} - 如果存在mid参数返回true，否则返回false
   */
  hasMid() {
    return !!this.getMid()
  },

  /**
   * 为URL添加mid参数
   * @param {string} url - 原始URL
   * @returns {string} - 添加了mid参数的URL
   */
  addMidToUrl(url) {
    const mid = this.getMid()
    if (!mid) {
      return url
    }

    const separator = url.includes('?') ? '&' : '?'
    return `${url}${separator}mid=${mid}`
  },

  /**
   * 跳转到指定页面并自动添加mid参数
   * @param {string} url - 目标页面URL
   * @param {object} options - 跳转选项
   */
  navigateTo(url, options = {}) {
    const urlWithMid = this.addMidToUrl(url)
    uni.navigateTo({
      url: urlWithMid,
      ...options
    })
  },

  /**
   * 重定向到指定页面并强制刷新，确保新参数生效
   * @param {string} url - 目标页面URL
   * @param {object} options - 重定向选项
   */
  navigateToWithRefresh(url, options = {}) {
    const urlWithMid = this.addMidToUrl(url)
    // 使用 reLaunch 来确保页面完全重新加载
    uni.reLaunch({
      url: urlWithMid,
      ...options
    })
  },

  /**
   * 重定向到指定页面并自动添加mid参数
   * @param {string} url - 目标页面URL
   * @param {object} options - 重定向选项
   */
  redirectTo(url, options = {}) {
    const urlWithMid = this.addMidToUrl(url)
    uni.redirectTo({
      url: urlWithMid,
      ...options
    })
  }
}

export default midManager
