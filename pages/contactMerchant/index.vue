<template>
  <view class="container">
    <view class="content">
      <text class="title">请联系所对应商家</text>
    </view>
    <button class="back-btn" @click="navigateBack">返回</button>
  </view>
</template>

<script>
export default {
  methods: {
    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;
}

.content {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  letter-spacing: 2rpx;
}

.back-btn {
  width: 200rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666666;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>