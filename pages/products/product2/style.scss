.product-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.product-showcase {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 60rpx;
  text-align: center;

  .product-title {
    font-size: 64rpx;
    font-weight: 300;
    color: #1e293b;
    margin-bottom: 24rpx;
    letter-spacing: 2rpx;
  }

  .product-subtitle {
    font-size: 28rpx;
    color: #64748b;
    font-weight: 400;
    line-height: 1.6;
  }
}

.order-section {
  padding: 60rpx;
  background: #fff;
  border-top: 1rpx solid #e2e8f0;

  .order-button {
    width: 100%;
    height: 120rpx;
    background: #0f172a;
    color: #fff;
    font-size: 36rpx;
    font-weight: 600;
    border-radius: 16rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    letter-spacing: 1rpx;

    &:active {
      background: #334155;
      transform: scale(0.98);
    }
  }
}